<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wftk</groupId>
        <artifactId>wftk-starters</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>wftk-http-client-spring-boot</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>wftk-http-client-spring-boot-autoconfigure</module>
        <module>wftk-http-client-spring-boot-starter</module>
        <module>wftk-http-client-okhttp-spring-boot-starter</module>
        <module>wftk-http-client</module>
    </modules>
    <properties>
        <okhttp.version>4.10.0</okhttp.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.wftk</groupId>
                <artifactId>wftk-jackson-spring-boot-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


</project>