package com.wftk.http.spring.boot.autoconfigure;


import com.wftk.http.client.core.client.HttpRequestClient;
import com.wftk.http.client.core.common.context.DefaultHttpContext;
import com.wftk.http.client.core.common.context.HttpContext;
import com.wftk.http.client.core.common.decoder.Decoder;
import com.wftk.http.client.core.common.decoder.DefaultDecoder;
import com.wftk.http.client.core.common.encoder.DefaultEncoder;
import com.wftk.http.client.core.common.encoder.Encoder;
import com.wftk.http.client.core.common.setting.TimeoutSetting;
import com.wftk.http.client.core.executor.DefaultHttpRequestExecutor;
import com.wftk.http.client.core.executor.HttpRequestExecutor;
import com.wftk.http.client.ext.DefaultRequestClient;
import com.wftk.http.client.ext.HttpClientRequestClient;
import com.wftk.http.client.ext.OkHttpRequestClient;
import com.wftk.http.spring.boot.autoconfigure.deserializer.JacksonDeserializer;
import com.wftk.http.spring.boot.autoconfigure.properties.HttpProperties;
import com.wftk.http.spring.boot.autoconfigure.properties.PoolProperties;
import com.wftk.http.spring.boot.autoconfigure.serializer.JacksonSerializer;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.io.HttpClientConnectionManager;
import org.apache.hc.client5.http.socket.ConnectionSocketFactory;
import org.apache.hc.client5.http.socket.PlainConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.core5.http.config.Registry;
import org.apache.hc.core5.http.config.RegistryBuilder;
import org.apache.hc.core5.pool.PoolConcurrencyPolicy;
import org.apache.hc.core5.pool.PoolReusePolicy;
import org.apache.hc.core5.util.TimeValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @create 2021/11/19 13:43
 **/
@Configuration
@EnableConfigurationProperties(HttpProperties.class)
public class HttpClientAutoConfiguration {


    /**
     * HttpClient
     */
    @ConditionalOnProperty(
            value = {"config.http.httpclient.enabled"},
            havingValue = "true",
            matchIfMissing = true
    )
    @Configuration
    @ConditionalOnClass(CloseableHttpClient.class)
    public static class HttpClientConfiguration {

        public static final String CONNECTION_MANAGER = "requestClientConnectionManager";

        @ConditionalOnProperty(
                value = {"config.http.pool.enabled"},
                matchIfMissing = true,
                havingValue = "true"
        )
        @Bean
        @ConditionalOnMissingBean
        Registry<ConnectionSocketFactory> socketFactoryRegistry() {
            return RegistryBuilder.<ConnectionSocketFactory>create()
                    .register("http", PlainConnectionSocketFactory.getSocketFactory())
                    .register("https", SSLConnectionSocketFactory.getSocketFactory())
                    .build();
        }


        //5.2.x版本使用注释的版本
//        @ConditionalOnProperty(
//                value = {"config.http.pool.enabled"},
//                matchIfMissing = true,
//                havingValue = "true"
//        )
//        @Bean(name = CONNECTION_MANAGER)
//        @ConditionalOnMissingBean(PoolingHttpClientConnectionManager.class)
//        public HttpClientConnectionManager poolingHttpClientConnectionManager(HttpProperties httpProperties,
//                                                                              ConnectionConfig connectionConfig,
//                                                                              @Autowired(required = false)Registry<ConnectionSocketFactory> connectionSocketFactoryRegistry){
//            final PoolProperties pool = httpProperties.getPool();
//            final PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(
//                    connectionSocketFactoryRegistry, PoolConcurrencyPolicy.LAX, PoolReusePolicy.LIFO, TimeValue.ofMilliseconds(pool.getKeepAliveInMills()),
//                    null, null, null);
//            connectionManager.setMaxTotal(pool.getMaxConnections());
//            connectionManager.setDefaultMaxPerRoute(pool.getMaxPerRouteConnections());
//            connectionManager.setDefaultConnectionConfig(connectionConfig);
//            return connectionManager;
//        }
//
//
//        @Bean
//        @Primary
//        ConnectionConfig connectionConfig(HttpProperties httpProperties) {
//            return ConnectionConfig.custom()
//                    .setConnectTimeout(httpProperties.getConnectTimeoutMills(), TimeUnit.MILLISECONDS)
//                    .setSocketTimeout(httpProperties.getReadTimeoutMills(), TimeUnit.MILLISECONDS)
//                    .build();
//        }
//
//        @Bean
//        @Primary
//        RequestConfig requestConfig(HttpProperties httpProperties) {
//            return RequestConfig.custom()
//                    .setConnectionRequestTimeout(httpProperties.getPool().getHttpClient().getConnectionRequestTimeoutMills(), TimeUnit.MILLISECONDS)
//                    .build();
//        }




        //5.2.x以下版本
        @ConditionalOnProperty(
                value = {"config.http.pool.enabled"},
                matchIfMissing = true,
                havingValue = "true"
        )
        @Bean(name = CONNECTION_MANAGER)
        @ConditionalOnMissingBean(PoolingHttpClientConnectionManager.class)
        public HttpClientConnectionManager poolingHttpClientConnectionManager(HttpProperties httpProperties,
                                                                              @Autowired(required = false)Registry<ConnectionSocketFactory> connectionSocketFactoryRegistry){
            final PoolProperties pool = httpProperties.getPool();
            final PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(
                    connectionSocketFactoryRegistry, PoolConcurrencyPolicy.LAX, PoolReusePolicy.LIFO, TimeValue.ofMilliseconds(pool.getKeepAliveInMills()),
                    null, null, null);
            connectionManager.setMaxTotal(pool.getMaxConnections());
            connectionManager.setDefaultMaxPerRoute(pool.getMaxPerRouteConnections());
            return connectionManager;
        }


        @Bean
        @Primary
        RequestConfig requestConfig(HttpProperties httpProperties) {
            return RequestConfig.custom()
                    .setConnectTimeout(httpProperties.getConnectTimeoutMills(), TimeUnit.MILLISECONDS)
                    .setResponseTimeout(httpProperties.getReadTimeoutMills(), TimeUnit.MILLISECONDS)
                    .setConnectionRequestTimeout(httpProperties.getPool().getHttpClient().getConnectionRequestTimeoutMills(), TimeUnit.MILLISECONDS)
                    .build();
        }



        @Bean
        @ConditionalOnMissingBean(CloseableHttpClient.class)
        public CloseableHttpClient httpClient(HttpProperties httpProperties,
                                              @Autowired(required = false)@Qualifier(CONNECTION_MANAGER) HttpClientConnectionManager httpClientConnectionManager) {
            return HttpClients.custom()
                    .setConnectionManager(httpClientConnectionManager)
                    .setConnectionManagerShared(true)
                    .evictExpiredConnections()
                    .setDefaultRequestConfig(requestConfig(httpProperties))
                    .build();
        }

        @Bean
        @ConditionalOnBean(CloseableHttpClient.class)
        @ConditionalOnMissingBean
        public HttpRequestClient httpRequestClient(CloseableHttpClient httpClient) {
            return new HttpClientRequestClient(httpClient);
        }
    }


    /**
     * OkHttp
     */
    @Configuration
    @ConditionalOnProperty(
            value = {"config.http.okhttp.enabled"},
            havingValue = "true"
    )
    @ConditionalOnClass(OkHttpClient.class)
    public static class OkHttpClientConfiguration {
        @ConditionalOnProperty(
                value = {"config.http.pool.enabled"},
                havingValue = "true"
        )
        @Bean
        @ConditionalOnMissingBean(ConnectionPool.class)
        public ConnectionPool connectionPool(HttpProperties httpProperties){
            final PoolProperties.OkHttpPoolProperties okHttpProperties = httpProperties.getPool().getOkHttp();
            return new ConnectionPool(okHttpProperties.getMaxIdleConnections(), httpProperties.getPool().getKeepAliveInMills(), TimeUnit.MILLISECONDS);
        }


        @Bean
        @ConditionalOnBean(ConnectionPool.class)
        @ConditionalOnMissingBean(OkHttpClient.class)
        public OkHttpClient pooledOkHttpClient(ConnectionPool connectionPool, HttpProperties httpProperties) {
            OkHttpClient.Builder httpClientBuilder = new OkHttpClient.Builder()
                    .connectTimeout(httpProperties.getConnectTimeoutMills(), TimeUnit.MILLISECONDS)
                    .readTimeout(httpProperties.getReadTimeoutMills(), TimeUnit.MILLISECONDS)
                    .callTimeout(httpProperties.getPool().getOkHttp().getCallTimeoutMills(), TimeUnit.MILLISECONDS)
                    .connectionPool(connectionPool);
            OkHttpClient httpClient = httpClientBuilder.build();
            httpClient.dispatcher().setMaxRequests(httpProperties.getPool().getMaxConnections());
            httpClient.dispatcher().setMaxRequestsPerHost(httpProperties.getPool().getMaxPerRouteConnections());
            return httpClient;
        }

        @Bean
        @ConditionalOnMissingBean(OkHttpClient.class)
        public OkHttpClient okHttpClient(HttpProperties httpProperties) {
            OkHttpClient.Builder httpClientBuilder = new OkHttpClient.Builder()
                    .connectTimeout(httpProperties.getConnectTimeoutMills(), TimeUnit.MILLISECONDS)
                    .readTimeout(httpProperties.getReadTimeoutMills(), TimeUnit.MILLISECONDS);
            return httpClientBuilder.build();
        }

        @Bean
        @ConditionalOnBean(OkHttpClient.class)
        @ConditionalOnMissingBean(HttpRequestClient.class)
        public HttpRequestClient httpRequestClient(OkHttpClient okHttpClient) {
            return new OkHttpRequestClient(okHttpClient);
        }

    }


    @Bean
    @ConditionalOnMissingBean(HttpRequestClient.class)
    public HttpRequestClient httpRequestClient() {
        return new DefaultRequestClient();
    }

    @Bean
    @ConditionalOnMissingBean
    public TimeoutSetting timeoutSetting(HttpProperties httpProperties) {
        return new TimeoutSetting(httpProperties.getConnectTimeoutMills(), httpProperties.getReadTimeoutMills());
    }


    @Bean
    @ConditionalOnMissingBean
    public Encoder encoder() {
        return new DefaultEncoder(new JacksonSerializer());
    }

    @Bean
    @ConditionalOnMissingBean
    public Decoder decoder() {
        return new DefaultDecoder(new JacksonDeserializer());
    }


    @Bean
    @ConditionalOnMissingBean
    public HttpContext context(TimeoutSetting timeoutSetting, Encoder encoder, Decoder decoder) {
        return new DefaultHttpContext(timeoutSetting, encoder, decoder);
    }

    @Bean
    @ConditionalOnMissingBean
    public HttpRequestExecutor httpRequestExecutor(HttpContext httpContext, HttpRequestClient httpRequestClient) {
        return new DefaultHttpRequestExecutor(httpContext, httpRequestClient);
    }

}
