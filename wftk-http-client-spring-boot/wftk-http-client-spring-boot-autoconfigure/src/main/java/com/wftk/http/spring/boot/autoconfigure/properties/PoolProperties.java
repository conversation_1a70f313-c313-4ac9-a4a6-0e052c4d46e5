package com.wftk.http.spring.boot.autoconfigure.properties;

import lombok.Data;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

/**
 * <AUTHOR>
 * @create 2021/11/25 18:10
 **/
@Data
public class PoolProperties {

    /**
     * 是否开启连接池
     */
    private Boolean enabled = false;

    /**
     * 连接池最大连接数
     */
    private Integer maxConnections = 100;

    /**
     * 每个domain最大连接数
     */
    private Integer maxPerRouteConnections = 20;

    /**
     * 指定时间后清除
     */
    private Integer keepAliveInMills = 3 * 60 * 1000;

    /**
     * httpClient连接池配置
     */
    @NestedConfigurationProperty
    private HttpClientPoolProperties httpClient = new HttpClientPoolProperties();

    /**
     * okHttp连接池配置
     */
    @NestedConfigurationProperty
    private OkHttpPoolProperties okHttp = new OkHttpPoolProperties();


    @Data
    public static class HttpClientPoolProperties {
        /**
         * 从连接池获取连接的等待超时时间
         */
        private Integer connectionRequestTimeoutMills = 3000;
    }


    @Data
    public static class OkHttpPoolProperties {
        /**
         * 整个流程耗时超时时间设置
         */
        private Integer callTimeoutMills = 6000;

        /**
         * 最大闲置连接数
         */
        private Integer maxIdleConnections = 30;


    }
}
