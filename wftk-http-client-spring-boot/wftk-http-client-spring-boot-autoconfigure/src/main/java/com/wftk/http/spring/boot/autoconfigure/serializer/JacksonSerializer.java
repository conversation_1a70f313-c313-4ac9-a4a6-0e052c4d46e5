package com.wftk.http.spring.boot.autoconfigure.serializer;

import com.wftk.http.client.core.common.encoder.DefaultEncoder;
import com.wftk.jackson.core.JSONObject;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @create 2023/4/10 15:29
 */
public class JacksonSerializer implements DefaultEncoder.JsonSerializer {
    @Override
    public byte[] serialize(Object object) {
        return JSONObject.getInstance().toJSONString(object).getBytes(StandardCharsets.UTF_8);
    }
}
