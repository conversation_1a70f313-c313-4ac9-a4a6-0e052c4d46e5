package com.wftk.http.spring.boot.autoconfigure.deserializer;

import com.wftk.http.client.core.common.decoder.DefaultDecoder;
import com.wftk.jackson.core.JSONObject;

import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @create 2023/4/10 15:30
 */
public class JacksonDeserializer implements DefaultDecoder.JsonDeserializer {
    @Override
    public <T> T deserialize(byte[] bytes, Type type) {
        return JSONObject.getInstance().parseObject(new String(bytes, StandardCharsets.UTF_8), type);
    }
}
