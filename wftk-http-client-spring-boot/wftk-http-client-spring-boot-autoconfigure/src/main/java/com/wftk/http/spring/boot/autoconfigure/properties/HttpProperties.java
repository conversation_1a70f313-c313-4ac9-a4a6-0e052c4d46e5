package com.wftk.http.spring.boot.autoconfigure.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

/**
 * <AUTHOR>
 * @create 2021/11/25 18:05
 **/
@ConfigurationProperties(prefix = HttpProperties.PREFIX)
@Data
public class HttpProperties {

    public static final String PREFIX = "config.http";

    private Integer connectTimeoutMills = 3000;
    private Integer readTimeoutMills = 3000;

    @NestedConfigurationProperty
    private PoolProperties pool = new PoolProperties();
}
