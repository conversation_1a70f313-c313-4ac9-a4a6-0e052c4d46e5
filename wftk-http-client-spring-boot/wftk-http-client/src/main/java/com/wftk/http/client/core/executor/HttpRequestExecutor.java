package com.wftk.http.client.core.executor;

import com.wftk.http.client.core.common.handler.response.ResponseErrorHandler;
import com.wftk.http.client.core.common.interceptor.RequestInterceptor;
import com.wftk.http.client.core.common.interceptor.ResponseBodyInterceptor;
import com.wftk.http.client.core.common.exception.HttpException;
import com.wftk.http.client.core.common.handler.request.RequestExceptionHandler;
import com.wftk.http.client.core.common.interceptor.ResponseInterceptor;
import com.wftk.http.client.core.request.HttpRequest;
import com.wftk.http.client.core.response.HttpResponse;

/**
 * http 请求执行器
 * <AUTHOR>
 * @create 2021/11/19 13:47
 **/
public interface HttpRequestExecutor {

    /**
     * 执行http请求(使用默认配置的拦截器及异常处理器)
     * @param request
     * @return
     */
    default <P, R> R execute(HttpRequest<P, R> request) {
        return execute(request, null, null, null, null, null);
    }

    /**
     * 执行http请求 (可以传入请求异常及响应状态码非200的请求处理器)
     * @param request
     * @param requestExceptionHandler
     * @param responseErrorHandler
     * @param <P>
     * @param <R>
     * @return
     */
    default <P, R> R execute(HttpRequest<P, R> request, RequestExceptionHandler requestExceptionHandler, ResponseErrorHandler responseErrorHandler) {
        return execute(request, requestExceptionHandler, responseErrorHandler, null, null, null);
    }

    /**
     * 执行http请求(可以传入请求前置拦截器及响应解码的前置拦截器)
     * @param request
     * @param requestInterceptor
     * @param responseBodyInterceptor
     * @param responseInterceptor
     * @param <P>
     * @param <R>
     * @return
     */
    default <P, R> R execute(HttpRequest<P, R> request, RequestInterceptor requestInterceptor,
                             ResponseBodyInterceptor responseBodyInterceptor, ResponseInterceptor responseInterceptor) {
        return execute(request, null, null, requestInterceptor, responseBodyInterceptor, responseInterceptor);
    }


    /**
     * 执行http请求(异常处理器及前置拦截器均可传入)
     * @param request
     * @param requestExceptionHandler
     * @param responseErrorHandler
     * @param requestInterceptor
     * @param responseBodyInterceptor
     * @param responseInterceptor
     * @return
     * @param <P>
     * @param <R>
     */
    <P, R> R execute(HttpRequest<P, R> request, RequestExceptionHandler requestExceptionHandler, ResponseErrorHandler responseErrorHandler,
                     RequestInterceptor requestInterceptor, ResponseBodyInterceptor responseBodyInterceptor, ResponseInterceptor responseInterceptor);


    /**
     *
     * @param request
     * @return
     * @param <P>
     * @param <R>
     */
    default <P, R> HttpResponse<R> executeWithResponse(HttpRequest<P, R> request) throws HttpException {
        return executeWithResponse(request, null, null);
    }

    /**
     *
     * @param request
     * @param requestInterceptor
     * @param responseInterceptor
     * @return
     * @param <P>
     * @param <R>
     * @throws Exception
     */
    <P, R> HttpResponse<R> executeWithResponse(HttpRequest<P, R> request, RequestInterceptor requestInterceptor, ResponseInterceptor responseInterceptor) throws HttpException;
}
