package com.wftk.http.client.core.response;

import com.wftk.http.client.core.common.body.HttpBody;
import com.wftk.http.client.core.request.header.HttpHeaders;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2021/11/23 10:26
 **/
@Data
public class DefaultHttpResponse<T> implements HttpResponse<T> {

    private final Integer status;
    private final HttpBody<T> httpBody;

    private final HttpHeaders headers;

    public DefaultHttpResponse(Integer status, HttpBody<T> httpBody, HttpHeaders httpHeaders) {
        this.status = status;
        this.httpBody = httpBody;
        this.headers = httpHeaders;
    }

    @Override
    public Integer status() {
        return status;
    }

    @Override
    public HttpHeaders getHeaders() {
        return headers;
    }

    @Override
    public HttpBody<T> getHttpBody() {
        return httpBody;
    }
}
