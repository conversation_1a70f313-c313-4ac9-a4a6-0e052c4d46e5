package com.wftk.http.client.core.common.exception;

/**
 * <AUTHOR>
 * @create 2021/11/25 14:17
 **/
public class HttpResponseException extends HttpException {

    public HttpResponseException() {
    }

    public HttpResponseException(String message) {
        super(message);
    }

    public HttpResponseException(String message, Throwable cause) {
        super(message, cause);
    }

    public HttpResponseException(Throwable cause) {
        super(cause);
    }

    public HttpResponseException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
