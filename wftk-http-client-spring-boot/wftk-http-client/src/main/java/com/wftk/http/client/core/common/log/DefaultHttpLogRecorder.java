package com.wftk.http.client.core.common.log;

import cn.hutool.core.date.StopWatch;
import com.wftk.http.client.core.common.body.HttpBody;
import com.wftk.http.client.core.request.HttpRequest;
import com.wftk.http.client.core.response.HttpResponse;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.Optional;

/**
 * <AUTHOR>
 * @create 2021/11/25 11:03
 **/
@Slf4j
public class DefaultHttpLogRecorder implements HttpLogRecorder {

    private final StopWatch stopWatch;

    public DefaultHttpLogRecorder() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        this.stopWatch = stopWatch;
    }

    @Override
    public <P, R> void logRequest(HttpRequest<P, R> request, LogLevel logLevel) {
        String pattern = "request: {} {} [{}]";
        String body = getRequestBody(request);
        log.debug("");
        switch (logLevel) {
            case INFO -> log.info(pattern, request.getMethod(), request.getParameterizedUrl(), body);
            case WARN -> log.warn(pattern, request.getMethod(), request.getParameterizedUrl(), body);
            case ERROR -> log.error(pattern, request.getMethod(), request.getParameterizedUrl(), body);
            default -> log.debug(pattern, request.getMethod(), request.getParameterizedUrl(), body);
        }
    }

    @Override
    public <P, R> void logRequest(HttpRequest<P, R> request, Exception exception, LogLevel logLevel) {
        //有异常即结束流程，停止计时
        long spent = stopWatch();
        String pattern = "request: {} {} {}ms";
        log.debug("request headers: {}", request.getHeaders());
        switch (logLevel) {
            case INFO -> log.info(pattern, request.getMethod(), request.getParameterizedUrl(), spent, exception);
            case WARN -> log.warn(pattern, request.getMethod(), request.getParameterizedUrl(), spent, exception);
            case ERROR -> log.error(pattern, request.getMethod(), request.getParameterizedUrl(), spent, exception);
            default -> log.debug(pattern, request.getMethod(), request.getParameterizedUrl(), spent, exception);
        }
    }

    @Override
    public <P, R> void logResponse(HttpRequest<P, R> request, HttpResponse<R> httpResponse, LogLevel logLevel) {
        long spent = stopWatch();
        String body = getRequestBody(request);
        String requestHeaderPattern = "request headers: {}";
        String pattern = "request: {} {} [{}] {} {}ms";
        String responsePattern = "response: [{}]";

        log.info(pattern, request.getMethod(), request.getParameterizedUrl(), body, httpResponse.status(), spent);

        switch (logLevel) {
            case WARN, ERROR -> {
                log.info(requestHeaderPattern, request.getHeaders());
                log.info(responsePattern, httpResponse);
            }
            default -> {
                log.debug(requestHeaderPattern, request.getHeaders());
                log.debug(responsePattern, httpResponse);
            }
        }
    }


    /**
     * 停止计时并获取
     * @return
     */
    private long stopWatch() {
        stopWatch.stop();
        return stopWatch.getTotalTimeMillis();
    }

    /**
     * 获取请求body
     * @param httpRequest
     * @return
     */
    private <P, R> String getRequestBody(HttpRequest<P, R> httpRequest) {
        return Optional.ofNullable(httpRequest.getHttpBody())
                .map(HttpBody::getData)
                .map(it -> new String(it, StandardCharsets.UTF_8))
                .orElse(null);
    }
}
