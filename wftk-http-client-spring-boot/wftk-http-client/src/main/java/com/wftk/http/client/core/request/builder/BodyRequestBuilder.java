package com.wftk.http.client.core.request.builder;

import com.wftk.http.client.core.common.body.BodyDataType;
import com.wftk.http.client.core.common.body.HttpBody;
import com.wftk.http.client.core.request.DefaultHttpRequest;
import com.wftk.http.client.core.request.HttpRequest;
import com.wftk.http.client.core.request.RequestMethod;

/**
 * 包含body的构造器(POST、PUT)
 * <AUTHOR>
 * @create 2021/11/19 16:03
 **/
public class BodyRequestBuilder<P, R> extends BasicRequestBuilder<P, R> {

    private final HttpBody<P> body;

    public BodyRequestBuilder(String url) {
        super(url);
        this.body = new HttpBody<>();
    }

    @Override
    public BodyRequestBuilder<P, R> method(RequestMethod requestMethod) {
        if (requestMethod == RequestMethod.GET || requestMethod == RequestMethod.DELETE) {
            throw new UnsupportedOperationException();
        }
        setRequestMethod(requestMethod);
        return this;
    }

    @Override
    public BodyRequestBuilder<P, R> query(String name, Object value) {
        super.query(name, value);
        return this;
    }

    @Override
    public BodyRequestBuilder<P, R> header(String name, String value) {
        super.header(name, value);
        return this;
    }

    public BodyRequestBuilder<P, R> json(P object) {
        getHeaders().addJsonHeader();
        body.setBody(object);
        body.setDataType(BodyDataType.JSON);
        return this;
    }

    public BodyRequestBuilder<P, R> form(P object) {
        getHeaders().addFormHeader();
        body.setBody(object);
        body.setDataType(BodyDataType.FORM);
        return this;
    }

    @Override
    public HttpRequest<P, R> build() {
        preBuildCheck();
        return new DefaultHttpRequest<>(url, requestMethod, headers, httpQueries, body, resultType);
    }

}
