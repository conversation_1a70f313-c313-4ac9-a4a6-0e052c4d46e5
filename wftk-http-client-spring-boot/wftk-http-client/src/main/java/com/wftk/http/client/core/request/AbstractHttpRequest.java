package com.wftk.http.client.core.request;

import com.wftk.http.client.core.common.body.HttpBody;
import com.wftk.http.client.core.request.header.HttpHeaders;
import com.wftk.http.client.core.request.query.HttpQueries;
import com.wftk.http.client.core.common.type.DataType;

/**
 * <AUTHOR>
 * @create 2021/11/19 14:49
 **/
public abstract class AbstractHttpRequest<P, R>  implements HttpRequest<P, R> {

    private final String url;
    private final RequestMethod requestMethod;
    private final HttpHeaders headers;
    private final HttpQueries httpQueries;
    private final HttpBody<P> httpBody;

    private final DataType<R> resultType;

    public AbstractHttpRequest(String url, RequestMethod requestMethod, HttpHeaders headers, HttpQueries httpQueries, DataType<R> resultType) {
        this(url, requestMethod, headers, httpQueries, null, resultType);
    }

    public AbstractHttpRequest(String url, RequestMethod requestMethod, HttpHeaders headers, HttpQueries httpQueries, HttpBody<P> httpBody, DataType<R> resultType) {
        this.url = url;
        this.requestMethod = requestMethod;
        this.headers = headers == null ? new HttpHeaders() : headers;
        this.httpQueries = httpQueries;
        this.httpBody = httpBody;
        this.resultType = resultType;
    }


    @Override
    public String getUrl() {
        return url;
    }

    @Override
    public RequestMethod getMethod() {
        return requestMethod;
    }

    @Override
    public HttpHeaders getHeaders() {
        return headers;
    }

    @Override
    public HttpQueries getHttpQueries() {
        return httpQueries;
    }

    @Override
    public HttpBody<P> getHttpBody() {
        return httpBody;
    }

    @Override
    public DataType<R> getResultType() {
        return resultType;
    }


}
