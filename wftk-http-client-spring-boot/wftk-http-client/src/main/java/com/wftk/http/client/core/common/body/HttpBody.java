package com.wftk.http.client.core.common.body;

import cn.hutool.core.util.ArrayUtil;

/**
 * <AUTHOR>
 * @create 2021/11/19 14:41
 **/
public class HttpBody<T> {

    /**
     * 请求对象
     * (序列化时将会转换为下面的data)
     */
    private T body;

    /**
     * 反序列化时将会转为上面的body
     */
    private byte[] data;

    private BodyDataType dataType = BodyDataType.JSON;

    public T getBody() {
        return body;
    }

    public void setBody(T body) {
        this.body = body;
    }

    public byte[] getData() {
        return data;
    }

    public void setData(byte[] data) {
        this.data = data;
    }

    public BodyDataType getDataType() {
        return dataType;
    }

    public void setDataType(BodyDataType dataType) {
        this.dataType = dataType;
    }

    @Override
    public String toString() {
        return "HttpBody{" +
                "body=" + (body != null ? body.toString() : null) +
                ", data=" + (ArrayUtil.isNotEmpty(data) ? new String(data) : "[]") +
                '}';
    }
}
