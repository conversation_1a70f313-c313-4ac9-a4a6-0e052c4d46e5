package com.wftk.http.client.core.request.builder;

import com.wftk.http.client.core.request.RequestMethod;
import com.wftk.http.client.core.common.type.DataType;
import com.wftk.http.client.core.request.HttpRequest;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021/11/19 15:45
 **/
public interface RequestBuilder<P, R> {

    /**
     * 设置请求方法
     * @param requestMethod
     * @return
     */
    RequestBuilder<P, R> method(RequestMethod requestMethod);

    /**
     * 设置请求参数
     * @param name
     * @param value
     * @return
     */
    RequestBuilder<P, R> query(String name, Object value);

    /**
     * 设置请求参数
     * @param queries
     * @return
     */
    RequestBuilder<P, R> query(Map<String, Object> queries);

    /**
     * 设置请求头
     * @param name
     * @param value
     * @return
     */
    RequestBuilder<P, R> header(String name, String value);

    /**
     * 设置返回值类型
     * @return
     */
    RequestBuilder<P, R> resultType(DataType<R> resultType);


    /**
     * 构造Request
     * @return
     */
    HttpRequest<P, R> build();
}
