package com.wftk.http.client.core.common.handler.response;

import com.wftk.http.client.core.common.exception.HttpResponseException;
import com.wftk.http.client.core.response.HttpResponse;
import com.wftk.http.client.core.request.HttpRequest;

/**
 * <AUTHOR>
 * @create 2021/11/25 14:18
 **/
public class DefaultResponseErrorHandler implements ResponseErrorHandler {

    @Override
    public <P, R> R handle(HttpRequest<P, R> httpRequest, HttpResponse<R> httpResponse) {
        throw new HttpResponseException("unexpected status: " + httpResponse.status());
    }
}
