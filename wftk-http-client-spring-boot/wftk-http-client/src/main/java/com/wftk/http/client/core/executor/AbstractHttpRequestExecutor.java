package com.wftk.http.client.core.executor;

import com.wftk.http.client.core.common.context.HttpContext;
import com.wftk.http.client.core.common.body.HttpBody;
import com.wftk.http.client.core.common.decoder.Decoder;
import com.wftk.http.client.core.common.encoder.Encoder;
import com.wftk.http.client.core.common.exception.HttpException;
import com.wftk.http.client.core.common.exception.HttpRequestException;
import com.wftk.http.client.core.common.exception.HttpResponseException;
import com.wftk.http.client.core.common.handler.request.RequestExceptionHandler;
import com.wftk.http.client.core.common.handler.response.ResponseErrorHandler;
import com.wftk.http.client.core.common.interceptor.RequestInterceptor;
import com.wftk.http.client.core.common.interceptor.ResponseBodyInterceptor;
import com.wftk.http.client.core.common.interceptor.ResponseInterceptor;
import com.wftk.http.client.core.common.log.HttpLogRecorder;
import com.wftk.http.client.core.common.log.LogLevel;
import com.wftk.http.client.core.request.HttpRequest;
import com.wftk.http.client.core.request.RequestMethod;
import com.wftk.http.client.core.response.HttpResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @create 2021/11/19 13:50
 **/
public abstract class AbstractHttpRequestExecutor implements HttpRequestExecutor {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    protected final HttpContext httpContext;

    public AbstractHttpRequestExecutor(HttpContext httpContext) {
        this.httpContext = httpContext;
    }

    @Override
    public <P, R> R execute(HttpRequest<P, R> request, RequestExceptionHandler requestExceptionHandler,
                            ResponseErrorHandler responseErrorHandler, RequestInterceptor requestInterceptor,
                            ResponseBodyInterceptor responseBodyInterceptor, ResponseInterceptor responseInterceptor) {
        //获取日志记录器(开始计时)
        final HttpLogRecorder httpLogRecorder = httpContext.getLogRecorderFactory().getObject();
        final HttpResponse<R> httpResponse;
        try {
            //解藕给Client执行
            httpResponse = executeWithResponse(request, requestInterceptor, responseInterceptor);
        } catch (Exception e) {
            httpLogRecorder.logRequest(request, e, LogLevel.WARN);

            //异常处理器拦截处理异常
            requestExceptionHandler = getRequestExceptionHandler(requestExceptionHandler, httpContext);
            if (requestExceptionHandler == null) {
                throw new HttpRequestException(e);
            }
            return requestExceptionHandler.handle(request, e);
        }

        if (!httpResponse.statusOk()) {
            httpLogRecorder.logResponse(request, httpResponse, LogLevel.WARN);

            //异常处理器拦截处理异常(状态码非200)
            responseErrorHandler = getResponseErrorHandler(responseErrorHandler, httpContext);
            if (responseErrorHandler == null) {
                throw new HttpResponseException("unexpected status: " + httpResponse.status());
            }
            return responseErrorHandler.handle(request, httpResponse);
        } else {
            httpLogRecorder.logResponse(request, httpResponse, LogLevel.INFO);
        }

        //反序列化
        final Decoder decoder = httpContext.getDecoder();
        logger.debug("use decoder: [{}]", decoder);
        R decodedData = decoder.decode(httpResponse.getHttpBody().getData(), request.getResultType());

        //响应报文拦截器(如果http状态码为200，但是业务code为异常，可以通过此拦截器处理)
        responseBodyInterceptor = getResponseBodyInterceptor(responseBodyInterceptor, httpContext);
        if (responseBodyInterceptor != null) {
            logger.debug("response intercepted by [{}]", responseBodyInterceptor);
            decodedData = responseBodyInterceptor.pre(request, decodedData);
        }
        return decodedData;
    }


    @Override
    public <P, R> HttpResponse<R> executeWithResponse(HttpRequest<P, R> request, RequestInterceptor requestInterceptor,
                                                      ResponseInterceptor responseInterceptor) throws HttpException {
        //请求前置拦截器
        requestInterceptor = getRequestInterceptor(requestInterceptor, httpContext);
        if (requestInterceptor != null) {
            request = requestInterceptor.pre(request);
            logger.info("request pre processed with interceptor [{}].", requestInterceptor);
        }
        if (request.getMethod() == RequestMethod.POST || request.getMethod() == RequestMethod.PUT) {
            HttpBody<P> httpBody = request.getHttpBody();
            //body 序列化
            if (httpBody.getBody() != null) {
                final Encoder encoder = httpContext.getEncoder();
                logger.debug("request body [{}] use encoder [{}]", httpBody.getBody(), encoder);
                byte[] data = encoder.encode(httpBody);
                httpBody.setData(data);
            }
        }
        //解藕给Client执行
        HttpResponse<R> httpResponse = doExecute(request, httpContext);
        //response拦截器
        responseInterceptor = getResponseInterceptor(responseInterceptor, httpContext);
        if (responseInterceptor != null) {
            return responseInterceptor.pre(request, httpResponse);
        }
        return httpResponse;
    }

    /**
     * 执行请求
     * @param request
     * @return
     */
    protected abstract <P, R> HttpResponse<R> doExecute(HttpRequest<P, R> request, HttpContext httpContext) throws HttpException;


    protected RequestExceptionHandler getRequestExceptionHandler(RequestExceptionHandler requestExceptionHandler, HttpContext httpContext) {
        return requestExceptionHandler != null ? requestExceptionHandler : httpContext.getRequestExceptionHandler();
    }

    protected ResponseErrorHandler getResponseErrorHandler(ResponseErrorHandler responseErrorHandler, HttpContext httpContext) {
        return responseErrorHandler != null ? responseErrorHandler : httpContext.getResponseErrorHandler();
    }

    protected RequestInterceptor getRequestInterceptor(RequestInterceptor requestInterceptor, HttpContext httpContext) {
        return requestInterceptor != null ? requestInterceptor : httpContext.getRequestInterceptor();
    }

    protected ResponseBodyInterceptor getResponseBodyInterceptor(ResponseBodyInterceptor responseBodyInterceptor, HttpContext httpContext) {
        return responseBodyInterceptor != null ? responseBodyInterceptor : httpContext.getResponseBodyInterceptor();
    }

    protected ResponseInterceptor getResponseInterceptor(ResponseInterceptor responseInterceptor, HttpContext httpContext) {
        return responseInterceptor != null ? responseInterceptor : httpContext.getResponseInterceptor();
    }
}
