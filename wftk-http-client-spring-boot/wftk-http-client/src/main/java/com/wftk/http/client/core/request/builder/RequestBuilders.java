package com.wftk.http.client.core.request.builder;

public class RequestBuilders {

    /**
     *
     * @param url
     * @param <P>
     * @param <R>
     * @return
     */
    public static <P, R> BodyRequestBuilder<P, R> bodyBuilder(String url) {
        return new BodyRequestBuilder<>(url);
    }

    /**
     *
     * @param url
     * @param <R>
     * @return
     */
    public static <R> BasicRequestBuilder<Object, R> noBodyBuilder(String url) {
        return new BasicRequestBuilder<>(url);
    }
}
