package com.wftk.http.client.core.common.handler.response;

import com.wftk.http.client.core.response.HttpResponse;
import com.wftk.http.client.core.common.handler.ErrorHandler;
import com.wftk.http.client.core.request.HttpRequest;

/**
 * 主要针对状态码非200的情况
 * <AUTHOR>
 * @create 2021/11/22 10:51
 **/
public interface ResponseErrorHandler extends ErrorHandler {

    /**
     * 状态码非200处理
     * @param httpRequest
     * @param httpResponse
     * @param <P>
     * @param <R>
     * @return
     */
    <P, R> R handle(HttpRequest<P, R> httpRequest, HttpResponse<R> httpResponse);
}
