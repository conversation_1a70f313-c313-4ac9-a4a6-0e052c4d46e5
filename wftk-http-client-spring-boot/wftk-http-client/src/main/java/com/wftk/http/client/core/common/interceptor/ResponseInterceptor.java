package com.wftk.http.client.core.common.interceptor;

import com.wftk.http.client.core.response.HttpResponse;
import com.wftk.http.client.core.request.HttpRequest;

/**
 * <AUTHOR>
 * @create 2023/4/6 14:59
 */
public interface ResponseInterceptor {

    /**
     *
     * @param httpRequest
     * @param httpResponse
     * @return
     * @param <P>
     * @param <R>
     */
    <P, R> HttpResponse<R> pre(HttpRequest<P, R> httpRequest, HttpResponse<R> httpResponse);
}
