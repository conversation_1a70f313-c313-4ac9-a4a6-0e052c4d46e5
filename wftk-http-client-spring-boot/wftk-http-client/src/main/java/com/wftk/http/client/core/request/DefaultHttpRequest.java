package com.wftk.http.client.core.request;

import com.wftk.http.client.core.common.body.HttpBody;
import com.wftk.http.client.core.common.type.DataType;
import com.wftk.http.client.core.request.header.HttpHeaders;
import com.wftk.http.client.core.request.query.HttpQueries;


/**
 * <AUTHOR>
 * @create 2021/11/19 16:01
 **/
public class DefaultHttpRequest<P, R> extends AbstractHttpRequest<P, R> {

    public DefaultHttpRequest(String url, RequestMethod requestMethod, HttpHeaders headers, HttpQueries httpQueries, DataType<R> resultType) {
        this(url, requestMethod, headers, httpQueries,  null, resultType);
    }

    public DefaultHttpRequest(String url, RequestMethod requestMethod, HttpHeaders headers, HttpQueries httpQueries, HttpBody<P> httpBody, DataType<R> resultType) {
        super(url, requestMethod, headers, httpQueries, httpBody, resultType);
    }
}
