package com.wftk.http.client.core.common.encoder;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.http.client.core.common.body.BodyDataType;
import com.wftk.http.client.core.common.body.HttpBody;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/4/10 09:40
 */
public abstract class AbstractEncoder implements Encoder {

    @Override
    public <T> byte[] encode(HttpBody<T> httpBody) {
        if (httpBody == null || httpBody.getBody() == null) {
            return null;
        }
        final T object = httpBody.getBody();
        Assert.notNull(object, "object must not be null.");
        if (object instanceof String str) {
            return serializeString(str);
        } else if (object instanceof byte[] bytes) {
            return serializeByteArray(bytes);
        }
        if (httpBody.getDataType() == BodyDataType.FORM) {
            return serializeWithForm(object);
        } else {
            return serializeWithJson(object);
        }
    }

    /**
     * 序列化字符串
     * @param str
     * @return
     */
    protected byte[] serializeString(String str) {
        if (StrUtil.isBlank(str)) {
            return null;
        }
        return str.getBytes(StandardCharsets.UTF_8);
    }


    /**
     * 序列化字节数组
     * @param bytes
     * @return
     */
    protected byte[] serializeByteArray(byte[] bytes) {
        return bytes;
    }


    /**
     * 序列化form
     * @param obj
     * @return
     */
    protected byte[] serializeWithForm(Object obj) {
        Map<String, Object> objectMap = BeanUtil.beanToMap(obj);
        return MapUtil.join(objectMap, "&", "=").getBytes(StandardCharsets.UTF_8);
    }


    /**
     * 序列化JSON
     * @param obj
     * @return
     */
    protected abstract byte[] serializeWithJson(Object obj);

}
