package com.wftk.http.client.core.common.setting;

/**
 * <AUTHOR>
 * @create 2021/11/19 16:32
 **/
public class TimeoutSetting {

    private static final Integer DEFAULT_CONNECT_TIMEOUT = 3000;
    private static final Integer DEFAULT_READ_TIMEOUT = 3000;

    private final Integer connectTimeoutMills;
    private final Integer readTimeoutMills;

    public TimeoutSetting() {
        this(DEFAULT_CONNECT_TIMEOUT, DEFAULT_READ_TIMEOUT);
    }

    public TimeoutSetting(Integer connectTimeoutMills, Integer readTimeoutMills) {
        this.connectTimeoutMills = connectTimeoutMills;
        this.readTimeoutMills = readTimeoutMills;
    }

    public Integer getConnectTimeoutMills() {
        return connectTimeoutMills;
    }

    public Integer getReadTimeoutMills() {
        return readTimeoutMills;
    }
}
