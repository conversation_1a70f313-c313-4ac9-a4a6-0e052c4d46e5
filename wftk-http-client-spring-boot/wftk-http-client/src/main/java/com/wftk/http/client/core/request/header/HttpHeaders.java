package com.wftk.http.client.core.request.header;

import java.util.Optional;
import java.util.TreeMap;

/**
 * http header
 * <AUTHOR>
 * @create 2021/11/19 14:13
 **/
public class HttpHeaders extends TreeMap<String, String> {

    private static final String CONTENT_TYPE = "Content-Type";
    public static final String JSON_HEADER_VALUE = "application/json;charset=UTF-8";
    public static final String FORM_HEADER_VALUE = "application/x-www-form-urlencoded;charset=UTF-8";

    public void addJsonHeader() {
        put("Content-Type", JSON_HEADER_VALUE);
    }

    public void addFormHeader() {
        put("Content-Type", FORM_HEADER_VALUE);
    }

    public Optional<String> getContentType(){
        return Optional.ofNullable(get(CONTENT_TYPE));
    }


}
