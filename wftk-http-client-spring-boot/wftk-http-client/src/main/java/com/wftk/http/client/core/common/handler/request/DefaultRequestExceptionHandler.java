package com.wftk.http.client.core.common.handler.request;

import com.wftk.http.client.core.common.exception.HttpRequestException;
import com.wftk.http.client.core.request.HttpRequest;

/**
 * <AUTHOR>
 * @create 2021/11/25 14:14
 **/
public class DefaultRequestExceptionHandler implements RequestExceptionHandler {

    @Override
    public <P, R> R handle(HttpRequest<P, R> httpRequest, Exception exception) {
        throw new HttpRequestException(exception);
    }
}
