package com.wftk.http.client.core.common.setting.pool;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @create 2021/11/25 17:11
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class OkHttpClientPoolSetting extends PoolSetting {

    /**
     * 整个流程耗时超时时间设置
     */
    private Integer callTimeoutMills;

    /**
     * 最大闲置连接数
     */
    private Integer maxIdleConnections;

    /**
     * 指定时间后清除
     */
    private Integer keepAliveInMills;
}
