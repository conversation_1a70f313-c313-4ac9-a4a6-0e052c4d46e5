package com.wftk.http.client.core.common.decoder;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.TypeUtil;
import com.wftk.http.client.core.common.type.DataType;

import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @create 2023/4/10 10:16
 */
public abstract class AbstractDecoder implements Decoder {

    @Override
    public <R> R decode(byte[] data, DataType<R> dataType) {
        if (ArrayUtil.isEmpty(data)) {
            return null;
        }
        if (isAssignableFrom(byte[].class, dataType.getType())) {
            return (R)data;
        } else if (isAssignableFrom(String.class, dataType.getType())) {
            return (R) decodeWithString(data);
        } else {
            return decodeWithObject(data, dataType.getType());
        }
    }


    /**
     *
     * @param clazz
     * @param type
     * @return
     */
    private boolean isAssignableFrom(Class<?> clazz, Type type) {
        return clazz.isAssignableFrom(TypeUtil.getClass(type));
    }

    protected String decodeWithString(byte[] bytes) {
        return new String(bytes, StandardCharsets.UTF_8);
    }

    protected abstract <R> R decodeWithObject(byte[] bytes, Type type);
}
