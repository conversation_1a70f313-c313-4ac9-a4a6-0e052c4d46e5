package com.wftk.http.client.ext;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharsetUtil;
import com.wftk.http.client.core.common.body.BodyDataType;
import com.wftk.http.client.core.common.body.HttpBody;
import com.wftk.http.client.core.common.context.HttpContext;
import com.wftk.http.client.core.request.header.HttpHeaders;
import com.wftk.http.client.core.response.DefaultHttpResponse;
import com.wftk.http.client.core.response.HttpResponse;
import com.wftk.http.client.core.client.HttpRequestClient;
import com.wftk.http.client.core.request.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.core5.http.*;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.HttpEntities;
import org.apache.hc.core5.http.io.support.ClassicRequestBuilder;


import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021/11/23 17:29
 **/
@Slf4j
public class HttpClientRequestClient implements HttpRequestClient {

    private static final ContentType APPLICATION_FORM_URLENCODED = ContentType.create("application/x-www-form-urlencoded", StandardCharsets.UTF_8);

    private final HttpClient httpClient;

    public HttpClientRequestClient(HttpClient httpClient) {
        this.httpClient = httpClient;
    }


    /**
     *
     * @param httpRequest
     * @param executedRequest
     * @param <P>
     * @param <R>
     * @return
     * @throws IOException
     */
    private <P, R> HttpResponse<R> execute(HttpRequest<P, R> httpRequest, ClassicHttpRequest executedRequest) throws IOException {
        setHeaders(httpRequest, executedRequest);
        return httpClient.execute(executedRequest, response -> {
            HttpBody<R> httpBody = new HttpBody<>();
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String res = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                httpBody.setData(res.getBytes(StandardCharsets.UTF_8));
            }
            int statusCode = response.getCode();
            Header[] allHeaders = response.getHeaders();
            if (allHeaders == null || allHeaders.length < 1) {
                return new DefaultHttpResponse<>(statusCode, httpBody, null);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            for (Header header : allHeaders) {
                httpHeaders.put(header.getName(), header.getValue());
            }
            return new DefaultHttpResponse<>(statusCode, httpBody, httpHeaders);
        });
    }


    /**
     * 设置header
     * @param httpRequest
     * @param executedRequest
     */
    private <P, R> void setHeaders(HttpRequest<P, R> httpRequest, ClassicHttpRequest executedRequest) {
        HttpHeaders headers = httpRequest.getHeaders();
        for (Map.Entry<String, String> next : headers.entrySet()) {
            String key = next.getKey();
            String value = next.getValue();
            executedRequest.addHeader(key, value);
        }
    }

    @Override
    public <P, R> HttpResponse<R> get(HttpRequest<P, R> httpRequest, HttpContext httpContext) throws IOException {
        ClassicHttpRequest httpGet = ClassicRequestBuilder.get(httpRequest.getParameterizedUrl()).build();
        return execute(httpRequest, httpGet);
    }

    @Override
    public <P, R> HttpResponse<R> post(HttpRequest<P, R> httpRequest, HttpContext httpContext) throws IOException {
        ClassicRequestBuilder postBuilder = ClassicRequestBuilder.post(httpRequest.getParameterizedUrl());
        ClassicHttpRequest httpPost = getRequest(httpRequest, postBuilder);
        return execute(httpRequest, httpPost);
    }

    @Override
    public <P, R> HttpResponse<R> put(HttpRequest<P, R> httpRequest, HttpContext httpContext) throws IOException {
        ClassicRequestBuilder putBuilder = ClassicRequestBuilder.put(httpRequest.getParameterizedUrl());
        ClassicHttpRequest httpPut = getRequest(httpRequest, putBuilder);
        return execute(httpRequest, httpPut);
    }

    @Override
    public <P, R> HttpResponse<R> delete(HttpRequest<P, R> httpRequest, HttpContext httpContext) throws IOException {
        ClassicHttpRequest httpDelete = ClassicRequestBuilder.delete(httpRequest.getParameterizedUrl()).build();
        return execute(httpRequest, httpDelete);
    }


    /**
     *
     * @param httpRequest
     * @param builder
     * @return
     * @param <P>
     * @param <R>
     */
    private <P, R> ClassicHttpRequest getRequest(HttpRequest<P, R> httpRequest, ClassicRequestBuilder builder) {
        HttpBody<P> httpBody = httpRequest.getHttpBody();
        if (ArrayUtil.isNotEmpty(httpBody.getData())) {
            ContentType contentType = httpBody.getDataType() == BodyDataType.JSON ? ContentType.APPLICATION_JSON : APPLICATION_FORM_URLENCODED;
            builder.setEntity(HttpEntities.create(httpBody.getData(), contentType));
        }
        return builder.build();
    }

}
