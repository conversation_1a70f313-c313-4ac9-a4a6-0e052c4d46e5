package com.wftk.http.client.core.request;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.http.client.core.common.body.HttpBody;
import com.wftk.http.client.core.common.type.DataType;
import com.wftk.http.client.core.request.header.HttpHeaders;
import com.wftk.http.client.core.request.query.HttpQueries;

import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;

/**
 * <AUTHOR>
 * @create 2021/11/19 13:48
 **/
public interface HttpRequest<P, R> {

    /**
     * 获取请求地址
     * @return
     */
    String getUrl();

    /**
     * 获取已经组装参数后的url
     * @return
     */
    default String getParameterizedUrl() {
        if (CollectionUtil.isEmpty(getHttpQueries())) {
            return getUrl();
        }
        return getUrl() + "?" + getHttpQueries().toString();
    }

    /**
     * 判断是否为https
     * @return
     */
    default boolean isHttps() {
        return StrUtil.isNotBlank(getUrl()) && getUrl().toLowerCase().startsWith("https:");
    }

    /**
     * 获取http method
     * @return
     */
    RequestMethod getMethod();

    /**
     * 获取请求头
     * @return
     */
    HttpHeaders getHeaders();

    /**
     * 获取请求参数
     * @return
     */
    HttpQueries getHttpQueries();

    /**
     * 获取请求body
     * @return
     */
    HttpBody<P> getHttpBody();

    /**
     * 获取返回值类型
     * @return
     */
    DataType<R> getResultType();
}
