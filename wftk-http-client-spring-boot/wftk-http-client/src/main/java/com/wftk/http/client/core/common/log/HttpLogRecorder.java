package com.wftk.http.client.core.common.log;

import com.wftk.http.client.core.request.HttpRequest;
import com.wftk.http.client.core.response.HttpResponse;

/**
 * <AUTHOR>
 * @create 2021/11/25 10:42
 **/
public interface HttpLogRecorder {

    /**
     * 记录请求日志
     * @param request
     * @param logLevel
     */
    <P, R> void logRequest(HttpRequest<P, R> request, LogLevel logLevel);

    /**
     * 记录请求错误日志
     * @param request
     * @param exception
     * @param logLevel
     */
    <P, R> void logRequest(HttpRequest<P, R> request, Exception exception, LogLevel logLevel);

    /**
     * 记录响应日志
     * @param httpRequest
     * @param httpResponse
     * @param logLevel
     * @param <P>
     * @param <R>
     */
    <P, R> void logResponse(HttpRequest<P, R> httpRequest, HttpResponse<R> httpResponse, LogLevel logLevel);

}
