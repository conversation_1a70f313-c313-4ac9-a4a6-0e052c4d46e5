package com.wftk.http.client.ext;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.wftk.http.client.core.common.body.HttpBody;
import com.wftk.http.client.core.common.context.HttpContext;
import com.wftk.http.client.core.request.header.HttpHeaders;
import com.wftk.http.client.core.response.DefaultHttpResponse;
import com.wftk.http.client.core.response.HttpResponse;
import com.wftk.http.client.core.client.HttpRequestClient;
import com.wftk.http.client.core.request.HttpRequest;
import lombok.extern.slf4j.Slf4j;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLSocketFactory;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.List;
import java.util.Map;

/**
 * 基于JDK HttpURLConnection实现
 * <AUTHOR>
 * @create 2021/11/24 14:49
 **/
@Slf4j
public class DefaultRequestClient implements HttpRequestClient {

    private final HostnameVerifier hostnameVerifier;
    private final SSLSocketFactory sslSocketFactory;

    public DefaultRequestClient() {
        this(null, null);
    }

    public DefaultRequestClient(HostnameVerifier hostnameVerifier, SSLSocketFactory sslSocketFactory) {
        this.hostnameVerifier = hostnameVerifier;
        this.sslSocketFactory = sslSocketFactory;
    }

    /**
     * 执行请求
     * @param httpRequest
     * @param httpContext
     * @return
     * @throws IOException
     */
    private <P, R> HttpResponse<R> execute(HttpRequest<P, R> httpRequest, HttpContext httpContext) throws IOException {
        final HttpURLConnection connection = getConnection(httpRequest, httpContext);
        //设置body
        if (httpRequest.getHttpBody() != null && ArrayUtil.isNotEmpty(httpRequest.getHttpBody().getData())) {
            connection.setDoOutput(true);
            try (OutputStream outputStream = connection.getOutputStream()) {
                outputStream.write(httpRequest.getHttpBody().getData());
            }
        } else {
            connection.connect();
        }

        final int status = connection.getResponseCode();
        Map<String, List<String>> headerFields = connection.getHeaderFields();
        InputStream inputStream = null;
        byte[] data;
        try {
            if (status >= 400) {
                inputStream = connection.getErrorStream();
            } else {
                inputStream = connection.getInputStream();
            }
            data = IoUtil.read(inputStream).toByteArray();
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
            closeConnectionQuietly(connection);
        }

        final HttpBody<R> httpBody = new HttpBody<>();
        httpBody.setData(data);
        if (headerFields == null || headerFields.isEmpty()) {
            return new DefaultHttpResponse<>(status, httpBody, null);
        }
        HttpHeaders headers = new HttpHeaders();
        headerFields.forEach((k, v) -> {
            if (StrUtil.isNotBlank(k)) {
                headers.put(k, StrUtil.join(",", v));
            }
        });
        return new DefaultHttpResponse<>(status, httpBody, headers);
    }


    /**
     * 关闭连接
     * @param connection
     */
    private void closeConnectionQuietly(HttpURLConnection connection) {
        try {
            connection.disconnect();
        } catch (Exception e) {
            log.debug("disconnect...", e);
        }
    }

    /**
     * 获取连接
     * @param httpRequest
     * @param httpContext
     * @return
     * @throws IOException
     */
    private <P, R> HttpURLConnection getConnection(HttpRequest<P, R> httpRequest, HttpContext httpContext) throws IOException {
        final URL url = URLUtil.toUrlForHttp(httpRequest.getParameterizedUrl());
        final URLConnection urlConnection = url.openConnection();
        HttpURLConnection httpURLConnection;
        if (urlConnection instanceof HttpsURLConnection httpsURLConnection) {
            //SSL设置
            if (hostnameVerifier != null) {
                httpsURLConnection.setHostnameVerifier(hostnameVerifier);
            }
            if (sslSocketFactory != null) {
                httpsURLConnection.setSSLSocketFactory(sslSocketFactory);
            }
            httpURLConnection = httpsURLConnection;
        } else {
            httpURLConnection = (HttpURLConnection) urlConnection;
        }
        //设置请求方法
        httpURLConnection.setRequestMethod(httpRequest.getMethod().name());
        //设置超时
        httpURLConnection.setConnectTimeout(httpContext.getConnectTimeoutMills());
        httpURLConnection.setReadTimeout(httpContext.getReadTimeoutMills());

        //设置header
        if (httpRequest.getHeaders() != null && !httpRequest.getHeaders().isEmpty()) {
            httpRequest.getHeaders().forEach(httpURLConnection::setRequestProperty);
        }
        return httpURLConnection;
    }

    @Override
    public <P, R> HttpResponse<R> get(HttpRequest<P, R> httpRequest, HttpContext httpContext) throws IOException {
        return execute(httpRequest, httpContext);
    }

    @Override
    public <P, R> HttpResponse<R> post(HttpRequest<P, R> httpRequest, HttpContext httpContext) throws IOException {
        return execute(httpRequest, httpContext);
    }

    @Override
    public <P, R> HttpResponse<R> put(HttpRequest<P, R> httpRequest, HttpContext httpContext) throws IOException {
        return execute(httpRequest, httpContext);
    }

    @Override
    public <P, R> HttpResponse<R> delete(HttpRequest<P, R> httpRequest, HttpContext httpContext) throws IOException {
        return execute(httpRequest, httpContext);
    }
}
