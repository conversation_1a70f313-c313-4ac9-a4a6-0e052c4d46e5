package com.wftk.http.client.core.client;

import com.wftk.http.client.core.common.context.HttpContext;
import com.wftk.http.client.core.response.HttpResponse;
import com.wftk.http.client.core.request.HttpRequest;

import java.io.IOException;

/**
 * <AUTHOR>
 * @create 2021/11/22 17:40
 **/
public interface HttpRequestClient {

    /**
     *
     * @param httpRequest
     * @param httpContext
     * @return
     */
    default <P, R> HttpResponse<R> http(HttpRequest<P, R> httpRequest, HttpContext httpContext) throws IOException {
        return switch (httpRequest.getMethod()) {
            case GET -> get(httpRequest, httpContext);
            case POST -> post(httpRequest, httpContext);
            case PUT -> put(httpRequest, httpContext);
            case DELETE -> delete(httpRequest, httpContext);
        };
    }

    /**
     * get请求
     * @param httpRequest
     * @param httpContext
     * @return
     */
    <P, R> HttpResponse<R> get(HttpRequest<P, R> httpRequest, HttpContext httpContext) throws IOException;

    /**
     * post请求
     * @param httpRequest
     * @param httpContext
     * @return
     */
    <P, R> HttpResponse<R> post(HttpRequest<P, R> httpRequest, HttpContext httpContext) throws IOException;

    /**
     * put请求
     * @param httpRequest
     * @param httpContext
     * @return
     */
    <P, R> HttpResponse<R> put(HttpRequest<P, R> httpRequest, HttpContext httpContext) throws IOException;

    /**
     * delete请求
     * @param httpRequest
     * @param httpContext
     * @return
     */
    <P, R> HttpResponse<R> delete(HttpRequest<P, R> httpRequest, HttpContext httpContext) throws IOException;
}
