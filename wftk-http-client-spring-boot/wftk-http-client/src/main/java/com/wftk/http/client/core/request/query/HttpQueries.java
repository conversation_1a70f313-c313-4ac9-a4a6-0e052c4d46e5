package com.wftk.http.client.core.request.query;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.TreeMap;

/**
 * 请求参数
 * <AUTHOR>
 * @create 2021/11/19 14:27
 **/
public class HttpQueries extends TreeMap<String, Object> {

    @Override
    public String toString() {
        if (MapUtil.isEmpty(this)) {
            return null;
        }
        StringBuilder builder = new StringBuilder();
        for (Map.Entry<String, Object> entry : entrySet()) {
            if (entry.getValue() == null) {
                continue;
            }
            if (entry.getValue() instanceof String v && StrUtil.isBlank(v)) {
                continue;
            }
            String encodedValue = URLEncoder.encode(entry.getValue().toString(), StandardCharsets.UTF_8);
            builder.append(entry.getKey()).append("=").append(encodedValue).append("&");
        }
        builder.deleteCharAt(builder.length() - 1);
        return builder.toString();
    }

}
