package com.wftk.http.client.core.common.context;

import com.wftk.http.client.core.common.decoder.Decoder;
import com.wftk.http.client.core.common.decoder.DefaultDecoder;
import com.wftk.http.client.core.common.encoder.DefaultEncoder;
import com.wftk.http.client.core.common.encoder.Encoder;
import com.wftk.http.client.core.common.handler.request.DefaultRequestExceptionHandler;
import com.wftk.http.client.core.common.handler.request.RequestExceptionHandler;
import com.wftk.http.client.core.common.handler.response.DefaultResponseErrorHandler;
import com.wftk.http.client.core.common.handler.response.ResponseErrorHandler;
import com.wftk.http.client.core.common.interceptor.RequestInterceptor;
import com.wftk.http.client.core.common.interceptor.ResponseBodyInterceptor;
import com.wftk.http.client.core.common.interceptor.ResponseInterceptor;
import com.wftk.http.client.core.common.log.factory.DefaultHttpLogRecorderFactory;
import com.wftk.http.client.core.common.log.factory.HttpLogRecorderFactory;
import com.wftk.http.client.core.common.setting.TimeoutSetting;

/**
 * <AUTHOR>
 * @create 2021/11/19 16:39
 **/
public class DefaultHttpContext implements HttpContext {

    private final TimeoutSetting timeoutSetting;
    private final Encoder encoder;
    private final Decoder decoder;
    private RequestExceptionHandler requestExceptionHandler = new DefaultRequestExceptionHandler();
    private ResponseErrorHandler responseErrorHandler = new DefaultResponseErrorHandler();
    private RequestInterceptor requestInterceptor;
    private ResponseBodyInterceptor responseBodyInterceptor;
    private ResponseInterceptor responseInterceptor;
    private HttpLogRecorderFactory logRecorderFactory = new DefaultHttpLogRecorderFactory();

    public DefaultHttpContext(TimeoutSetting timeoutSetting) {
        this.timeoutSetting = timeoutSetting;
        this.encoder = new DefaultEncoder();
        this.decoder = new DefaultDecoder();
    }

    public DefaultHttpContext(TimeoutSetting timeoutSetting, Encoder encoder, Decoder decoder) {
        this.timeoutSetting = timeoutSetting;
        this.encoder = encoder;
        this.decoder = decoder;
    }

    @Override
    public Integer getConnectTimeoutMills() {
        return timeoutSetting.getConnectTimeoutMills();
    }

    @Override
    public Integer getReadTimeoutMills() {
        return timeoutSetting.getReadTimeoutMills();
    }

    @Override
    public Encoder getEncoder() {
        return encoder;
    }

    @Override
    public Decoder getDecoder() {
        return decoder;
    }

    @Override
    public void setRequestExceptionHandler(RequestExceptionHandler requestExceptionHandler) {
        this.requestExceptionHandler = requestExceptionHandler;
    }

    @Override
    public RequestExceptionHandler getRequestExceptionHandler() {
        return requestExceptionHandler;
    }

    @Override
    public void setResponseErrorHandler(ResponseErrorHandler responseErrorHandler) {
        this.responseErrorHandler = responseErrorHandler;
    }

    @Override
    public ResponseErrorHandler getResponseErrorHandler() {
        return responseErrorHandler;
    }

    @Override
    public void setRequestInterceptor(RequestInterceptor requestInterceptor) {
        this.requestInterceptor = requestInterceptor;
    }

    @Override
    public RequestInterceptor getRequestInterceptor() {
        return requestInterceptor;
    }

    @Override
    public void setResponseBodyInterceptor(ResponseBodyInterceptor responseBodyInterceptor) {
        this.responseBodyInterceptor = responseBodyInterceptor;
    }

    @Override
    public ResponseBodyInterceptor getResponseBodyInterceptor() {
        return responseBodyInterceptor;
    }

    @Override
    public void setResponseInterceptor(ResponseInterceptor responseInterceptor) {
        this.responseInterceptor = responseInterceptor;
    }

    @Override
    public ResponseInterceptor getResponseInterceptor() {
        return responseInterceptor;
    }

    @Override
    public void setLogRecorderFactory(HttpLogRecorderFactory logRecorderFactory) {
        this.logRecorderFactory = logRecorderFactory;
    }

    @Override
    public HttpLogRecorderFactory getLogRecorderFactory() {
        return logRecorderFactory;
    }


}
