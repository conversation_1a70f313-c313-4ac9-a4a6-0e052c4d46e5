package com.wftk.http.client.core.common.decoder;


import cn.hutool.core.util.StrUtil;
import com.wftk.jackson.core.JSONObject;

import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @create 2021/11/22 10:18
 **/
public class DefaultDecoder extends AbstractDecoder {

    private final JsonDeserializer jsonDeserializer;

    public DefaultDecoder() {
        this(new DefaultJsonDeserializer());
    }

    public DefaultDecoder(JsonDeserializer jsonDeserializer) {
        this.jsonDeserializer = jsonDeserializer;
    }

    @Override
    protected <R> R decodeWithObject(byte[] bytes, Type type) {
        return jsonDeserializer.deserialize(bytes, type);
    }


    /**
     * JSON反序列化
     */
    public interface JsonDeserializer {

        <T> T deserialize(byte[] bytes, Type type);
    }


    public static class DefaultJsonDeserializer implements JsonDeserializer {

        @Override
        public <T> T deserialize(byte[] bytes, Type type) {
            String json = new String(bytes, StandardCharsets.UTF_8);
            if (StrUtil.isBlank(json)) {
                return null;
            }
            return JSONObject.getInstance().parseObject(json, type);
        }
    }


}
