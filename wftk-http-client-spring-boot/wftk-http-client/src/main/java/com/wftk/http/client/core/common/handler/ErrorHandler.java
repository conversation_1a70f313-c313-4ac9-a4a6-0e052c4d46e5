package com.wftk.http.client.core.common.handler;

import com.wftk.http.client.core.request.HttpRequest;

/**
 * 异常处理器
 * <AUTHOR>
 * @create 2021/11/22 10:36
 **/
public interface ErrorHandler {

    /**
     * 异常情况处理
     * @param httpRequest
     * @param <P>
     * @param <R>
     * @return
     */
    default <P, R> R handle(HttpRequest<P, R> httpRequest) {
        throw new UnsupportedOperationException();
    };
}
