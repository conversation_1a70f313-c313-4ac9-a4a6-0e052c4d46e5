package com.wftk.http.client.core.common.type;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

public abstract class DataType<R> {

    private final Type type;

    public DataType() {
        this.type = determineType(getClass());
    }

    private Type determineType(Type type) {
        if (type instanceof ParameterizedType) {
            return ((ParameterizedType) type).getActualTypeArguments()[0];
        }
        Type superClass = ((Class<?>)type).getGenericSuperclass();
        return determineType(superClass);
    }

    public Type getType() {
        return type;
    }
}
