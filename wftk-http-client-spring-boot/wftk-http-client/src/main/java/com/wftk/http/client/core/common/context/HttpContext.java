package com.wftk.http.client.core.common.context;

import com.wftk.http.client.core.common.decoder.Decoder;
import com.wftk.http.client.core.common.encoder.Encoder;
import com.wftk.http.client.core.common.handler.request.RequestExceptionHandler;
import com.wftk.http.client.core.common.handler.response.ResponseErrorHandler;
import com.wftk.http.client.core.common.interceptor.RequestInterceptor;
import com.wftk.http.client.core.common.interceptor.ResponseBodyInterceptor;
import com.wftk.http.client.core.common.interceptor.ResponseInterceptor;
import com.wftk.http.client.core.common.log.factory.HttpLogRecorderFactory;

/**
 * <AUTHOR>
 * @create 2021/11/19 16:30
 **/
public interface HttpContext {

    /**
     * 连接超时时间
     * @return
     */
    Integer getConnectTimeoutMills();

    /**
     * 读取超时时间
     * @return
     */
    Integer getReadTimeoutMills();

    /**
     * 编码器
     * @return
     */
    Encoder getEncoder();

    /**
     * 解码器
     * @return
     */
    Decoder getDecoder();

    /**
     * 设置异常处理器
     * @param requestExceptionHandler
     * @return
     */
    void setRequestExceptionHandler(RequestExceptionHandler requestExceptionHandler);

    /**
     * 获取异常处理器
     * @return
     */
    RequestExceptionHandler getRequestExceptionHandler();

    /**
     * 设置响应异常（状态码非200）处理器
     * @param responseErrorHandler
     */
    void setResponseErrorHandler(ResponseErrorHandler responseErrorHandler);

    /**
     * 获取响应异常（状态码非200）处理器
     * @return
     */
    ResponseErrorHandler getResponseErrorHandler();

    /**
     * 设置请求拦截器
     * @param requestInterceptor
     */
    void setRequestInterceptor(RequestInterceptor requestInterceptor);

    /**
     * 获取请求拦截器
     * @return
     */
    RequestInterceptor getRequestInterceptor();

    /**
     * 设置报文响应拦截器
     * @param responseBodyInterceptor
     */
    void setResponseBodyInterceptor(ResponseBodyInterceptor responseBodyInterceptor);

    /**
     * 获取报文响应拦截器
     * @return
     */
    ResponseBodyInterceptor getResponseBodyInterceptor();


    /**
     * 设置响应拦截器
     * @param responseInterceptor
     * @return
     */
    void setResponseInterceptor(ResponseInterceptor responseInterceptor);


    /**
     * 获取相应拦截器
     * @return
     */
    ResponseInterceptor getResponseInterceptor();


    /**
     * 设置日志记录器
     * @param logRecorderFactory
     */
    void setLogRecorderFactory(HttpLogRecorderFactory logRecorderFactory);

    /**
     * 获取日志记录器
     * @return
     */
    HttpLogRecorderFactory getLogRecorderFactory();

}
