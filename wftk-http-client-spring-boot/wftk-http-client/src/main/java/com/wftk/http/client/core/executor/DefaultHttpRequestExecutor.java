package com.wftk.http.client.core.executor;

import com.wftk.http.client.core.common.context.HttpContext;
import com.wftk.http.client.core.client.HttpRequestClient;
import com.wftk.http.client.core.common.exception.HttpException;
import com.wftk.http.client.core.response.HttpResponse;
import com.wftk.http.client.core.request.HttpRequest;

import java.io.IOException;

/**
 * <AUTHOR>
 * @create 2021/11/23 13:47
 **/
public class DefaultHttpRequestExecutor extends AbstractHttpRequestExecutor {

    private final HttpRequestClient httpRequestClient;

    public DefaultHttpRequestExecutor(HttpContext httpContext, HttpRequestClient httpRequestClient) {
        super(httpContext);
        this.httpRequestClient = httpRequestClient;
    }

    @Override
    protected <P, R> HttpResponse<R> doExecute(HttpRequest<P, R> request, HttpContext httpContext) throws HttpException {
        try {
            return httpRequestClient.http(request, httpContext);
        } catch (IOException e) {
            throw new HttpException(e);
        }
    }
}
