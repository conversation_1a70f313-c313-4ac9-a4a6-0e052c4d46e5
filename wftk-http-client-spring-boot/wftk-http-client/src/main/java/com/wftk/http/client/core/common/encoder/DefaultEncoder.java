package com.wftk.http.client.core.common.encoder;

import com.wftk.jackson.core.JSONObject;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @create 2021/11/22 09:37
 **/
public class DefaultEncoder extends AbstractEncoder {

    private final JsonSerializer jsonSerializer;

    public DefaultEncoder() {
        this(new DefaultJsonSerializer());
    }

    public DefaultEncoder(JsonSerializer jsonSerializer) {
        this.jsonSerializer = jsonSerializer;
    }

    @Override
    protected byte[] serializeWithJson(Object obj) {
        return jsonSerializer.serialize(obj);
    }


    /**
     * JSON序列化
     */
    public interface JsonSerializer {

        byte[] serialize(Object object);
    }


    public static class DefaultJsonSerializer implements JsonSerializer {

        @Override
        public byte[] serialize(Object object) {
            if (object == null) {
                return null;
            }
            if (object instanceof String s) {
                return s.getBytes(StandardCharsets.UTF_8);
            }
            return JSONObject.getInstance().toJSONString(object).getBytes(StandardCharsets.UTF_8);
        }
    }
}
