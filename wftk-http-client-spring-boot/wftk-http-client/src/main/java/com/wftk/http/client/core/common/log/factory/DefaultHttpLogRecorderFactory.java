package com.wftk.http.client.core.common.log.factory;

import com.wftk.http.client.core.common.log.DefaultHttpLogRecorder;
import com.wftk.http.client.core.common.log.HttpLogRecorder;

/**
 * <AUTHOR>
 * @create 2021/11/25 14:02
 **/
public class DefaultHttpLogRecorderFactory implements HttpLogRecorderFactory {

    @Override
    public HttpLogRecorder getObject() {
        return new DefaultHttpLogRecorder();
    }
}
