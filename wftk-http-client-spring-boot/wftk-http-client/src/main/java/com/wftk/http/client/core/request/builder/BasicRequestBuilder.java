package com.wftk.http.client.core.request.builder;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.wftk.http.client.core.common.type.DataType;
import com.wftk.http.client.core.request.DefaultHttpRequest;
import com.wftk.http.client.core.request.HttpRequest;
import com.wftk.http.client.core.request.RequestMethod;
import com.wftk.http.client.core.request.header.HttpHeaders;
import com.wftk.http.client.core.request.query.HttpQueries;

import java.util.Map;

/**
 * 不含body的构造器
 * <AUTHOR>
 * @create 2021/11/19 15:48
 **/
public class BasicRequestBuilder<P, R> implements RequestBuilder<P, R> {

    protected final String url;
    protected final HttpHeaders headers;
    protected final HttpQueries httpQueries;
    protected RequestMethod requestMethod;
    protected DataType<R> resultType;

    public BasicRequestBuilder(String url) {
        this.url = url;
        this.headers = new HttpHeaders();
        this.httpQueries = new HttpQueries();
    }

    @Override
    public RequestBuilder<P, R> method(RequestMethod requestMethod) {
        this.requestMethod = requestMethod;
        if (requestMethod == RequestMethod.POST || requestMethod == RequestMethod.PUT) {
            RequestBuilder<P, R> builder = new BodyRequestBuilder<>(url);
            if (!headers.isEmpty()) {
                headers.forEach(builder::header);
            }
            if (!httpQueries.isEmpty()) {
                httpQueries.forEach(builder::query);
            }
            if (resultType != null) {
                builder.resultType(resultType);
            }
            builder.method(requestMethod);
            return builder;
        }
        return this;
    }

    @Override
    public RequestBuilder<P, R> query(String name, Object value) {
        httpQueries.put(name, value);
        return this;
    }

    @Override
    public RequestBuilder<P, R> query(Map<String, Object> queries) {
        if (CollectionUtil.isNotEmpty(queries)) {
            httpQueries.putAll(queries);
        }
        return this;
    }

    @Override
    public RequestBuilder<P, R> header(String name, String value) {
        headers.put(name, value);
        return this;
    }

    @Override
    public RequestBuilder<P, R> resultType(DataType<R> resultType) {
        this.resultType = resultType;
        return this;
    }

    @Override
    public HttpRequest<P, R> build() {
        preBuildCheck();
        return new DefaultHttpRequest<>(url, requestMethod, headers, httpQueries, resultType);
    }

    public HttpHeaders getHeaders() {
        return headers;
    }

    protected void setRequestMethod(RequestMethod requestMethod) {
        this.requestMethod = requestMethod;
    }

    protected void preBuildCheck() {
        Assert.notNull(requestMethod, "request method must not be null.");
        Assert.notNull(resultType, "resultType must not be null.");
    }
}
