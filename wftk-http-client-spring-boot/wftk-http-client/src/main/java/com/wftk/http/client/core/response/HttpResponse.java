package com.wftk.http.client.core.response;

import com.wftk.http.client.core.common.body.HttpBody;
import com.wftk.http.client.core.request.header.HttpHeaders;

/**
 * <AUTHOR>
 * @create 2021/11/19 15:11
 **/
public interface HttpResponse<T> {

    default boolean statusOk() {
        return status() == 200;
    }

    /**
     * 获取http状态码
     * @return
     */
    Integer status();

    /**
     * 获取响应头部
     * @return
     */
    HttpHeaders getHeaders();

    /**
     * 获取body
     * @return
     */
    HttpBody<T> getHttpBody();
}
