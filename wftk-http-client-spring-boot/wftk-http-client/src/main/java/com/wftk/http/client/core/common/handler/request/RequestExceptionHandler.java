package com.wftk.http.client.core.common.handler.request;


import com.wftk.http.client.core.common.handler.ErrorHandler;
import com.wftk.http.client.core.request.HttpRequest;

/**
 * 请求异常处理器(比如各类超时异常及其他)
 * <AUTHOR>
 * @create 2021/11/22 10:42
 **/
public interface RequestExceptionHandler extends ErrorHandler {

    /**
     * 异常处理
     * @param httpRequest
     * @param exception
     * @param <P>
     * @param <R>
     * @return
     */
    <P, R> R handle(HttpRequest<P, R> httpRequest, Exception exception);
}
