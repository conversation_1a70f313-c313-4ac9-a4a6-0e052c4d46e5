package com.wftk.http.client.ext;

import com.wftk.http.client.core.common.body.BodyDataType;
import com.wftk.http.client.core.common.body.HttpBody;
import com.wftk.http.client.core.common.context.HttpContext;
import com.wftk.http.client.core.request.header.HttpHeaders;
import com.wftk.http.client.core.response.DefaultHttpResponse;
import com.wftk.http.client.core.response.HttpResponse;
import com.wftk.http.client.core.client.HttpRequestClient;
import com.wftk.http.client.core.request.HttpRequest;
import okhttp3.*;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2021/11/23 17:30
 **/

public class OkHttpRequestClient implements HttpRequestClient {

    private final OkHttpClient okHttpClient;

    public OkHttpRequestClient(OkHttpClient okHttpClient) {
        this.okHttpClient = okHttpClient;
    }


    /**
     * 创建header
     * @param httpRequest
     * @param reqBuilder
     * @return
     */
    public <P, R> void createHeader(HttpRequest<P, R> httpRequest,Request.Builder reqBuilder){
        Map<String,String> headers = httpRequest.getHeaders();
        Headers headersBuilder = Headers.of(headers);
        reqBuilder.headers(headersBuilder);
    }


    /**
     * 创建body
     * @param httpRequest
     * @return
     */
    public <P, R> RequestBody createBody(HttpRequest<P, R> httpRequest){
        HttpBody<P> httpBody = httpRequest.getHttpBody();
        if (httpBody == null) {
            httpBody = new HttpBody<>();
        }
        if (httpBody.getData() == null) {
            httpBody.setData(new byte[0]);
            httpBody.setDataType(BodyDataType.FORM);
        }
        HttpBody<P> finalHttpBody = httpBody;
        MediaType mediaType = httpRequest.getHeaders().getContentType()
                .map(MediaType::get)
                .orElseGet(() -> {
                    if (finalHttpBody.getDataType() == BodyDataType.FORM) {
                        return MediaType.get(HttpHeaders.FORM_HEADER_VALUE);
                    } else {
                        return MediaType.get(HttpHeaders.JSON_HEADER_VALUE);
                    }
                });
        return RequestBody.create(finalHttpBody.getData(), mediaType);
    }

    /**
     * 创建request
     * @param httpRequest
     * @return
     */
    public <P, R> Request.Builder createRequest(HttpRequest<P, R> httpRequest){
        return new Request.Builder().url(httpRequest.getParameterizedUrl());
    }

    /**
     * 结果转换
     * @param response
     * @param <T>
     * @return
     * @throws IOException
     */
    private <T> HttpResponse<T> parseOkHttpResponse(Response response) throws IOException {
        try {
            HttpBody<T> httpBody = new HttpBody<>();
            httpBody.setData(Objects.requireNonNull(response.body()).bytes());
            Headers headers = response.headers();
            Set<String> names = headers.names();
            HttpHeaders httpHeaders = new HttpHeaders();
            names.forEach(it -> httpHeaders.put(it, headers.get(it)));
            return new DefaultHttpResponse<>(response.code(),httpBody, httpHeaders);
        }finally {
            response.close();
        }
    }

    /**
     * 执行请求
     * @param request
     * @param <T>
     * @return
     */
    private <T> HttpResponse<T> initAndExecute(Request request) throws IOException {
        Response response = okHttpClient.newCall(request).execute();
        return parseOkHttpResponse(response);
    }

    @Override
    public <P, R> HttpResponse<R> get(HttpRequest<P, R> httpRequest, HttpContext httpContext) throws IOException {
        final Request.Builder reqBuilder = createRequest(httpRequest);
        createHeader(httpRequest,reqBuilder);
        return initAndExecute(reqBuilder.build());
    }

    @Override
    public <P, R> HttpResponse<R> post(HttpRequest<P, R> httpRequest, HttpContext httpContext) throws IOException {
        final Request.Builder reqBuilder = createRequest(httpRequest);
        createHeader(httpRequest,reqBuilder);
        Request request = reqBuilder.post(createBody(httpRequest)).build();
        return initAndExecute(request);
    }

    @Override
    public <P, R> HttpResponse<R> put(HttpRequest<P, R> httpRequest, HttpContext httpContext) throws IOException {
        Request.Builder reqBuilder = createRequest(httpRequest);
        createHeader(httpRequest,reqBuilder);
        reqBuilder.put(createBody(httpRequest));
        Request request = reqBuilder.put(createBody(httpRequest)).build();
        return initAndExecute(request);
    }

    @Override
    public <P, R> HttpResponse<R> delete(HttpRequest<P, R> httpRequest, HttpContext httpContext) throws IOException {
        final Request.Builder request = createRequest(httpRequest).delete();
        return initAndExecute(request.build());
    }
}
