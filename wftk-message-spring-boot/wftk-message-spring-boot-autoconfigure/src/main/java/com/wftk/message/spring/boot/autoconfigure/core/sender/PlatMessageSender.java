package com.wftk.message.spring.boot.autoconfigure.core.sender;

import com.wftk.message.spring.boot.autoconfigure.core.exception.PlatMessageException;
import com.wftk.message.spring.boot.autoconfigure.core.message.PlatMessage;

/**
 * <AUTHOR>
 * @create 2024/5/22 14:03
 */
public interface PlatMessageSender<T extends PlatMessage> {

    /**
     * 发送消息
     * @param platMessage
     * @return
     * @throws PlatMessageException
     */
    boolean send(T platMessage) throws PlatMessageException;
}
