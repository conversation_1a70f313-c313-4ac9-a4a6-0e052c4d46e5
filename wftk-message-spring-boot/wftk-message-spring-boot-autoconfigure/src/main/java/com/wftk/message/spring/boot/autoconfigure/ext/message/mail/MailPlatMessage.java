package com.wftk.message.spring.boot.autoconfigure.ext.message.mail;

import com.wftk.message.spring.boot.autoconfigure.core.message.PlatMessage;
import org.springframework.lang.NonNull;

/**
 * <AUTHOR>
 * @create 2024/5/22 11:19
 */
public abstract class MailPlatMessage implements PlatMessage<MailPlatMessageType> {

    private final String subject;
    private final MailMetaInfo mailMetaInfo;
    private String content;

    public MailPlatMessage(String subject, @NonNull MailMetaInfo mailMetaInfo) {
        this.subject = subject;
        this.mailMetaInfo = mailMetaInfo;
    }

    @Override
    public String getContent() {
        return content;
    }

    @Override
    public void setContent(String content) {
        this.content = content;
    }

    public String getSubject() {
        return subject;
    }

    public MailMetaInfo getMailMetaInfo() {
        return mailMetaInfo;
    }

    @Override
    public MailPlatMessageType getPlatMessageType() {
        return mailMetaInfo.getMailMessageType();
    }
}
