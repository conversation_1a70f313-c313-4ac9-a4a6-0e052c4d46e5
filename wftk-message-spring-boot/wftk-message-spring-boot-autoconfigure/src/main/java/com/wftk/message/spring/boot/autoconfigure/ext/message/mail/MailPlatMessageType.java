package com.wftk.message.spring.boot.autoconfigure.ext.message.mail;

import com.wftk.message.spring.boot.autoconfigure.core.message.type.PlatMessageType;

/**
 * <AUTHOR>
 * @create 2024/5/22 17:25
 */
public enum MailPlatMessageType implements PlatMessageType {
    SIMPLE("simple"), MIME("mime");

    private final String type;

    MailPlatMessageType(String type) {
        this.type = type;
    }

    @Override
    public String getOriginalType() {
        return type;
    }
}
