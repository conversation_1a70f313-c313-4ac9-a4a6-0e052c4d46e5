package com.wftk.message.spring.boot.autoconfigure.core.exception;

import java.io.Serial;

/**
 * <AUTHOR>
 * @create 2024/5/22 11:26
 */
public abstract class PlatMessageException extends RuntimeException {
    @Serial
    private static final long serialVersionUID = 31888975167413520L;

    public PlatMessageException(String message) {
        super(message);
    }

    public PlatMessageException(String message, Throwable cause) {
        super(message, cause);
    }

    public PlatMessageException(Throwable cause) {
        super(cause);
    }
}
