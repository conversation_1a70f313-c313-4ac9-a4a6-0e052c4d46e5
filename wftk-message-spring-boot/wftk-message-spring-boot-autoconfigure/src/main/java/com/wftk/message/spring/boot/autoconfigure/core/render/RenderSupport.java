package com.wftk.message.spring.boot.autoconfigure.core.render;

import com.wftk.message.spring.boot.autoconfigure.core.exception.TemplateRenderingException;

import java.util.Map;

/**
 * 支持模版渲染
 * <AUTHOR>
 * @create 2024/5/22 11:23
 */
public interface RenderSupport {


    /**
     * 根据模板及参数渲染
     * @param templateCode
     * @param params
     * @return
     * @throws TemplateRenderingException
     */
    String render(String templateCode, Map<?, ?> params) throws TemplateRenderingException;
}
