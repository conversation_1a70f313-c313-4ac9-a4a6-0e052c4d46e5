package com.wftk.message.spring.boot.autoconfigure.ext.message.feishu.robot;

import com.wftk.message.spring.boot.autoconfigure.core.message.type.PlatMessageType;

/**
 * <AUTHOR>
 * @create 2024/5/23 10:21
 */
public enum FeishuRobotPlatMessageType implements PlatMessageType {
    TEXT("text", "文本"), POST("post", "富文本"), SHARE_CHAT("share_chat", "群名片"),
    IMAGE("image", "图片"), INTERACTIVE("interactive", "消息卡片");

    private final String type;
    private final String label;

    FeishuRobotPlatMessageType(String type, String label) {
        this.type = type;
        this.label = label;
    }

    @Override
    public String getOriginalType() {
        return type;
    }
}
