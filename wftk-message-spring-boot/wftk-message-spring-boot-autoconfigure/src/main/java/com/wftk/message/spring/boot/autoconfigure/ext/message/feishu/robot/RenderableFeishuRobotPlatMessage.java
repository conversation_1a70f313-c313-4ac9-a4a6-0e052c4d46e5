package com.wftk.message.spring.boot.autoconfigure.ext.message.feishu.robot;

import com.wftk.message.spring.boot.autoconfigure.core.render.Renderable;
import org.springframework.lang.NonNull;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/5/23 11:26
 */
public class RenderableFeishuRobotPlatMessage extends FeishuRobotPlatMessage implements Renderable {

    private final String templateCode;
    private Map<?, ?> paramsMap;

    public RenderableFeishuRobotPlatMessage(@NonNull FeishuRobotPlatMessageType type, @NonNull String templateCode) {
        super(type);
        this.templateCode = templateCode;
    }

    @Override
    public String getTemplateCode() {
        return templateCode;
    }

    @Override
    public void setParams(Map<?, ?> params) {
        this.paramsMap = params;
    }

    @Override
    public Map<?, ?> getParams() {
        return paramsMap;
    }
}
