package com.wftk.message.spring.boot.autoconfigure.core.message;

import com.wftk.message.spring.boot.autoconfigure.core.message.type.PlatMessageType;

/**
 * <AUTHOR>
 * @create 2024/5/22 11:16
 */
public interface PlatMessage<T extends PlatMessageType> {

    /**
     * 获取消息内容
     * @return
     */
    String getContent();

    /**
     * 设置消息内容
     * @param content
     */
    void setContent(String content);


    /**
     * 获取消息类型
     * @return
     */
    T getPlatMessageType();
}
