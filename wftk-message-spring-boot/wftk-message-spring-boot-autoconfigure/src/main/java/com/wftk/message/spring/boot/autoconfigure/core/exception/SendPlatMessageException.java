package com.wftk.message.spring.boot.autoconfigure.core.exception;

import java.io.Serial;

/**
 * <AUTHOR>
 * @create 2024/5/22 16:29
 */
public class SendPlatMessageException extends PlatMessageException {
    @Serial
    private static final long serialVersionUID = 3512604805172200988L;

    public SendPlatMessageException(String message) {
        super(message);
    }

    public SendPlatMessageException(String message, Throwable cause) {
        super(message, cause);
    }

    public SendPlatMessageException(Throwable cause) {
        super(cause);
    }
}
