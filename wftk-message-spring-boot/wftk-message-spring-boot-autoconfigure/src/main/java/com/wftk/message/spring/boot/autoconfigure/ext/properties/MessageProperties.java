package com.wftk.message.spring.boot.autoconfigure.ext.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

/**
 * <AUTHOR>
 * @create 2024/5/24 11:18
 */
@ConfigurationProperties("config.message")
public class MessageProperties {

    @NestedConfigurationProperty
    private MailProperties mail;

    @NestedConfigurationProperty
    private FeishuProperties feishu;


    public MailProperties getMail() {
        return mail;
    }

    public void setMail(MailProperties mail) {
        this.mail = mail;
    }

    public FeishuProperties getFeishu() {
        return feishu;
    }

    public void setFeishu(FeishuProperties feishu) {
        this.feishu = feishu;
    }
}
