package com.wftk.message.spring.boot.autoconfigure.ext.message.mail;

import com.wftk.message.spring.boot.autoconfigure.core.render.Renderable;

import java.util.Map;

/**
 * 可通过模板渲染的邮件消息
 * <AUTHOR>
 * @create 2024/5/22 13:59
 */
public class RenderableMailPlatMessage extends MailPlatMessage implements Renderable {

    private final String templateCode;
    private Map<?, ?> paramsMap;


    public RenderableMailPlatMessage(String subject, MailMetaInfo mailMetaInfo, String templateCode) {
        super(subject, mailMetaInfo);
        this.templateCode = templateCode;
    }

    @Override
    public String getTemplateCode() {
        return templateCode;
    }

    @Override
    public void setParams(Map<?, ?> params) {
        this.paramsMap = params;
    }

    @Override
    public Map<?, ?> getParams() {
        return paramsMap;
    }
}
