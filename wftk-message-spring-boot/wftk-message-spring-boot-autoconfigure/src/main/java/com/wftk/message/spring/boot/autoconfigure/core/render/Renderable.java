package com.wftk.message.spring.boot.autoconfigure.core.render;

import org.springframework.lang.Nullable;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/5/22 13:56
 */
public interface Renderable {


    /**
     * 模板编码
     * @return
     */
    String getTemplateCode();


    /**
     * 设置参数值
     * @param params
     */
    void setParams(@Nullable Map<?, ?> params);


    /**
     * 获取参数值
     * @return
     */
    Map<?, ?> getParams();

}
