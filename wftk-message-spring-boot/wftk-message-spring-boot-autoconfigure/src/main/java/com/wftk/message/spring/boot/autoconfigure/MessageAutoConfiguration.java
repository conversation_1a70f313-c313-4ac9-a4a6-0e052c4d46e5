package com.wftk.message.spring.boot.autoconfigure;

import com.wftk.message.spring.boot.autoconfigure.ext.FeishuRobotAutoConfiguration;
import com.wftk.message.spring.boot.autoconfigure.ext.MailAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @create 2024/5/21 20:01
 */
@Configuration
@EnableConfigurationProperties
@Import({MailAutoConfiguration.class, FeishuRobotAutoConfiguration.class})
public class MessageAutoConfiguration {

}
