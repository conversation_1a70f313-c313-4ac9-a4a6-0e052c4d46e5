package com.wftk.message.spring.boot.autoconfigure.core.exception;

import java.io.Serial;

/**
 * <AUTHOR>
 * @create 2024/5/22 11:25
 */
public class TemplateRenderingException extends PlatMessageException {
    @Serial
    private static final long serialVersionUID = -1277065097870361515L;


    public TemplateRenderingException(String templateCode) {
        this(templateCode, null);
    }

    public TemplateRenderingException(String templateCode, Throwable cause) {
        super(String.format("Template [%s] rendering error", templateCode), cause);
    }

}
