package com.wftk.message.spring.boot.autoconfigure.ext.message.feishu.robot;

import com.wftk.message.spring.boot.autoconfigure.core.message.PlatMessage;
import org.springframework.lang.NonNull;

/**
 * 飞书机器人消息
 * <AUTHOR>
 * @create 2024/5/23 10:15
 */
public abstract class FeishuRobotPlatMessage implements PlatMessage<FeishuRobotPlatMessageType> {

    private final FeishuRobotPlatMessageType type;
    private String content;

    protected FeishuRobotPlatMessage(@NonNull FeishuRobotPlatMessageType type) {
        this.type = type;
    }


    @Override
    public String getContent() {
        return content;
    }

    @Override
    public void setContent(String content) {
        this.content = content;
    }

    @Override
    public FeishuRobotPlatMessageType getPlatMessageType() {
        return type;
    }
}
