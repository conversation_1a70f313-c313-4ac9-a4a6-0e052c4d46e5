package com.wftk.message.spring.boot.autoconfigure.core.sender;

import com.wftk.message.spring.boot.autoconfigure.core.exception.PlatMessageException;
import com.wftk.message.spring.boot.autoconfigure.core.exception.SendPlatMessageException;
import com.wftk.message.spring.boot.autoconfigure.core.exception.TemplateRenderingException;
import com.wftk.message.spring.boot.autoconfigure.core.message.PlatMessage;
import com.wftk.message.spring.boot.autoconfigure.core.render.Renderable;
import com.wftk.message.spring.boot.autoconfigure.ext.render.FreemarkerRenderSupport;
import org.springframework.util.StringUtils;



/**
 * <AUTHOR>
 * @create 2024/5/22 15:46
 */
public abstract class BasePlatMessageSender<T extends PlatMessage<?>> extends FreemarkerRenderSupport implements PlatMessageSender<T> {


    public BasePlatMessageSender(String templatePath) {
        super(templatePath);
    }

    @Override
    public boolean send(T platMessage) throws PlatMessageException {
        if (platMessage == null) {
            return false;
        }
        if (platMessage instanceof Renderable rMessage) {
            if (!StringUtils.hasText(rMessage.getTemplateCode())) {
                throw new TemplateRenderingException("TemplateCode must not be empty.");
            }
            //渲染模板
            String content = render(rMessage.getTemplateCode(), rMessage.getParams());
            platMessage.setContent(content);
        }
        try {
            return doSend(platMessage);
        } catch (Exception e) {
            throw new SendPlatMessageException("send message error.", e);
        }
    }

    /**
     * 发起请求
     * @param platMessage
     * @return
     * @throws SendPlatMessageException
     */
    protected abstract boolean doSend(T platMessage) throws SendPlatMessageException;

}
