package com.wftk.message.spring.boot.autoconfigure.ext;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.message.spring.boot.autoconfigure.ext.properties.MailProperties;
import com.wftk.message.spring.boot.autoconfigure.ext.properties.MessageProperties;
import com.wftk.message.spring.boot.autoconfigure.ext.sender.mail.MailPlatMessageSender;
import jakarta.mail.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @create 2024/5/24 11:28
 */
@ConditionalOnClass(Session.class)
@ConditionalOnProperty(name = "config.message.mail.enable", havingValue = "true", matchIfMissing = true)
@Configuration
public class MailAutoConfiguration {

    private static final Logger log = LoggerFactory.getLogger(MailAutoConfiguration.class);

    @Bean
    @ConditionalOnMissingBean(JavaMailSender.class)
    JavaMailSender mailSender(MessageProperties messageProperties) {
        JavaMailSenderImpl sender = new JavaMailSenderImpl();
        applyProperties(messageProperties.getMail(), sender);
        return sender;
    }


    @Bean
    @ConditionalOnMissingBean
    MailPlatMessageSender mailPlatMessageSender(MessageProperties messageProperties, JavaMailSender javaMailSender) {
        return new MailPlatMessageSender(javaMailSender, messageProperties.getMail().getTemplatePath());
    }



    private void applyProperties(MailProperties properties, JavaMailSenderImpl sender) {
        sender.setHost(properties.getHost());
        if (properties.getPort() != null) {
            sender.setPort(properties.getPort());
        }
        sender.setUsername(properties.getUsername());
        sender.setPassword(properties.getPassword());
        sender.setProtocol(properties.getProtocol());
        sender.setDefaultEncoding(getCharset(properties.getCharset()));
    }


    /**
     *
     * @param charset
     * @return
     */
    private String getCharset(String charset) {
        String defaultCharset = StandardCharsets.UTF_8.name();
        if (StrUtil.isBlank(charset)) {
            return defaultCharset;
        }
        try {
            return CharsetUtil.charset(charset).name();
        } catch (Exception e) {
            log.warn("parse charset [{}] error. default charset [{}] will be used.", charset, defaultCharset, e);
            return defaultCharset;
        }
    }


}
