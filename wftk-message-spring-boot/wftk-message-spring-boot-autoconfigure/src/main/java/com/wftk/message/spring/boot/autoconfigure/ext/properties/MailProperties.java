package com.wftk.message.spring.boot.autoconfigure.ext.properties;


import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @create 2024/5/24 10:33
 */
public class MailProperties {

    private final String DEFAULT_CHARSET = StandardCharsets.UTF_8.name();

    private String host;
    private Integer port;
    private String username;
    private String password;
    private String protocol;
    /**
     * 模板路径
     */
    private String templatePath;
    private String charset = DEFAULT_CHARSET;

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getCharset() {
        return charset;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public String getTemplatePath() {
        return templatePath;
    }

    public void setTemplatePath(String templatePath) {
        this.templatePath = templatePath;
    }
}
