package com.wftk.message.spring.boot.autoconfigure.ext.properties;

/**
 * 飞书配置
 * <AUTHOR>
 * @create 2024/5/24 11:20
 */
public class FeishuProperties {


    private FeishuRobotProperties robot;


    public FeishuRobotProperties getRobot() {
        return robot;
    }

    public void setRobot(FeishuRobotProperties robot) {
        this.robot = robot;
    }

    /**
     * 飞书机器人配置
     */
    public static class FeishuRobotProperties {

        private String webhook;

        /**
         * 签名秘钥
         */
        private String secret;

        /**
         * 模板路径
         */
        private String templatePath;

        public String getWebhook() {
            return webhook;
        }

        public void setWebhook(String webhook) {
            this.webhook = webhook;
        }

        public String getSecret() {
            return secret;
        }

        public void setSecret(String secret) {
            this.secret = secret;
        }

        public String getTemplatePath() {
            return templatePath;
        }

        public void setTemplatePath(String templatePath) {
            this.templatePath = templatePath;
        }
    }


}
