package com.wftk.message.spring.boot.autoconfigure.ext;

import com.wftk.http.client.core.executor.HttpRequestExecutor;
import com.wftk.message.spring.boot.autoconfigure.ext.properties.MessageProperties;
import com.wftk.message.spring.boot.autoconfigure.ext.sender.feishu.robot.FeishuRobotPlatMessageSender;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2024/5/24 14:16
 */
@ConditionalOnClass(HttpRequestExecutor.class)
@ConditionalOnProperty(name = "config.message.feishu.robot.enable", havingValue = "true", matchIfMissing = true)
@Configuration
public class FeishuRobotAutoConfiguration {


    @ConditionalOnMissingBean
    @Bean
    FeishuRobotPlatMessageSender feishuRobotPlatMessageSender(MessageProperties messageProperties, HttpRequestExecutor httpRequestExecutor) {
        return new FeishuRobotPlatMessageSender(messageProperties.getFeishu().getRobot(), httpRequestExecutor);
    }

}
