package com.wftk.message.spring.boot.autoconfigure.ext.sender.feishu.robot;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wftk.http.client.core.common.interceptor.RequestInterceptor;
import com.wftk.http.client.core.common.interceptor.ResponseBodyInterceptor;
import com.wftk.http.client.core.common.type.DataType;
import com.wftk.http.client.core.executor.HttpRequestExecutor;
import com.wftk.http.client.core.request.HttpRequest;
import com.wftk.http.client.core.request.RequestMethod;
import com.wftk.http.client.core.request.builder.RequestBuilders;
import com.wftk.jackson.core.JSONObject;
import com.wftk.jackson.core.TargetType;
import com.wftk.message.spring.boot.autoconfigure.core.exception.SendPlatMessageException;
import com.wftk.message.spring.boot.autoconfigure.ext.message.feishu.robot.FeishuRobotPlatMessage;
import com.wftk.message.spring.boot.autoconfigure.core.sender.HttpPlatMessageSender;
import com.wftk.message.spring.boot.autoconfigure.ext.message.feishu.robot.FeishuRobotPlatMessageType;
import com.wftk.message.spring.boot.autoconfigure.ext.properties.FeishuProperties;
import com.wftk.message.spring.boot.autoconfigure.ext.sender.feishu.robot.response.FeishuRobotResponse;
import com.wftk.message.spring.boot.autoconfigure.ext.sender.feishu.robot.util.FeishuSignUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.NonNull;

import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/5/23 11:33
 */
public class FeishuRobotPlatMessageSender extends HttpPlatMessageSender<FeishuRobotPlatMessage> {


    private static final Logger log = LoggerFactory.getLogger(FeishuRobotPlatMessageSender.class);

    private final String webhook;
    private final String secret;

    public FeishuRobotPlatMessageSender(FeishuProperties.FeishuRobotProperties feishuRobotProperties, @NonNull HttpRequestExecutor httpRequestExecutor) {
        super(feishuRobotProperties.getTemplatePath(), httpRequestExecutor);
        this.webhook = feishuRobotProperties.getWebhook();
        this.secret = feishuRobotProperties.getSecret();
    }

    @SuppressWarnings("unchecked")
    @Override
    protected <P, R> HttpRequest<P, R> buildHttpRequest(FeishuRobotPlatMessage platMessage) {
        //普通文本消息可能需要单独处理，例如单独只传了text字段
        if (FeishuRobotPlatMessageType.TEXT.equals(platMessage.getPlatMessageType())) {
            return buildSimpleHttpRequest(platMessage.getPlatMessageType(), platMessage.getContent());
        }
        return (HttpRequest<P, R>) RequestBuilders.<String, FeishuRobotResponse>bodyBuilder(webhook)
                .method(RequestMethod.POST)
                .json(platMessage.getContent())
                .resultType(new DataType<>() {
                })
                .build();
    }

    @Override
    protected RequestInterceptor getHttpRequestInterceptor(FeishuRobotPlatMessage platMessage) {
        return new RequestInterceptor() {
            @Override
            public <P, R> HttpRequest<P, R> pre(HttpRequest<P, R> httpRequest) {
                P body = httpRequest.getHttpBody().getBody();
                if (body == null) {
                    return httpRequest;
                }
                Map<String, Object> bodyMap;
                if (body instanceof String bodyStr) {
                    bodyMap = JSONObject.getInstance().parseMap(bodyStr, String.class, Object.class);
                } else {
                    bodyMap = JSONObject.getInstance().convertValue(body, new TargetType<>() {
                    });
                }
                long timestamp = System.currentTimeMillis() / 1000;
                bodyMap.put("timestamp", timestamp);
                try {
                    bodyMap.put("sign", FeishuSignUtil.sign(timestamp, secret));
                } catch (NoSuchAlgorithmException | InvalidKeyException e) {
                    throw new RuntimeException(e);
                }
                //重新赋值(body置空，阻止encoder进行序列化，自行手动实现序列化)
                httpRequest.getHttpBody().setBody(null);
                httpRequest.getHttpBody().setData(JSONObject.getInstance().toJSONString(bodyMap).getBytes(StandardCharsets.UTF_8));
                return httpRequest;
            }
        };

    }

    @Override
    protected ResponseBodyInterceptor getHttpResponseInterceptor(FeishuRobotPlatMessage platMessage) {
        return new ResponseBodyInterceptor() {
            @Override
            public <P, R> R pre(HttpRequest<P, R> httpRequest, R data) {
                if (!(data instanceof FeishuRobotResponse)) {
                    return data;
                }
                if (!((FeishuRobotResponse) data).isSuccess()) {
                    log.error("feishu robot response: [{}]", data);
                    throw new SendPlatMessageException("send feishu robot message fail");
                }
                return data;
            }
        };
    }

    @Override
    protected boolean doSend(FeishuRobotPlatMessage platMessage) throws SendPlatMessageException {
        execute(platMessage);
        return true;
    }


    /**
     * 普通文本消息
     *
     * {
     *     "msg_type": "text",
     *     "content": {
     *         "text": "新更新提醒"
     *     }
     * }
     * 飞书机器人普通文本消息格式如上，本方法判断如果传入的内容是以上完整格式的内容，则直接发送，否则组装为以上格式
     *
     *
     * @param type
     * @param content
     * @return
     * @param <P>
     * @param <R>
     */
    @SuppressWarnings("unchecked")
    private <P, R> HttpRequest<P, R> buildSimpleHttpRequest(FeishuRobotPlatMessageType type, String content) {
        OriginalFeishuTextMessage originalFeishuTextMessage = null;
        try {
            originalFeishuTextMessage = JSONObject.getInstance().parseObject(content, OriginalFeishuTextMessage.class);
        } catch (Exception e) {
            log.debug("parse json error.", e);
            originalFeishuTextMessage = new OriginalFeishuTextMessage();
        }
        if (StrUtil.isBlank(originalFeishuTextMessage.getMsgType())) {
            originalFeishuTextMessage.setMsgType(type.getOriginalType());
        }
        if (originalFeishuTextMessage.getContent() == null || StrUtil.isBlank(originalFeishuTextMessage.getContent().getText())) {
            OriginalFeishuTextMessage.Content body = new OriginalFeishuTextMessage.Content();
            body.setText(content);
            originalFeishuTextMessage.setContent(body);
        }
        return (HttpRequest<P, R>) RequestBuilders.<OriginalFeishuTextMessage, FeishuRobotResponse>bodyBuilder(webhook)
                .method(RequestMethod.POST)
                .json(originalFeishuTextMessage)
                .resultType(new DataType<>() {
                })
                .build();
    }



    /**
     * 飞书文本消息
     */
    public static class OriginalFeishuTextMessage {

        @JsonProperty("msg_type")
        private String msgType;
        private Content content;

        public String getMsgType() {
            return msgType;
        }

        public void setMsgType(String msgType) {
            this.msgType = msgType;
        }

        public Content getContent() {
            return content;
        }

        public void setContent(Content content) {
            this.content = content;
        }

        public static class Content {
            private String text;

            public String getText() {
                return text;
            }

            public void setText(String text) {
                this.text = text;
            }
        }
    }


}
