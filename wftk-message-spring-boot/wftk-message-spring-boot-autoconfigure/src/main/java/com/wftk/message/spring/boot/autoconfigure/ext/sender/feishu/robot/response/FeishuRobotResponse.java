package com.wftk.message.spring.boot.autoconfigure.ext.sender.feishu.robot.response;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/5/23 15:24
 */
public class FeishuRobotResponse {


    private Integer code;
    private String msg;
    private Map<String, Object> data;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

    @JsonIgnore
    public boolean isSuccess() {
        return code == 0;
    }
}
