package com.wftk.message.spring.boot.autoconfigure.ext.render;

import cn.hutool.extra.template.Template;
import cn.hutool.extra.template.TemplateConfig;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.TemplateUtil;
import com.wftk.message.spring.boot.autoconfigure.core.exception.TemplateRenderingException;
import com.wftk.message.spring.boot.autoconfigure.core.render.RenderSupport;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/5/22 11:39
 */
public class FreemarkerRenderSupport implements RenderSupport {

    private final String templatePath;

    public FreemarkerRenderSupport(String templatePath) {
        this.templatePath = templatePath;
    }

    @Override
    public String render(String templateCode, Map<?, ?> params) throws TemplateRenderingException {
        //通过freemarker模板引擎渲染短信内容
        TemplateEngine templateEngine = TemplateUtil.createEngine(new TemplateConfig(templatePath, TemplateConfig.ResourceMode.CLASSPATH));
        Template template = templateEngine.getTemplate(templateCode + ".ftl");
        return template.render(params);
    }
}
