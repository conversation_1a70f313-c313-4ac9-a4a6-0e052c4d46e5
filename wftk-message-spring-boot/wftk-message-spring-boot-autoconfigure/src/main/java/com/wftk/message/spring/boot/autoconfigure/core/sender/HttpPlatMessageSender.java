package com.wftk.message.spring.boot.autoconfigure.core.sender;

import com.wftk.http.client.core.common.interceptor.RequestInterceptor;
import com.wftk.http.client.core.common.interceptor.ResponseBodyInterceptor;
import com.wftk.http.client.core.executor.HttpRequestExecutor;
import com.wftk.http.client.core.request.HttpRequest;
import com.wftk.message.spring.boot.autoconfigure.core.message.PlatMessage;
import org.springframework.lang.NonNull;

/**
 * <AUTHOR>
 * @create 2024/5/23 14:26
 */
public abstract class HttpPlatMessageSender<T extends PlatMessage<?>> extends BasePlatMessageSender<T> {

    private final HttpRequestExecutor httpRequestExecutor;

    public HttpPlatMessageSender(String templatePath, @NonNull HttpRequestExecutor httpRequestExecutor) {
        super(templatePath);
        this.httpRequestExecutor = httpRequestExecutor;
    }

    public HttpRequestExecutor getHttpRequestExecutor() {
        return httpRequestExecutor;
    }


    /**
     * 获取请求拦截器(可以在其中进行签名)
     * @param platMessage
     * @return
     */
    protected RequestInterceptor getHttpRequestInterceptor(T platMessage) {
        return null;
    }


    /**
     * 获取响应拦截器(处理http状态码200，用报文中业务状态码来判断业务是否成功的情况)
     * @param platMessage
     * @return
     */
    protected ResponseBodyInterceptor getHttpResponseInterceptor(T platMessage) {
        return null;
    }


    /**
     * 执行请求
     * @param platMessage
     * @return
     * @param <P>
     * @param <R>
     */
    protected <P, R> R execute(T platMessage) {
        HttpRequest<P, R> request = buildHttpRequest(platMessage);
        return httpRequestExecutor.execute(request, getHttpRequestInterceptor(platMessage), getHttpResponseInterceptor(platMessage), null);
    }


    /**
     * 构造请求
     * @param platMessage
     * @return
     * @param <P>
     * @param <R>
     */
    protected abstract <P, R> HttpRequest<P, R> buildHttpRequest(T platMessage);

}
