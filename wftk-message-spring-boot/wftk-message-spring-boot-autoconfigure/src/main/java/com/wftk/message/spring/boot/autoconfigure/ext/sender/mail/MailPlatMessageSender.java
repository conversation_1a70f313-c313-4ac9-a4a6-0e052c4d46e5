package com.wftk.message.spring.boot.autoconfigure.ext.sender.mail;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.message.spring.boot.autoconfigure.core.exception.SendPlatMessageException;
import com.wftk.message.spring.boot.autoconfigure.ext.message.mail.MailMetaInfo;
import com.wftk.message.spring.boot.autoconfigure.ext.message.mail.MailPlatMessage;
import com.wftk.message.spring.boot.autoconfigure.ext.message.mail.MailPlatMessageType;
import com.wftk.message.spring.boot.autoconfigure.core.sender.BasePlatMessageSender;
import org.springframework.mail.MailMessage;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMailMessage;
import org.springframework.mail.javamail.MimeMessageHelper;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @create 2024/5/22 16:20
 */
public class MailPlatMessageSender extends BasePlatMessageSender<MailPlatMessage> {


    private final JavaMailSender mailSender;

    public MailPlatMessageSender(JavaMailSender mailSender, String templatePath) {
        super(templatePath);
        this.mailSender = mailSender;
    }


    @Override
    protected boolean doSend(MailPlatMessage platMessage) throws SendPlatMessageException {
        MailPlatMessageType platMessageType = platMessage.getPlatMessageType();
        if (MailPlatMessageType.MIME.equals(platMessage.getPlatMessageType())) {
            return sendMimeMessage(platMessage);
        } else if (MailPlatMessageType.SIMPLE.equals(platMessage.getPlatMessageType())) {
            return sendSimpleMessage(platMessage);
        } else {
            throw new SendPlatMessageException("unsupported type of mail message. [" + platMessageType + "]");
        }
    }


    protected boolean sendMimeMessage(MailPlatMessage platMessage) throws SendPlatMessageException {
        MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mailSender.createMimeMessage(), StandardCharsets.UTF_8.name());
        MimeMailMessage mailMessage = new MimeMailMessage(mimeMessageHelper);
        configureMail(mailMessage, platMessage);
        mailSender.send(mailMessage.getMimeMessage());
        return true;
    }


    protected boolean sendSimpleMessage(MailPlatMessage platMessage) throws SendPlatMessageException {
        SimpleMailMessage mailMessage = new SimpleMailMessage();
        configureMail(mailMessage, platMessage);
        mailSender.send(mailMessage);
        return true;
    }



    protected <T extends MailMessage> void configureMail(T mailMessage, MailPlatMessage mailPlatMessage) throws SendPlatMessageException {
        MailMetaInfo mailMetaInfo = mailPlatMessage.getMailMetaInfo();
        if (StrUtil.isBlank(mailMetaInfo.getFrom())) {
            throw new SendPlatMessageException("mail [from] must not be empty");
        }
        if (StrUtil.isBlank(mailPlatMessage.getSubject())) {
            throw new SendPlatMessageException("mail [subject] must not be empty");
        }
        if (CollUtil.isEmpty(mailMetaInfo.getTo())) {
            throw new SendPlatMessageException("mail [to] must not be empty");
        }

        mailMessage.setFrom(mailMetaInfo.getFrom());
        mailMessage.setTo(ArrayUtil.toArray(mailMetaInfo.getTo(), String.class));
        mailMessage.setSubject(mailPlatMessage.getSubject());
        mailMessage.setText(mailPlatMessage.getContent());
        if (CollUtil.isNotEmpty(mailMetaInfo.getBcc())) {
            mailMessage.setBcc(ArrayUtil.toArray(mailMetaInfo.getBcc(), String.class));
        }
        if (CollUtil.isNotEmpty(mailMetaInfo.getCc())) {
            mailMessage.setCc(ArrayUtil.toArray(mailMetaInfo.getCc(), String.class));
        }
    }
}



