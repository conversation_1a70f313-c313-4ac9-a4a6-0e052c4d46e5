package com.wftk.message.spring.boot.autoconfigure.ext.message.mail;

import java.util.LinkedHashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2024/5/22 18:43
 */
public class MailMetaInfo {

    /**
     * 邮件类型
     */
    private MailPlatMessageType type;


    /** 邮件信息 **/
    private String from;
    private final Set<String> to = new LinkedHashSet<>();
    private final Set<String> cc = new LinkedHashSet<>();
    private final Set<String> bcc = new LinkedHashSet<>();
    private String replyTo;

    private MailMetaInfo() {}

    public static MailMetaInfo builder() {
        return new MailMetaInfo();
    }

    public MailMetaInfo mailMessageType(MailPlatMessageType type) {
        this.type = type;
        return this;
    }

    public MailMetaInfo from(String from) {
        this.from = from;
        return this;
    }

    public MailMetaInfo to(String to) {
        this.to.add(to);
        return this;
    }

    public MailMetaInfo cc(String cc) {
        this.cc.add(cc);
        return this;
    }

    public MailMetaInfo bcc(String bcc) {
        this.bcc.add(bcc);
        return this;
    }

    public MailMetaInfo replyTo(String replyTo) {
        this.replyTo = replyTo;
        return this;
    }




    public MailPlatMessageType getMailMessageType() {
        return type;
    }

    public String getFrom() {
        return from;
    }

    public Set<String> getTo() {
        return to;
    }

    public Set<String> getCc() {
        return cc;
    }

    public Set<String> getBcc() {
        return bcc;
    }

    public String getReplyTo() {
        return replyTo;
    }
}
