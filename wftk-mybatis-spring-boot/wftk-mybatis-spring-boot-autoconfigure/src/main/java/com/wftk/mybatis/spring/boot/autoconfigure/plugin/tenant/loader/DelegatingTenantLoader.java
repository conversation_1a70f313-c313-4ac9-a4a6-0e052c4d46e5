package com.wftk.mybatis.spring.boot.autoconfigure.plugin.tenant.loader;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import net.sf.jsqlparser.expression.Expression;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/3/6 17:27
 */
public class DelegatingTenantLoader implements TenantLoader {

    private static final Logger LOGGER = LoggerFactory.getLogger(DelegatingTenantLoader.class);

    private final List<TenantLoader> tenantLoaders;

    public DelegatingTenantLoader(List<TenantLoader> tenantLoaders) {
        if (CollectionUtil.isNotEmpty(tenantLoaders)) {
            tenantLoaders.sort(Comparator.comparingInt(TenantLoader::getSort));
        }
        this.tenantLoaders = tenantLoaders;
    }

    @Override
    public String getTenantId() {
        if (CollectionUtil.isEmpty(tenantLoaders)) {
            return null;
        }
        for (TenantLoader tenantLoader : tenantLoaders) {
            String tenantId = tenantLoader.getTenantId();
            if (StrUtil.isNotBlank(tenantId)) {
                return tenantId;
            }
        }
        LOGGER.debug("cannot find tenant info.");
        return null;
    }

    @Override
    public Expression getTenantExpression() {
        if (CollectionUtil.isEmpty(tenantLoaders)) {
            return null;
        }
        for (TenantLoader tenantLoader : tenantLoaders) {
            Expression tenantExpression = tenantLoader.getTenantExpression();
            if (tenantExpression != null) {
                return tenantExpression;
            }
        }
        LOGGER.debug("cannot find tenant info.");
        return null;
    }
}
