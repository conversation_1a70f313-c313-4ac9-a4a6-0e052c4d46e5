package com.wftk.mybatis.spring.boot.autoconfigure.id;

import cn.hutool.core.util.IdUtil;

/**
 * <AUTHOR>
 * @create 2023/8/3 11:24
 */
public class WorkerIdGenerator {

    private static final long WORKER_ID_BITS = 5L;
    // 最大支持机器节点数0~31，一共32个
    private static final long MAX_WORKER_ID = ~(-1L << WORKER_ID_BITS);
    private static final long DATA_CENTER_ID_BITS = 5L;
    // 最大支持数据中心节点数0~31，一共32个
    private static final long MAX_DATA_CENTER_ID = ~(-1L << DATA_CENTER_ID_BITS);

    public static short getWorkerId() {
        return (short)IdUtil.getWorkerId(IdUtil.getDataCenterId(MAX_DATA_CENTER_ID), MAX_WORKER_ID);
    }

}
