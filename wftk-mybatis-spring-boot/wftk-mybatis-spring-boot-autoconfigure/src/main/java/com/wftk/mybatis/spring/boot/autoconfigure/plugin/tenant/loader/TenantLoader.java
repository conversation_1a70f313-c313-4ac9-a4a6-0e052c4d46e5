package com.wftk.mybatis.spring.boot.autoconfigure.plugin.tenant.loader;

import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.ExpressionVisitor;
import net.sf.jsqlparser.parser.SimpleNode;

import java.io.Serial;

/**
 * <AUTHOR>
 * @create 2024/3/6 17:26
 */
public interface TenantLoader {

    /**
     * 获取租户ID
     * @return
     */
    String getTenantId();

    /**
     * 获取租户ID表达式
     * @return
     */
    Expression getTenantExpression();


    /**
     * 排序
     * @return
     */
    default int getSort() {
        return Integer.MAX_VALUE;
    }




    /**
     * 无任何实际用途，单独用来标识不需要被多租户拦截器拦截
     */
    class IgnoreExpression implements Expression {

        @Serial
        private static final long serialVersionUID = 6060176838032184357L;

        @Override
        public <T, S> T accept(ExpressionVisitor<T> expressionVisitor, S context) {
            return null;
        }

        @Override
        public SimpleNode getASTNode() {
            return null;
        }

        @Override
        public void setASTNode(SimpleNode node) {

        }
    }

}
