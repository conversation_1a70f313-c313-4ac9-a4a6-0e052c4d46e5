package com.wftk.mybatis.spring.boot.autoconfigure.plugin.tenant.thread;

import com.wftk.mybatis.spring.boot.autoconfigure.plugin.tenant.loader.TenantLoader;
import org.springframework.core.task.TaskDecorator;

/**
 * <AUTHOR>
 * @create 2024/3/11 19:29
 */
public class TenantTaskDecorator implements TaskDecorator {


    private final TenantLoader tenantLoader;

    public TenantTaskDecorator(TenantLoader tenantLoader) {
        this.tenantLoader = tenantLoader;
    }

    @Override
    public Runnable decorate(Runnable runnable) {
        return new TenantRunnable(tenantLoader.getTenantId(), runnable);
    }
}
