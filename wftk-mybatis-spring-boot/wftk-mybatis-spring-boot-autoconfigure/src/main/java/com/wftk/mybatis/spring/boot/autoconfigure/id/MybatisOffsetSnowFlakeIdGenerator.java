package com.wftk.mybatis.spring.boot.autoconfigure.id;

import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.wftk.mybatis.spring.boot.autoconfigure.id.generator.IdGenerator;

/**
 * <AUTHOR>
 * @create 2023/8/3 11:10
 */
public class MybatisOffsetSnowFlakeIdGenerator implements IdentifierGenerator {

    private final IdGenerator idGenerator;

    public MybatisOffsetSnowFlakeIdGenerator(IdGenerator idGenerator) {
        this.idGenerator = idGenerator;
    }

    @Override
    public Number nextId(Object entity) {
        return idGenerator.nextId();
    }

}
