package com.wftk.mybatis.spring.boot.autoconfigure.plugin.tenant.loader;

import cn.hutool.core.util.StrUtil;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;

import java.util.Optional;

/**
 * 临时租户加载器(优先级最高，可以在此设置忽略租户，或者提前设置额外的值用于脱离后续的租户加载器)
 * <AUTHOR>
 * @create 2024/3/11 19:54
 */
public class TmpTenantLoader implements TenantLoader {

    private static final ThreadLocal<TenantInfo> THREAD_LOCAL = new ThreadLocal<>();

    @Override
    public String getTenantId() {
        return Optional.ofNullable(THREAD_LOCAL.get())
                .map(TenantInfo::getTenantId)
                .orElse(null);
    }

    @Override
    public Expression getTenantExpression() {
        TenantInfo tenantInfo = THREAD_LOCAL.get();
        if (tenantInfo == null) {
            return null;
        }
        if (tenantInfo.isIgnore()) {
            //忽略租户
            return new IgnoreExpression();
        }
        String clientId = tenantInfo.getTenantId();
        if (StrUtil.isNotBlank(clientId)) {
            return new StringValue(clientId);
        }
        return null;
    }

    @Override
    public int getSort() {
        return -10000;
    }


    /**
     * 设置租户ID
     * @param tenantId
     * @return
     */
    public static synchronized void setTenantId(String tenantId) {
        if (StrUtil.isBlank(tenantId)) {
            return;
        }
        TenantInfo tenantInfo = THREAD_LOCAL.get();
        if (tenantInfo == null) {
            tenantInfo = new TenantInfo();
        }
        tenantInfo.setTenantId(tenantId);
        THREAD_LOCAL.set(tenantInfo);
    }


    /**
     * 设置是否忽略租户
     * @param ignore
     */
    public static synchronized void ignoreTenant(boolean ignore) {
        TenantInfo tenantInfo = THREAD_LOCAL.get();
        if (tenantInfo == null) {
            tenantInfo = new TenantInfo();
        }
        tenantInfo.setIgnore(ignore);
        THREAD_LOCAL.set(tenantInfo);
    }


    public static TenantInfo getTenantInfo() {
        return THREAD_LOCAL.get();
    }



    /**
     * 重置
     */
    public static synchronized void reset() {
        THREAD_LOCAL.remove();
    }






    /**
     * 租户信息
     */
    public static class TenantInfo {

        private String tenantId;

        /**
         * 是否忽略租户
         */
        private boolean ignore = false;

        /**
         * 限制实例化范围
         */
        TenantInfo() {

        }


        public String getTenantId() {
            return tenantId;
        }

        public void setTenantId(String tenantId) {
            this.tenantId = tenantId;
        }

        public boolean isIgnore() {
            return ignore;
        }

        public void setIgnore(boolean ignore) {
            this.ignore = ignore;
        }
    }

}
