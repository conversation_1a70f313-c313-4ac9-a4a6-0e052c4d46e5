
package com.wftk.mybatis.spring.boot.autoconfigure.core.audit;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.wftk.mybatis.spring.boot.autoconfigure.core.audit.loader.AuditUserLoader;
import com.wftk.mybatis.spring.boot.autoconfigure.properties.MybatisProperties;
import org.apache.ibatis.reflection.MetaObject;

import java.time.LocalDateTime;


public class MybatisAuditMetaObjectHandler implements MetaObjectHandler {

    private final MybatisProperties.AuditProperties auditProperties;
    private final AuditUserLoader auditUserLoader;

    public MybatisAuditMetaObjectHandler(MybatisProperties.AuditProperties auditProperties, AuditUserLoader auditUserLoader) {
        this.auditProperties = auditProperties;
        this.auditUserLoader = auditUserLoader;
    }

    
    @Override
    public void insertFill(MetaObject metaObject) {
        LocalDateTime nowTime = LocalDateTime.now();
        if (metaObject.hasSetter(auditProperties.getCreateTimePropertyName())) {
            this.setFieldValByName(auditProperties.getCreateTimePropertyName(), nowTime,metaObject);
        }
        if (metaObject.hasSetter(auditProperties.getUpdateTimePropertyName())) {
            this.setFieldValByName(auditProperties.getUpdateTimePropertyName(), nowTime,metaObject);
        }
        if (auditUserLoader == null) {
            return;
        }
        AuditUser auditUser = auditUserLoader.getAuditUser();
        if (auditUser == null) {
            return;
        }
        if (metaObject.hasSetter(auditProperties.getCreateByPropertyName())) {
            Class<?> setterType = metaObject.getSetterType(auditProperties.getCreateByPropertyName());
            if (setterType.isAssignableFrom(String.class)) {
                this.setFieldValByName(auditProperties.getCreateByPropertyName(), auditUser.getId().toString(),metaObject);
            } else {
                this.setFieldValByName(auditProperties.getCreateByPropertyName(), auditUser.getId(),metaObject);
            }
        }
        if (metaObject.hasSetter(auditProperties.getUpdateByPropertyName())) {
            Class<?> setterType = metaObject.getSetterType(auditProperties.getUpdateByPropertyName());
            if (setterType.isAssignableFrom(String.class)) {
                this.setFieldValByName(auditProperties.getUpdateByPropertyName(), auditUser.getId().toString(),metaObject);
            } else {
                this.setFieldValByName(auditProperties.getUpdateByPropertyName(), auditUser.getId(),metaObject);
            }
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        if (metaObject.hasSetter(auditProperties.getUpdateTimePropertyName())) {
            this.setFieldValByName(auditProperties.getUpdateTimePropertyName(), LocalDateTime.now(),metaObject);
        }
        if (auditUserLoader == null) {
            return;
        }
        AuditUser auditUser = auditUserLoader.getAuditUser();
        if (auditUser == null) {
            return;
        }
        if (metaObject.hasSetter(auditProperties.getUpdateByPropertyName())) {
            Class<?> setterType = metaObject.getSetterType(auditProperties.getUpdateByPropertyName());
            if (setterType.isAssignableFrom(String.class)) {
                this.setFieldValByName(auditProperties.getUpdateByPropertyName(), auditUser.getId().toString(),metaObject);
            } else {
                this.setFieldValByName(auditProperties.getUpdateByPropertyName(), auditUser.getId(),metaObject);
            }
        }
    }

}
