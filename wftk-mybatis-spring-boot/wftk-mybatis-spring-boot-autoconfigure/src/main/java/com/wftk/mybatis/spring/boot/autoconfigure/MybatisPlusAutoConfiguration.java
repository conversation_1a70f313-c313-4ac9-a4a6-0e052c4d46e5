package com.wftk.mybatis.spring.boot.autoconfigure;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.github.yitter.contract.IdGeneratorOptions;
import com.github.yitter.idgen.YitIdHelper;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.mybatis.spring.boot.autoconfigure.core.audit.MybatisAuditMetaObjectHandler;
import com.wftk.mybatis.spring.boot.autoconfigure.core.audit.loader.AuditUserLoader;
import com.wftk.mybatis.spring.boot.autoconfigure.core.audit.loader.DefaultAuditUserLoader;
import com.wftk.mybatis.spring.boot.autoconfigure.id.MybatisOffsetSnowFlakeIdGenerator;
import com.wftk.mybatis.spring.boot.autoconfigure.id.WorkerIdGenerator;
import com.wftk.mybatis.spring.boot.autoconfigure.id.generator.DefaultSnowflakeGenerator;
import com.wftk.mybatis.spring.boot.autoconfigure.id.generator.IdGenerator;
import com.wftk.mybatis.spring.boot.autoconfigure.plugin.tenant.DefaultTenantLineHandler;
import com.wftk.mybatis.spring.boot.autoconfigure.plugin.tenant.loader.*;
import com.wftk.mybatis.spring.boot.autoconfigure.plugin.tenant.thread.TenantTaskDecorator;
import com.wftk.mybatis.spring.boot.autoconfigure.properties.MybatisProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.task.TaskDecorator;
import org.springframework.core.task.support.CompositeTaskDecorator;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/8/3 13:59
 */
@Configuration
@EnableConfigurationProperties(MybatisProperties.class)
public class MybatisPlusAutoConfiguration {

    @AutoConfigureBefore(com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.class)
    @ConditionalOnClass(com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.class)
    public static class AutoConfiguration {

        @Bean
        IdentifierGenerator identifierGenerator(IdGenerator idGenerator) {
            return new MybatisOffsetSnowFlakeIdGenerator(idGenerator);
        }



        @Bean
        @ConditionalOnMissingBean
        BlockAttackInnerInterceptor blockAttackInnerInterceptor() {
            return new BlockAttackInnerInterceptor();
        }


        @Bean
        @ConditionalOnMissingBean
        MybatisPlusInterceptor mybatisPlusInterceptor(List<InnerInterceptor> interceptors) {
            MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();
            if (CollectionUtil.isEmpty(interceptors)) {
                return mybatisPlusInterceptor;
            }
            interceptors.forEach(mybatisPlusInterceptor::addInnerInterceptor);
            return mybatisPlusInterceptor;
        }
    }



    @Bean
    IdGenerator idGenerator() {
        IdGeneratorOptions options = new IdGeneratorOptions(WorkerIdGenerator.getWorkerId());
        YitIdHelper.setIdGenerator(options);
        return new DefaultSnowflakeGenerator(YitIdHelper.getIdGenInstance());
    }


    @Configuration
    public static class AuditConfiguration {

        @ConditionalOnClass(AuthenticationHolder.class)
        @Bean
        AuditUserLoader auditUserLoader() {
            return new DefaultAuditUserLoader();
        }

        @Bean
        @ConditionalOnMissingBean
        MetaObjectHandler metaObjectHandler(MybatisProperties mybatisProperties, @Autowired(required = false) AuditUserLoader auditUserLoader) {
            return new MybatisAuditMetaObjectHandler(mybatisProperties.getAudit(), auditUserLoader);
        }

    }





    @ConditionalOnProperty(name = "config.mybatis.tenant.enabled", havingValue = "true")
    static class TenantPluginConfiguration {

        @Bean
        @ConditionalOnMissingBean
        TenantLineInnerInterceptor tenantLineInnerInterceptor(MybatisProperties mybatisProperties, DelegatingTenantLoader delegatingTenantLoader) {
            DefaultTenantLineHandler tenantLineHandler = new DefaultTenantLineHandler(mybatisProperties.getTenant(), delegatingTenantLoader);
            return new TenantLineInnerInterceptor(tenantLineHandler);
        }


        @Primary
        @Bean
        DelegatingTenantLoader tenantLoader(List<TenantLoader> tenantLoaders) {
            return new DelegatingTenantLoader(tenantLoaders);
        }

        @Bean
        @ConditionalOnClass(ServletRequestAttributes.class)
        @ConditionalOnProperty(name = "config.mybatis.tenant.http.enabled", havingValue = "true", matchIfMissing = true)
        @ConditionalOnMissingBean
        HttpRequestTenantLoader httpRequestTenantLoader(MybatisProperties mybatisProperties) {
            MybatisProperties.TenantHttpLoaderProperties http = mybatisProperties.getTenant().getHttp();
            return new HttpRequestTenantLoader(http.getTenantName());
        }


        @Bean
        TmpTenantLoader tmpTenantLoader() {
            return new TmpTenantLoader();
        }

        @Bean
        ThreadLocalTenantLoader threadLocalTenantLoader() {
            return new ThreadLocalTenantLoader();
        }


        @Bean
        TenantTaskDecorator tenantTaskDecorator(DelegatingTenantLoader delegatingTenantLoader) {
            return new TenantTaskDecorator(delegatingTenantLoader);
        }

        @Bean
        @Primary
        CompositeTaskDecorator compositeTaskDecorator(List<TaskDecorator> taskDecorators) {
            return new CompositeTaskDecorator(taskDecorators);
        }

    }
}
