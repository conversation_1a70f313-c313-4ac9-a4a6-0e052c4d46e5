package com.wftk.mybatis.spring.boot.autoconfigure.plugin.tenant;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.wftk.mybatis.spring.boot.autoconfigure.plugin.tenant.loader.TenantLoader;
import com.wftk.mybatis.spring.boot.autoconfigure.properties.MybatisProperties;
import net.sf.jsqlparser.expression.Expression;

import java.util.List;


/**
 * <AUTHOR>
 * @create 2024/3/6 17:13
 */
public class DefaultTenantLineHandler implements TenantLineHandler {

    private final MybatisProperties.TenantProperties tenantProperties;
    private final TenantLoader tenantLoader;

    public DefaultTenantLineHandler(MybatisProperties.TenantProperties tenantProperties, TenantLoader tenantLoader) {
        this.tenantProperties = tenantProperties;
        this.tenantLoader = tenantLoader;
    }


    @Override
    public Expression getTenantId() {
        return tenantLoader.getTenantExpression();
    }

    @Override
    public String getTenantIdColumn() {
        return tenantProperties.getTenantColumnName();
    }

    @Override
    public boolean ignoreTable(String tableName) {
        Expression tenantExpression = tenantLoader.getTenantExpression();
        if (!tenantProperties.isAllowNullTenant() && tenantExpression == null) {
            throw new IllegalArgumentException("tenant must not be null.");
        }
        //如果没有租户相关的值或者标注为忽略的，则直接忽略
        if (tenantExpression == null || tenantExpression instanceof TenantLoader.IgnoreExpression) {
            return true;
        }
        List<String> ignoreTables = tenantProperties.getIgnoreTables();
        if (CollectionUtil.isEmpty(ignoreTables)) {
            return false;
        }
        return tenantProperties.getIgnoreTables().contains(tableName);
    }

}
