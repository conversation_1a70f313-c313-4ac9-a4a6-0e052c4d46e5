package com.wftk.mybatis.spring.boot.autoconfigure.plugin.tenant.thread;

import cn.hutool.core.util.StrUtil;
import com.wftk.mybatis.spring.boot.autoconfigure.plugin.tenant.loader.ThreadLocalTenantLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @create 2024/3/11 19:46
 */
public class TenantRunnable implements Runnable {

    private static final Logger LOGGER = LoggerFactory.getLogger(TenantRunnable.class);
    private final String tenantId;
    private final Runnable runnable;

    public TenantRunnable(String tenantId, Runnable runnable) {
        this.tenantId = tenantId;
        this.runnable = runnable;
    }

    @Override
    public void run() {
        try {
            if (StrUtil.isNotBlank(tenantId)) {
                LOGGER.debug("current tenantId: {}", tenantId);
                ThreadLocalTenantLoader.setTenantId(tenantId);
            }
            runnable.run();
        } finally {
            ThreadLocalTenantLoader.reset();
        }
    }
}
