package com.wftk.mybatis.spring.boot.autoconfigure.plugin.tenant;

import cn.hutool.core.util.StrUtil;
import com.wftk.mybatis.spring.boot.autoconfigure.plugin.tenant.loader.TmpTenantLoader;

/**
 * <AUTHOR>
 * @create 2024/3/14 14:39
 */
public class TenantExecutor {


    /**
     * 指定
     * @param tenantId
     * @param execution
     */
    public static <T> T execute(String tenantId, Execution<T> execution) {
        if (StrUtil.isBlank(tenantId)) {
            throw new IllegalArgumentException("tenantId must not be null.");
        }
        TmpTenantLoader.setTenantId(tenantId);
        return execute(execution);
    }


    public static <T> T executeWithoutTenant(Execution<T> execution) {
        TmpTenantLoader.ignoreTenant(true);
        return execute(execution);
    }





    /**
     * 执行(临时调整ThreadLocal, 执行完毕后清除ThreadLocal)
     * @param execution
     */
    private static <T> T execute(Execution<T> execution) {
        try {
            return execution.execute();
        } finally {
            TmpTenantLoader.reset();
        }
    }




    @FunctionalInterface
    public interface Execution<T> {
        T execute();
    }

}
