package com.wftk.mybatis.spring.boot.autoconfigure.core.audit.loader;

import com.wftk.auth.spring.boot.autoconfigure.core.auth.Authentication;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.user.AuthUser;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.mybatis.spring.boot.autoconfigure.core.audit.AuditUser;

/**
 * <AUTHOR>
 * @create 2024/12/20 15:45
 */
public class DefaultAuditUserLoader implements AuditUserLoader {
    @Override
    public AuditUser getAuditUser() {
        Authentication authentication = AuthenticationHolder.getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return null;
        }
        AuthUser<?> authUser = authentication.getAuthUser();
        if (authUser == null) {
            return null;
        }
        AuditUser auditUser = new AuditUser();
        auditUser.setId(authUser.getId());
        return auditUser;
    }
}
