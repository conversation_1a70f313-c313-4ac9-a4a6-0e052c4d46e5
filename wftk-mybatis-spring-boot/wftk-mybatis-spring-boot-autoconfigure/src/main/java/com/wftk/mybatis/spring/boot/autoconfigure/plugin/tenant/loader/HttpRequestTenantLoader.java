package com.wftk.mybatis.spring.boot.autoconfigure.plugin.tenant.loader;

import cn.hutool.core.util.StrUtil;
import jakarta.servlet.http.HttpServletRequest;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 从http请求中获取租户信息
 * <AUTHOR>
 * @create 2024/3/6 18:27
 */
public class HttpRequestTenantLoader implements TenantLoader {

    private final String tenantName;

    public HttpRequestTenantLoader(String tenantName) {
        this.tenantName = tenantName;
    }

    @Override
    public String getTenantId() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return null;
        }
        HttpServletRequest request = requestAttributes.getRequest();
        return request.getParameter(tenantName);
    }

    @Override
    public Expression getTenantExpression() {
        String clientId = getTenantId();
        if (StrUtil.isNotBlank(clientId)) {
            return new StringValue(clientId);
        }
        return null;
    }

    @Override
    public int getSort() {
        return 0;
    }
}
