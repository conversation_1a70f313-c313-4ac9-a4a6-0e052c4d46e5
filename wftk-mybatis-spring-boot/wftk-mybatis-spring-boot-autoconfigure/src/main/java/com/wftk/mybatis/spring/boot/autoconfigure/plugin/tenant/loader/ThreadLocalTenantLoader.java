package com.wftk.mybatis.spring.boot.autoconfigure.plugin.tenant.loader;

import cn.hutool.core.util.StrUtil;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;

/**
 * <AUTHOR>
 * @create 2024/3/11 19:54
 */
public class ThreadLocalTenantLoader implements TenantLoader {

    private static final ThreadLocal<String> THREAD_LOCAL = new ThreadLocal<>();

    @Override
    public String getTenantId() {
        return THREAD_LOCAL.get();
    }

    @Override
    public Expression getTenantExpression() {
        String clientId = getTenantId();
        if (StrUtil.isNotBlank(clientId)) {
            return new StringValue(clientId);
        }
        return null;
    }

    @Override
    public int getSort() {
        return -100;
    }


    /**
     * 设置租户ID
     * @param tenantId
     * @return
     */
    public static void setTenantId(String tenantId) {
        THREAD_LOCAL.set(tenantId);
    }

    /**
     * 重置
     */
    public static void reset() {
        THREAD_LOCAL.remove();
    }

}
