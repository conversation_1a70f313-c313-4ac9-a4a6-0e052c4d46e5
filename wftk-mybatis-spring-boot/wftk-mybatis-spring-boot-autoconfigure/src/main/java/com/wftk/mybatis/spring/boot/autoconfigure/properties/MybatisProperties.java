package com.wftk.mybatis.spring.boot.autoconfigure.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/3/6 17:14
 */
@ConfigurationProperties(prefix = "config.mybatis")
public class MybatisProperties {

    /**
     * 多租户相关配置
     */
    @NestedConfigurationProperty
    private TenantProperties tenant = new TenantProperties();

    /**
     * 审计相关
     */
    @NestedConfigurationProperty
    private AuditProperties audit = new AuditProperties();


    public TenantProperties getTenant() {
        return tenant;
    }

    public void setTenant(TenantProperties tenant) {
        this.tenant = tenant;
    }

    public AuditProperties getAudit() {
        return audit;
    }

    public void setAudit(AuditProperties audit) {
        this.audit = audit;
    }

    /**
     * 租户相关配置
     */
    public static class TenantProperties {

        /**
         * 是否开启
         */
        private boolean enabled = true;

        /**
         * 是否允许未携带租户信息
         */
        private boolean allowNullTenant = true;

        /**
         * 忽略表
         */
        private List<String> ignoreTables;

        /**
         * 从http中获取租户ID的配置
         */
        @NestedConfigurationProperty
        private TenantHttpLoaderProperties http = new TenantHttpLoaderProperties();

        /**
         * 数据库表中的租户字段名
         */
        private String tenantColumnName = "client_id";


        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public List<String> getIgnoreTables() {
            return ignoreTables;
        }

        public void setIgnoreTables(List<String> ignoreTables) {
            this.ignoreTables = ignoreTables;
        }

        public String getTenantColumnName() {
            return tenantColumnName;
        }

        public void setTenantColumnName(String tenantColumnName) {
            this.tenantColumnName = tenantColumnName;
        }

        public TenantHttpLoaderProperties getHttp() {
            return http;
        }

        public void setHttp(TenantHttpLoaderProperties http) {
            this.http = http;
        }

        public boolean isAllowNullTenant() {
            return allowNullTenant;
        }

        public void setAllowNullTenant(boolean allowNullTenant) {
            this.allowNullTenant = allowNullTenant;
        }
    }



    public static class TenantHttpLoaderProperties {
        private boolean enabled = true;
        private String tenantName = "client_id";

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getTenantName() {
            return tenantName;
        }

        public void setTenantName(String tenantName) {
            this.tenantName = tenantName;
        }
    }


    /**
     * 审计字段配置
     */
    public static class AuditProperties {

        private String createTimePropertyName = "createTime";
        private String updateTimePropertyName = "updateTime";
        private String createByPropertyName = "createBy";
        private String updateByPropertyName = "updateBy";

        public String getCreateTimePropertyName() {
            return createTimePropertyName;
        }

        public void setCreateTimePropertyName(String createTimePropertyName) {
            this.createTimePropertyName = createTimePropertyName;
        }

        public String getUpdateTimePropertyName() {
            return updateTimePropertyName;
        }

        public void setUpdateTimePropertyName(String updateTimePropertyName) {
            this.updateTimePropertyName = updateTimePropertyName;
        }

        public String getCreateByPropertyName() {
            return createByPropertyName;
        }

        public void setCreateByPropertyName(String createByPropertyName) {
            this.createByPropertyName = createByPropertyName;
        }

        public String getUpdateByPropertyName() {
            return updateByPropertyName;
        }

        public void setUpdateByPropertyName(String updateByPropertyName) {
            this.updateByPropertyName = updateByPropertyName;
        }
    }

}
