package com.wftk.mybatis.spring.boot.autoconfigure.id.generator;

import com.github.yitter.contract.IIdGenerator;

/**
 * <AUTHOR>
 * @create 2023/8/10 13:53
 */
public class DefaultSnowflakeGenerator implements IdGenerator {

    private final IIdGenerator idGenerator;

    public DefaultSnowflakeGenerator(IIdGenerator idGenerator) {
        this.idGenerator = idGenerator;
    }

    @Override
    public long nextId() {
        return idGenerator.newLong();
    }

    @Override
    public String nextIdStr() {
        return String.valueOf(nextId());
    }
}
