<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wftk</groupId>
        <artifactId>wftk-starters</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>wftk-mybatis-spring-boot</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>wftk-mybatis-spring-boot-autoconfigure</module>
        <module>wftk-mybatis-spring-boot-starter</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <mybatis-plus.version>3.5.8</mybatis-plus.version>
        <yitter-idgenerator.version>1.0.6</yitter-idgenerator.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.wftk</groupId>
                <artifactId>wftk-auth-spring-boot-starter</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.baomidou/mybatis-plus-spring-boot3-starter -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.github.yitter/yitter-idgenerator -->
            <dependency>
                <groupId>com.github.yitter</groupId>
                <artifactId>yitter-idgenerator</artifactId>
                <version>${yitter-idgenerator.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

</project>