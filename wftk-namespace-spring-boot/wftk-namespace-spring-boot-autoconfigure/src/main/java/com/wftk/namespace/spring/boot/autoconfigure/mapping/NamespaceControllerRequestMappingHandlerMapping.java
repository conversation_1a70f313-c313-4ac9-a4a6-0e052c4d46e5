package com.wftk.namespace.spring.boot.autoconfigure.mapping;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.wftk.namespace.spring.boot.autoconfigure.annotation.NamespaceRequestMapping;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.lang.reflect.Method;
import java.util.Set;

/**
 * 针对namespace相关的注解给请求路径添加namespace
 * @Schedule
 * <AUTHOR>
 * @create 2022/4/21 13:34
 */
public class NamespaceControllerRequestMappingHandlerMapping extends RequestMappingHandlerMapping {

    @Override
    protected void registerHandlerMethod(Object handler, Method method, RequestMappingInfo mapping) {
        HandlerMethod handlerMethod = createHandlerMethod(handler, method);
        Class<?> beanType = handlerMethod.getBeanType();
        NamespaceRequestMapping namespaceRequestMapping = AnnotatedElementUtils.findMergedAnnotation(beanType, NamespaceRequestMapping.class);
        if (namespaceRequestMapping != null && StrUtil.isNotBlank(namespaceRequestMapping.namespace())) {
            super.registerHandlerMethod(handler, method, withNamespace(mapping, namespaceRequestMapping.namespace()));
        } else {
            super.registerHandlerMethod(handler, method, mapping);
        }
    }

    private RequestMappingInfo withNamespace(RequestMappingInfo mapping, String namespace) {
        String[] patterns = withNamespacePatterns(mapping.getPatternValues(), namespace);
        return mapping.mutate().paths(patterns).build();
    }

    private String[] withNamespacePatterns(Set<String> patterns, String namespace) {
        if (namespace.endsWith("/")) {
            namespace = namespace.substring(0, namespace.length() - 1);
        }
        String resolvedNamespace = namespace;
        return patterns.stream().map((pattern) -> URLUtil.getPath(resolvedNamespace + pattern))
                .toArray(String[]::new);
    }
}
