package com.wftk.oauth.client.core.factory;

import com.wftk.common.core.registry.DefaultObjectRegistry;
import com.wftk.oauth.client.core.http.HttpExecutor;

/**
 * <AUTHOR>
 * @create 2024/2/23 14:07
 */
public class DefaultHttpExecutorFactory extends DefaultObjectRegistry<String, HttpExecutor<?>> implements HttpExecutorFactory {
    @Override
    public HttpExecutor<?> create(String apiProviderName) {
        return null;
    }
}
