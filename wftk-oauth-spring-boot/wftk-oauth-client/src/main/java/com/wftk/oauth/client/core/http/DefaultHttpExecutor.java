package com.wftk.oauth.client.core.http;

/**
 * <AUTHOR>
 * @create 2024/2/23 17:57
 */
public class DefaultHttpExecutor<SDK> implements HttpExecutor<SDK> {

    private final HttpApiExecutor httpApiExecutor;
    private final SDK sdk;

    public DefaultHttpExecutor(HttpApiExecutor httpApiExecutor, SDK sdk) {
        this.httpApiExecutor = httpApiExecutor;
        this.sdk = sdk;
    }

    @Override
    public HttpApiExecutor getApiExecutor() {
        return httpApiExecutor;
    }

    @Override
    public SDK getSdk() {
        return sdk;
    }
}
