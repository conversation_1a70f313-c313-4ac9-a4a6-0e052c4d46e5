package com.wftk.oauth.client.core.login.token;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2024/2/21 18:34
 */
@Data
@Builder
public class TokenInfo {

    private String openId;

    private String accessToken;

    /**
     * accessToken过期时间(单位：毫秒)
     */
    private Long expireInTimeMills;


    private String refreshToken;

    /**
     * refreshToken过期时间(单位：毫秒)
     */
    private Long refreshExpireInTimeMills;

}
