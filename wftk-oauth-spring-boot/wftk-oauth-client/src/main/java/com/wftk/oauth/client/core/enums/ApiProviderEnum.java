package com.wftk.oauth.client.core.enums;


import lombok.Getter;

import java.util.Optional;

/**
 * <AUTHOR>
 * @create 2024/2/23 14:09
 */
public enum ApiProviderEnum {

    ALIPAY("ALIPAY", "支付宝");

    @Getter
    private String providerName;
    private String label;

    ApiProviderEnum(String providerName, String label) {
        this.providerName = providerName;
        this.label = label;
    }

    public static Optional<ApiProviderEnum> valueOfName(String providerName) {
        for (ApiProviderEnum apiProviderEnum : ApiProviderEnum.values()) {
            if (apiProviderEnum.getProviderName().equals(providerName)) {
                return Optional.of(apiProviderEnum);
            }
        }
        return Optional.empty();
    }
}
