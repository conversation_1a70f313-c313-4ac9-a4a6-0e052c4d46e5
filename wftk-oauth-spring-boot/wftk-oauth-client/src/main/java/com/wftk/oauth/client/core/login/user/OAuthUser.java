package com.wftk.oauth.client.core.login.user;

import com.wftk.oauth.client.core.enums.GenderEnum;

/**
 * <AUTHOR>
 * @create 2024/2/21 17:59
 */
public interface OAuthUser {

    /**
     * 获取用户唯一标识
     * @return
     */
    String getOpenId();

    /**
     * 获取用户昵称
     * @return
     */
    String getNickName();


    /**
     * 获取用户头像
     * @return
     */
    String getAvatar();


    /**
     * 获取用户地址
     * @return
     */
    String getLocation();


    /**
     * 获取性别
     * @return
     */
    GenderEnum getGender();

}
