package com.wftk.oauth.client.core.request;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.StrUtil;
import com.wftk.oauth.client.core.api.ApiInfo;
import com.wftk.oauth.client.core.config.CacheConfig;
import com.wftk.oauth.client.core.config.OAuthConfig;
import com.wftk.oauth.client.core.config.RequestConfig;
import com.wftk.oauth.client.core.exception.OAuthException;
import com.wftk.oauth.client.core.factory.HttpExecutorFactory;
import com.wftk.oauth.client.core.login.LoginRequestInfo;
import com.wftk.oauth.client.core.login.token.TokenInfo;

import java.net.URI;

/**
 * <AUTHOR>
 * @create 2024/2/21 18:49
 */
public abstract class BaseOAuthRequest implements OAuthRequest {

    protected final OAuthConfig oAuthConfig;
    protected final RequestConfig requestConfig;

    protected final CacheConfig.StateCacheConfig stateCacheConfig;

    protected final ApiInfo apiInfo;

    protected final HttpExecutorFactory httpExecutorFactory;

    public BaseOAuthRequest(OAuthConfig oAuthConfig, RequestConfig requestConfig, CacheConfig.StateCacheConfig stateCacheConfig,
                            ApiInfo apiInfo, HttpExecutorFactory httpExecutorFactory) {
        this.oAuthConfig = oAuthConfig;
        this.requestConfig = requestConfig;
        this.stateCacheConfig = stateCacheConfig;
        this.apiInfo = apiInfo;
        this.httpExecutorFactory = httpExecutorFactory;
    }

    @Override
    public URI authorize(String state) {
        UrlBuilder urlBuilder = UrlBuilder.of(apiInfo.getAuthorizeApi())
                .addQuery("app_id", oAuthConfig.getAppId())
                .addQuery("redirect_uri", oAuthConfig.getRedirectUri())
                .addQuery("response_type", "code")
                .addQuery("state", state);
        if (CollectionUtil.isNotEmpty(oAuthConfig.getScope())) {
            urlBuilder.addQuery("scope", StrUtil.join(",", oAuthConfig.getScope()));
        }
        stateCacheConfig.getStateCache().setState(state, state, stateCacheConfig.getExpireIn(), stateCacheConfig.getTimeUnit());
        return urlBuilder.toURI();
    }


    @Override
    public TokenInfo getAccessToken(LoginRequestInfo loginRequestInfo) {
        if (StrUtil.isBlank(loginRequestInfo.getState())
                || StrUtil.isBlank(stateCacheConfig.getStateCache().get(loginRequestInfo.getState()))) {
            throw new OAuthException("invalid state.");
        }
        return doGetAccessToken(loginRequestInfo);
    }

    protected abstract TokenInfo doGetAccessToken(LoginRequestInfo loginRequestInfo);

}
