package com.wftk.oauth.client.core.enums;

import com.wftk.common.core.enums.BaseEnum;

/**
 * <AUTHOR>
 * @create 2024/2/21 18:08
 */
public enum GenderEnum implements BaseEnum {
    UNKNOWN(0, "未知"), MALE(1, "男"), FEMALE(2, "女");

    private Integer value;
    private String label;
    GenderEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
