package com.wftk.oauth.client.core.factory;


import com.wftk.common.core.registry.ObjectRegistry;
import com.wftk.oauth.client.core.http.HttpExecutor;


/**
 * <AUTHOR>
 * @create 2024/2/23 13:51
 */
public interface HttpExecutorFactory extends ObjectRegistry<String, HttpExecutor<?>> {

    /**
     * 创建HttpExecutor
     * @param apiProviderName
     * @return
     */
    HttpExecutor<?> create(String apiProviderName);


    /**
     * 获取Executor
     * @param apiProviderName
     * @return
     */
    default HttpExecutor<?> getExecutor(String apiProviderName) {
        return get(apiProviderName).orElseGet(() -> {
            HttpExecutor<?> httpExecutor = create(apiProviderName);
            if (httpExecutor == null) {
                throw new RuntimeException("httpExecutor must not be null.");
            }
            register(apiProviderName, httpExecutor);
            return httpExecutor;
        });
    }
}
