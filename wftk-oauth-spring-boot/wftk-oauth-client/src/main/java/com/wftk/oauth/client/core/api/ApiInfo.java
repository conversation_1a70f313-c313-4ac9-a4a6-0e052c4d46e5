package com.wftk.oauth.client.core.api;

/**
 * <AUTHOR>
 * @create 2024/2/22 14:00
 */
public interface ApiInfo {

    /**
     * 获取API服务方名称
     * @return
     */
    String getProvider();


    /**
     * 获取授权地址
     * @return
     */
    String getAuthorizeApi();

    /**
     * 获取AccessToken的API地址
     * @return
     */
    String getAccessTokenApi();

    /**
     * 获取刷新AccessToken的API地址
     * @return
     */
    String getRefreshTokenApi();

    /**
     * 获取用户信息的API地址
     * @return
     */
    String getUserInfoApi();
}
