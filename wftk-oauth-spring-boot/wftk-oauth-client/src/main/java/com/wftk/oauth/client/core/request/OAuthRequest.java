package com.wftk.oauth.client.core.request;

import cn.hutool.core.util.IdUtil;
import com.wftk.oauth.client.core.login.LoginRequestInfo;
import com.wftk.oauth.client.core.login.token.TokenInfo;
import com.wftk.oauth.client.core.login.user.OAuthUser;

import java.net.URI;

/**
 * <AUTHOR>
 * @create 2024/2/21 17:44
 */
public interface OAuthRequest {

    /**
     * 返回授权地址
     * @return
     */
    default URI authorize() {
        return authorize(IdUtil.fastSimpleUUID());
    }


    /**
     * 返回授权地址
     * @param state
     * @return
     */
    URI authorize(String state);


    /**
     * 根据回调信息获取accessToken
     * @param loginRequestInfo
     * @return
     */
    TokenInfo getAccessToken(LoginRequestInfo loginRequestInfo);


    /**
     * 刷新accessToken
     * @param refreshToken
     * @return
     */
    TokenInfo refreshAccessToken(String refreshToken);


    /**
     * 获取用户信息
     * @param tokenInfo
     * @return
     */
    OAuthUser getUserInfo(TokenInfo tokenInfo);


    /**
     * 登录
     * @param loginRequestInfo
     * @return
     */
    default OAuthUser login(LoginRequestInfo loginRequestInfo) {
        TokenInfo accessToken = getAccessToken(loginRequestInfo);
        return getUserInfo(accessToken);
    }
}
