package com.wftk.oauth.client.core.state;

import java.util.concurrent.TimeUnit;

/**
 * OAuth的state字段缓存
 * <AUTHOR>
 * @create 2024/2/21 17:40
 */
public interface StateCache {

    /**
     * 缓存state
     * @param key
     * @param state
     * @param expireIn
     * @param timeUnit
     */
    void setState(String key, String state, long expireIn, TimeUnit timeUnit);


    /**
     * 获取缓存
     * @param key
     * @return
     */
    String get(String key);


    /**
     * 查看缓存是否存在
     * @param key
     * @return
     */
    boolean containsKey(String key);

    /**
     * 删除缓存
     * @param key
     */
    void remove(String key);
}
