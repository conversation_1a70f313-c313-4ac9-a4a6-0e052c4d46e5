package com.wftk.oauth.client.core.login.user;

import com.wftk.oauth.client.core.enums.GenderEnum;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2024/2/21 18:48
 */
@Data
@Builder
public class DefaultOAuth<PERSON>ser implements OAuthUser {

    private String openId;
    private String nickName;
    private String avatar;
    private String location;
    private GenderEnum gender;
}
