package com.wftk.oauth.client.core.config;

import com.wftk.oauth.client.core.state.StateCache;
import lombok.Data;

import java.util.concurrent.TimeUnit;

/**
 * 缓存配置
 * <AUTHOR>
 * @create 2024/2/23 10:43
 */
@Data
public class CacheConfig {

    private StateCacheConfig sateCacheConfig;


    @Data
    public static class StateCacheConfig {
        private StateCache stateCache;
        private Long expireIn;
        private TimeUnit timeUnit;
    }

}
