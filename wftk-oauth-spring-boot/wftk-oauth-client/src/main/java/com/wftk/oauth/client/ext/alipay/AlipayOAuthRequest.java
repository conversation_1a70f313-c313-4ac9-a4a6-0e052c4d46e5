package com.wftk.oauth.client.ext.alipay;

import cn.hutool.core.util.StrUtil;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.alipay.api.request.AlipayUserInfoShareRequest;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.alipay.api.response.AlipayUserInfoShareResponse;
import com.wftk.oauth.client.core.api.ApiInfo;
import com.wftk.oauth.client.core.config.CacheConfig;
import com.wftk.oauth.client.core.config.OAuthConfig;
import com.wftk.oauth.client.core.config.RequestConfig;
import com.wftk.oauth.client.core.enums.GenderEnum;
import com.wftk.oauth.client.core.exception.OAuthException;
import com.wftk.oauth.client.core.factory.HttpExecutorFactory;
import com.wftk.oauth.client.core.http.HttpExecutor;
import com.wftk.oauth.client.core.login.LoginRequestInfo;
import com.wftk.oauth.client.core.login.token.TokenInfo;
import com.wftk.oauth.client.core.login.user.DefaultOAuthUser;
import com.wftk.oauth.client.core.login.user.OAuthUser;
import com.wftk.oauth.client.core.request.BaseOAuthRequest;
import lombok.extern.slf4j.Slf4j;


/**
 * 支付宝OAuth2.0
 * <AUTHOR>
 * @create 2024/2/23 14:21
 */
@Slf4j
public class AlipayOAuthRequest extends BaseOAuthRequest {

    public AlipayOAuthRequest(OAuthConfig oAuthConfig, RequestConfig requestConfig, CacheConfig.StateCacheConfig stateCacheConfig,
                              ApiInfo apiInfo, HttpExecutorFactory httpExecutorFactory) {
        super(oAuthConfig, requestConfig, stateCacheConfig, apiInfo, httpExecutorFactory);
    }

    @Override
    protected TokenInfo doGetAccessToken(LoginRequestInfo loginRequestInfo) {
        return getOrRefreshAccessToken(loginRequestInfo.getCode(), false);
    }

    @Override
    public TokenInfo refreshAccessToken(String refreshToken) {
        return getOrRefreshAccessToken(refreshToken, true);
    }

    @Override
    public OAuthUser getUserInfo(TokenInfo tokenInfo) {
        AlipayUserInfoShareRequest request = new AlipayUserInfoShareRequest();
        AlipayUserInfoShareResponse response;
        try {
            response = getSdk().execute(request, tokenInfo.getAccessToken());
        } catch (AlipayApiException e) {
            throw new OAuthException(e);
        }
        if (!response.isSuccess()) {
            throw new OAuthException("get alipay userInfo failed. raw data: {" + response.getBody() + " }");
        }
        //默认使用userId, 如果需要新版的openId,则配置userOpenId为true即可
        String openId = response.getUserId();
        if (oAuthConfig.getOptions().containsKey("useOpenId") && (Boolean) oAuthConfig.getOptions().get("useOpenId")) {
            openId = response.getOpenId();
        }
        GenderEnum genderEnum = GenderEnum.UNKNOWN;
        if (StrUtil.isNotBlank(response.getGender())) {
            genderEnum = "M".equalsIgnoreCase(response.getGender()) ? GenderEnum.MALE : GenderEnum.FEMALE;
        }
        return DefaultOAuthUser.builder()
                .openId(openId)
                .avatar(response.getAvatar())
                .nickName(response.getNickName())
                .location(response.getProvince() + "-" + response.getCity())
                .gender(genderEnum)
                .build();
    }



    /**
     * 获取或者刷新access_token
     * @param param
     * @param isRefresh
     * @return
     */
    private TokenInfo getOrRefreshAccessToken(String param, boolean isRefresh) {
        // 构造请求参数以调用接口
        AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();

        if (isRefresh) {
            request.setRefreshToken(param);
            request.setGrantType("refresh_token");
        } else {
            // 设置授权码
            request.setCode(param);
            // 设置授权方式
            request.setGrantType("authorization_code");
        }

        AlipaySystemOauthTokenResponse response;
        try {
            response = getSdk().execute(request);
        } catch (AlipayApiException e) {
            throw new OAuthException(e);
        }
        if (!response.isSuccess()) {
            throw new OAuthException("get alipay access_token failed. raw data: {" + response.getBody() + " }");
        }
        //默认使用userId, 如果需要新版的openId,则配置userOpenId为true即可
        String openId = response.getUserId();
        if (oAuthConfig.getOptions().containsKey("useOpenId") && (Boolean) oAuthConfig.getOptions().get("useOpenId")) {
            openId = response.getOpenId();
        }
        return TokenInfo.builder()
                .accessToken(response.getAccessToken())
                .refreshToken(response.getRefreshToken())
                .expireInTimeMills(Long.parseLong(response.getExpiresIn()) * 1000)
                .refreshExpireInTimeMills(Long.parseLong(response.getReExpiresIn()) * 1000)
                .openId(openId)
                .build();
    }


    /**
     * 获取sdk
     * @return
     */
    private AlipayClient getSdk() {
        HttpExecutor<?> executor = httpExecutorFactory.getExecutor(apiInfo.getProvider());
        if (executor.getSdk() == null && executor.getSdk() instanceof AlipayClient) {
            throw new RuntimeException("illegal alipay sdk.");
        }
        return (AlipayClient) executor.getSdk();
    }
}
