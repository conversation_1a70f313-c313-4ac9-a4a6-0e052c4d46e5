package com.wftk.oauth.client.core.http;

import com.wftk.oauth.client.core.config.RequestConfig;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/2/23 17:52
 */
public interface HttpApiExecutor {

    /**
     * GET请求
     * @param url
     * @param params
     * @param requestConfig
     * @return
     * @param <R>
     */
    <R> R get(String url, Map<String, Object> params, RequestConfig requestConfig);

    /**
     * POST请求
     * @param url
     * @param params
     * @param bodyType
     * @param body
     * @param requestConfig
     * @return
     * @param <P>
     * @param <R>
     */
    <P, R> R post(String url, Map<String, Object> params, BodyType bodyType, P body, RequestConfig requestConfig);


    /**
     * PUT请求
     * @param url
     * @param params
     * @param bodyType
     * @param body
     * @param requestConfig
     * @return
     * @param <P>
     * @param <R>
     */
    <P, R> R put(String url, Map<String, Object> params, BodyType bodyType, P body, RequestConfig requestConfig);



    /**
     * 数据类型
     */
    enum BodyType {
        FORM, JSON;
    }
}
