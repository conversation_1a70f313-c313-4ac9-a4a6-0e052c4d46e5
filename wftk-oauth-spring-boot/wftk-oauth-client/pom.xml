<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wftk</groupId>
        <artifactId>wftk-oauth-spring-boot</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>wftk-oauth-client</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <alipay-sdk.version>4.38.212.ALL</alipay-sdk.version>
    </properties>

    <dependencies>
        <!-- 私有库 -->
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>common-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-http-client</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.wftk</groupId>
            <artifactId>wftk-signature</artifactId>
            <version>${project.version}</version>
        </dependency>


        <!-- 外部库 -->
        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>${alipay-sdk.version}</version>
        </dependency>
    </dependencies>

</project>