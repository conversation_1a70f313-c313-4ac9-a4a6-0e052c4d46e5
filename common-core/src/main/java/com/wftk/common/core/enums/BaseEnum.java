package com.wftk.common.core.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2022/8/17 10:24
 */
public interface BaseEnum {

    Integer getValue();

    String getLabel();

    static <E extends BaseEnum> E valueOf(Integer value, Class<E> clazz) {
        Objects.requireNonNull(value);
        for (E enumConstant : clazz.getEnumConstants()) {
            if (enumConstant.getValue().equals(value)) {
                return enumConstant;
            }
        }
        throw new IllegalArgumentException("illegal enum value: " + value);
    }
}
