package com.wftk.common.core.registry;

import com.wftk.common.core.store.InMemoryObjectStore;
import com.wftk.common.core.store.ObjectStore;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @create 2023/5/8 15:35
 */
public class DefaultObjectRegistry<K, V> implements ObjectRegistry<K, V> {

    private final ObjectStore<K, V> objectStore;

    public DefaultObjectRegistry() {
        this(new InMemoryObjectStore<>());
    }

    public DefaultObjectRegistry(ObjectStore<K, V> objectStore) {
        this.objectStore = objectStore;
    }

    @Override
    public boolean register(K k, V v) {
        return objectStore.save(k, v);
    }

    @Override
    public boolean register(Map<K, V> objects) {
        if (objects == null || objects.isEmpty()) {
            return false;
        }
        objects.forEach(objectStore::save);
        return true;
    }

    @Override
    public boolean register(ObjectRegistry<K, V> objectRegistry) {
        return register(objectRegistry.getAll());
    }

    @Override
    public Optional<V> get(K k) {
        return Optional.ofNullable(objectStore.getObject(k));
    }

    @Override
    public Map<K, V> getAll() {
        return objectStore.getAll();
    }

    @Override
    public boolean remove(K k) {
        return objectStore.remove(k);
    }

    @Override
    public boolean removeAll() {
        return objectStore.clear();
    }
}
