package com.wftk.common.core.request.matcher;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;

import java.util.Set;

/**
 * <AUTHOR>
 * @create 2023/2/6 17:21
 */
public class AntIgnoredRequestMatcher implements IgnoredRequestMatcher {

    private final AntPathMatcher antPathMatcher;
    private final Set<String> ignorePatterns;

    public AntIgnoredRequestMatcher(Set<String> ignorePatterns) {
        this.antPathMatcher = new AntPathMatcher();
        this.ignorePatterns = ignorePatterns;
    }

    @Override
    public boolean ignore(HttpServletRequest request) {
        if (CollectionUtils.isEmpty(ignorePatterns)) {
            return false;
        }
        String uri = request.getRequestURI();
        for (String pattern: ignorePatterns) {
            if (antPathMatcher.match(pattern, uri)) {
                return true;
            }
        }
        return false;
    }
}
