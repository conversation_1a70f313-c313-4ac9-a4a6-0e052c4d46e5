package com.wftk.common.core.registry;


import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @create 2023/5/8 15:25
 */
public interface ObjectRegistry<K, V> {

    /**
     * 注册
     * @param k
     * @param v
     * @return
     */
    boolean register(K k, V v);

    /**
     * 注册
     * @param objects
     * @return
     */
    boolean register(Map<K, V> objects);

    /**
     * 注册
     * @param objectRegistry
     * @return
     */
    boolean register(ObjectRegistry<K, V> objectRegistry);

    /**
     * 获取
     * @param k
     * @return
     */
    Optional<V> get(K k);


    /**
     * 获取全部
     * @return
     */
    Map<K, V> getAll();


    /**
     * 删除
     * @param k
     * @return
     */
    boolean remove(K k);

    /**
     * 删除所有
     * @return
     */
    boolean removeAll();
}
