package com.wftk.common.core.store;

/**
 * <AUTHOR>
 * @create 2022/1/25 13:58
 */
public interface ExpirationObjectStore<K, V> extends ObjectStore<K, V> {

    /**
     * 保存对象
     * @param k
     * @param v
     * @param expiredInSeconds
     * @return
     */
    boolean save(K k, V v, Long expiredInSeconds);


    /**
     * 获取剩余时间
     * @param k
     * @return
     */
    default Long ttl(K k) {
        throw new UnsupportedOperationException();
    }
}
