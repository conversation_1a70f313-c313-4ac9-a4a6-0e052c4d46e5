package com.wftk.common.core.request.wrapper;

import cn.hutool.core.io.IoUtil;
import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @create 2023/2/15 11:04
 */
public class ContentCachingRequestWrapper extends org.springframework.web.util.ContentCachingRequestWrapper {

    private byte[] content;

    public ContentCachingRequestWrapper(HttpServletRequest request) {
        super(request);
        //初始化参数
        super.getParameterMap();
        //form
        content = super.getContentAsByteArray();
        if (content.length == 0) {
            //非form
            try {
                content = IoUtil.readBytes(super.getInputStream(), true);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public ContentCachingRequestWrapper(HttpServletRequest request, byte[] content) {
        super(request);
        //初始化参数
        super.getParameterMap();
        this.content = content;
    }



    @Override
    public ServletInputStream getInputStream() throws IOException {
        return new CachingRequestInputStream(content);
    }



    /**
     *
     */
    public static class CachingRequestInputStream extends ServletInputStream {

        private final InputStream inputStream;

        public CachingRequestInputStream(byte[] content) {
            this.inputStream = new ByteArrayInputStream(content);
        }

        @Override
        public boolean isFinished() {
            try {
                return inputStream.available() == 0;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        @Override
        public boolean isReady() {
            return true;
        }

        @Override
        public void setReadListener(ReadListener listener) {

        }

        @Override
        public int read() throws IOException {
            return inputStream.read();
        }
    }
}
