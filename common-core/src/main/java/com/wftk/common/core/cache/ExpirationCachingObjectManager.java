package com.wftk.common.core.cache;

/**
 * <AUTHOR>
 * @create 2022/1/25 14:01
 */
public interface ExpirationCachingObjectManager<K, V> extends CachingObjectManager<K, V> {

    /**
     * 保存对象
     * @param k
     * @param v
     * @param expiredInSeconds
     * @return
     */
    boolean save(K k, V v, Long expiredInSeconds);


    /**
     * 获取剩余时间
     * @param k
     * @return
     */
    default Long getTTL(K k) {
        throw new UnsupportedOperationException();
    }
}
