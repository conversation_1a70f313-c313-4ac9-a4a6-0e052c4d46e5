package com.wftk.common.core.cache;

import java.util.Optional;

/**
 * 通用对象缓存管理器
 * @Author: ying.dong
 * @Date: 2021/10/13 10:36
 */
public interface CachingObjectManager<K, V> {

    /**
     * 保存对象
     * @param k
     * @param v
     * @return
     */
    boolean save(K k, V v);

    /**
     * 获取对象
     * @param k
     * @return
     */
    Optional<V> getObject(K k);

    /**
     * 删除对象
     * @param k
     * @return
     */
    boolean remove(K k);
}
