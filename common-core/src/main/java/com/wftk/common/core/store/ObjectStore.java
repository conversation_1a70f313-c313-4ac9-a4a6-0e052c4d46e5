package com.wftk.common.core.store;

import java.util.Map;

/**
 * 对象存储
 * @Author: ying.dong
 * @Date: 2021/10/13 10:52
 */
public interface ObjectStore<K, V> {

    /**
     * 保存对象
     * @param k
     * @param v
     * @return
     */
    boolean save(K k, V v);

    /**
     * 获取对象
     * @param k
     * @return
     */
    V getObject(K k);


    /**
     * 获取所有对象
     * @return
     */
    Map<K, V> getAll();


    /**
     * 删除对象
     * @param k
     * @return
     */
    boolean remove(K k);


    /**
     * 清空对象
     * @return
     */
    boolean clear();
}
