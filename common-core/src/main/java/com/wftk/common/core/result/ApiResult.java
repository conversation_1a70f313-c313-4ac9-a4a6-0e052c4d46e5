package com.wftk.common.core.result;


import java.io.Serial;
import java.io.Serializable;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 15:10
 */
public class ApiResult<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 2251319656486478070L;

    /**
     * 错误码
     *
     */
    private Integer code;
    /**
     * 返回数据
     */
    private T data;
    /**
     * 错误提示，用户可阅读
     *
     */
    private String message;

    private static final Integer DEFAULT_STATUS_OK = 200;
    private static final String DEFAULT_OK_MSG = "成功";

    public ApiResult() {
        this(null);
    }


    public ApiResult(T data) {
        this(DEFAULT_STATUS_OK, data, DEFAULT_OK_MSG);
    }

    public ApiResult(Integer code, T data, String message) {
        this.code = code;
        this.data = data;
        this.message = message;
    }

    public static <T> ApiResult<T> ok() {
        return new ApiResult<>(null);
    }

    public static <T> ApiResult<T> ok(T data) {
        return new ApiResult<>(data);
    }


    public static <T> ApiResult<T> fail(Integer code, String message) {
        return new ApiResult<>(code, null, message);
    }


    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public T getData() {
        return data;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public void setData(T data) {
        this.data = data;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
