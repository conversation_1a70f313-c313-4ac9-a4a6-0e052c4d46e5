package com.wftk.common.core.cache;


import com.wftk.common.core.store.ExpirationObjectStore;
import com.wftk.common.core.store.ObjectStore;

import java.util.Optional;

/**
 * @Author: ying.dong
 * @Date: 2021/10/13 10:49
 */
public abstract class BaseCachingObjectManager<K, V> implements ExpirationCachingObjectManager<K, V> {

    protected final ObjectStore<K, V> objectStore;

    protected BaseCachingObjectManager(ObjectStore<K, V> objectStore) {
        this.objectStore = objectStore;
    }

    @Override
    public boolean save(K k, V v) {
        return objectStore.save(k, v);
    }

    @Override
    public Optional<V> getObject(K k) {
        return Optional.ofNullable(objectStore.getObject(k));
    }

    @Override
    public boolean remove(K k) {
        return objectStore.remove(k);
    }

    @Override
    public boolean save(K k, V v, Long expiredInSeconds) {
        if (objectStore instanceof ExpirationObjectStore) {
            ((ExpirationObjectStore<K, V>) objectStore).save(k, v, expiredInSeconds);
        } else {
            throw new UnsupportedOperationException();
        }
        return true;
    }

    @Override
    public Long getTTL(K k) {
        if (objectStore instanceof ExpirationObjectStore<K, V> eos) {
            return eos.ttl(k);
        }
        throw new UnsupportedOperationException();
    }
}
