package com.wftk.common.core.store;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 缓存到内存
 * @Author: ying.dong
 * @Date: 2021/10/13 11:12
 */
public class InMemoryObjectStore<K, V> implements ObjectStore<K, V> {

    private final Map<K, V> cache;

    public InMemoryObjectStore() {
        this(new ConcurrentHashMap<>());
    }

    public InMemoryObjectStore(Map<K, V> cache) {
        this.cache = cache;
    }

    @Override
    public boolean save(K k, V v) {
        cache.put(k, v);
        return true;
    }

    @Override
    public V getObject(K k) {
        return cache.get(k);
    }

    @Override
    public Map<K, V> getAll() {
        return cache;
    }

    @Override
    public boolean remove(K k) {
        cache.remove(k);
        return true;
    }

    @Override
    public boolean clear() {
        cache.clear();
        return true;
    }
}
