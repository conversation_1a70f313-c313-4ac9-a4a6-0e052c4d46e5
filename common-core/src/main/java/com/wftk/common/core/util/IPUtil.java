/*
 * @Author: dy
 * @Date: 2025-06-12 16:55:00
 * @LastEditors: dy
 * @LastEditTime: 2025-06-12 17:11:42
 * @Description: IP工具类
 */
package com.wftk.common.core.util;

import java.net.InetAddress;
import java.util.Collection;

import cn.hutool.core.net.Ipv4Util;
import jakarta.servlet.http.HttpServletRequest;

public class IPUtil {

    /**
     * 获取请求ip
     * @param request
     * @return
     */
    public static String getRequestIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        // 对于通过多个代理的情况，X-Forwarded-For 的值可能是 "client, proxy1, proxy2"，取第一个非 unknown 的 IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        return ip;
    }


    /**
     * 判断IP是否在CIDR范围内
     * @param ip
     * @param cidrs
     * @return
     */
    public static boolean isIpInCidr(String ip, Collection<String> cidrs) {
        return cidrs.stream().anyMatch(cidr -> {
            try {
                return isIpInCidr(ip, cidr);
            } catch (Exception e) {
                return false;
            }
        });
    }


    /**
     * 判断IP是否在CIDR范围内
     * @param ip
     * @param cidr
     * @return
     */
    public static boolean isIpInCidr(String ip, String cidr) throws Exception {
        try {
            // 如果只提供了一个IP地址，直接比较是否相等
            if (!cidr.contains("/")) {
                return ip.equals(cidr);
            }
            String[] parts = cidr.split("/");
            if (parts.length != 2) {
                // 如果配置的不是CIDR，则认为不匹配，而不是抛出错误
                return false;
            }

            String networkIp = parts[0];
            int prefixLength = Integer.parseInt(parts[1]);

            InetAddress targetAddr = InetAddress.getByName(ip);
            InetAddress networkAddr = InetAddress.getByName(networkIp);

            byte[] targetBytes = targetAddr.getAddress();
            byte[] networkBytes = networkAddr.getAddress();

            // 确保 IP 版本一致 (IPv4 vs IPv6)
            if (targetBytes.length != networkBytes.length) {
                return false;
            }

            // 检查前缀长度的有效性
            if (prefixLength < 0 || prefixLength > targetBytes.length * 8) {
                throw new IllegalArgumentException("invalid CIDR.");
            }

            int bytesToCheck = prefixLength / 8;
            int bitsToCheck = prefixLength % 8;

            // 比较完整的字节
            for (int i = 0; i < bytesToCheck; i++) {
                if (targetBytes[i] != networkBytes[i]) {
                    return false;
                }
            }

            // 比较剩余的位
            if (bitsToCheck > 0) {
                int mask = 0xFF << (8 - bitsToCheck);
                if ((targetBytes[bytesToCheck] & mask) != (networkBytes[bytesToCheck] & mask)) {
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 判断IP是否为内网IP
     * @param ip
     * @return
     */
    public static boolean isInternalIp(String ip) {
        return Ipv4Util.isInnerIP(ip);
    }

}
