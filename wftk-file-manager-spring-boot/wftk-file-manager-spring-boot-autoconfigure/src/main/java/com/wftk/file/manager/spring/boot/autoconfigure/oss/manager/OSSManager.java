package com.wftk.file.manager.spring.boot.autoconfigure.oss.manager;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.HttpMethod;
import com.wftk.file.manager.spring.boot.autoconfigure.core.exception.AccessRoleNotConfiguredException;
import com.wftk.file.manager.spring.boot.autoconfigure.core.exception.ResourceAccessDeniedException;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.request.FileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.manager.ResourceManager;
import com.wftk.file.manager.spring.boot.autoconfigure.core.properties.CloudResourceProperties;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.client.OSSFileClient;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.client.registry.OSSFileClientRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * @Author: ying.dong
 * @Date: 2021/5/24 17:47
 */
public abstract class OSSManager implements ResourceManager {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    private final String HTTP_PROTOCOL = "http://";
    private final String HTTPS_PROTOCOL = "https://";

    private final OSSFileClientRegistry ossFileClientRegistry;

    public OSSManager(OSSFileClientRegistry ossFileClientRegistry) {
        this.ossFileClientRegistry = ossFileClientRegistry;
    }

    protected OSSFileClientRegistry getOssClientRegistry() {
        return ossFileClientRegistry;
    }

    /**
     * 获取OSS Client
     * @param role
     * @return
     */
    public OSSFileClient getOSSClient(String role) {
        return getOssClientRegistry().get(role)
                .orElseThrow(() -> new AccessRoleNotConfiguredException(role));
    }

    /**
     * 获取文件名
     * @param roleProperties
     * @param resource
     * @return
     */
    protected String getFileName(CloudResourceProperties roleProperties, FileMeta resource) {
        if (StrUtil.isBlank(roleProperties.getDirectory())) {
            return FileUtil.file(resource.getFileName()).toString();
        }
        return FileUtil.file(roleProperties.getDirectory(), resource.getFileName()).toString();
    }

    @Override
    public boolean copy(String role, FileMeta resource, String destRole, FileMeta destResource) {
        throw new UnsupportedOperationException();
    }


    /**
     *
     * @param properties
     * @return
     */
    protected String getEndpoint(CloudResourceProperties properties) {
        if (StrUtil.isNotBlank(properties.getEndpoint())) {
            return properties.getEndpoint();
        }
        String cloudEndpoint = properties.getCloudEndpoint();
        if (StrUtil.isBlank(cloudEndpoint)) {
            return cloudEndpoint;
        }
        String bucket = properties.getBucket();
        if (cloudEndpoint.startsWith(HTTP_PROTOCOL)) {
            cloudEndpoint = HTTP_PROTOCOL + bucket + "." + cloudEndpoint.substring(HTTP_PROTOCOL.length());
        } else if (cloudEndpoint.startsWith(HTTPS_PROTOCOL)) {
            cloudEndpoint = HTTPS_PROTOCOL + bucket + "." + cloudEndpoint.substring(HTTPS_PROTOCOL.length());
        }
        return cloudEndpoint;
    }


    /**
     * 初步校验权限
     * @param role
     * @param roleProperties
     * @param httpMethod
     */
    protected void permissionValidated(String role, CloudResourceProperties roleProperties, HttpMethod httpMethod) {
        //通常情况下，我们只会用到GET、PUT、POST、DELETE，因此本处只校验这几种
        if (!roleProperties.getWriteable() && httpMethod != HttpMethod.GET) {
            throw new ResourceAccessDeniedException(
                    "writable access denied for role [" + role + "], bucket [" + roleProperties.getBucket() + "], " +
                            "directory: [" + roleProperties.getDirectory() + "]");
        }

        if (!roleProperties.getReadable() && httpMethod == HttpMethod.GET) {
            throw new ResourceAccessDeniedException(
                    "readable access denied for role [" + role + "], bucket [" + roleProperties.getBucket() + "], " +
                            "directory: [" + roleProperties.getDirectory() + "]");
        }
    }
}
