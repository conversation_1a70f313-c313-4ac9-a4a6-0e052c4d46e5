package com.wftk.file.manager.spring.boot.autoconfigure.oss.manager;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.common.comm.ResponseMessage;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectResult;
import com.aliyun.oss.model.VoidResult;
import com.wftk.file.manager.spring.boot.autoconfigure.core.exception.ResourceException;
import com.wftk.file.manager.spring.boot.autoconfigure.core.exception.ResourceOperationException;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.request.FileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.response.DefaultUploadedFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.response.UploadedFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.properties.CloudResourceProperties;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.client.OSSFileClient;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.client.registry.OSSFileClientRegistry;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.constant.OSSConstant;

import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * @Author: ying.dong
 * @Date: 2021/7/13 10:21
 */
public class OSSServerManager extends OSSManager {

    public OSSServerManager(OSSFileClientRegistry ossFileClientRegistry) {
        super(ossFileClientRegistry);
    }


    /**
     * 获取流
     * @param role
     * @param resource
     * @param streamConsumer
     */
    public void getInputStream(String role, FileMeta resource, Consumer<InputStream> streamConsumer) {
        OSSFileClient ossClient = getOSSClient(role);
        CloudResourceProperties configuration = ossClient.getConfiguration();
        String key = getFileName(configuration, resource);

        TrackedInputStream inputStream = null;
        try {
            OSSObject ossObject = ossClient.getSDKClient().getObject(configuration.getBucket(), key);
            ResponseMessage response = ossObject.getResponse();
            if (!response.isSuccessful()) {
                throw new ResourceOperationException("download file error.[" + key + "]");
            }
            InputStream objectContent = ossObject.getObjectContent();
            //包装inputStream
            inputStream = new TrackedInputStream(objectContent);
            streamConsumer.accept(inputStream);
        } finally {
            if (inputStream != null && !inputStream.isClosed()) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    logger.error("close inputStream error.", e);
                }
            }
        }

    }


    @Override
    public UploadedFileMeta get(String role, FileMeta resource) throws ResourceException {
        OSSFileClient ossClient = getOSSClient(role);
        CloudResourceProperties configuration = ossClient.getConfiguration();
        String key = getFileName(configuration, resource);
        String urlStr = getEndpoint(configuration) + "/" + key;
        try {
            URL url = new URL(URLUtil.normalize(urlStr));
            return new DefaultUploadedFileMeta(resource.getFileName(), url);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public UploadedFileMeta save(String role, FileMeta resource, boolean allowOverride) throws ResourceException {
        ObjectMetadata objectMetadata = new ObjectMetadata();
        if (!allowOverride) {
            objectMetadata.setHeader(OSSConstant.Headers.FORBID_OVERWRITE, "true");
        }

        OSSFileClient ossClient = getOSSClient(role);
        CloudResourceProperties configuration = ossClient.getConfiguration();
        String key = resource.getFile().getName();
        if (StrUtil.isNotBlank(configuration.getDirectory())) {
            key = FileUtil.file(configuration.getDirectory(), key).toString();
        }

        PutObjectResult putObjectResult = ossClient.getSDKClient().putObject(configuration.getBucket(), key, resource.getFile(), objectMetadata);
        if ((putObjectResult.getResponse() != null && !putObjectResult.getResponse().isSuccessful()) ||
                StrUtil.isBlank(putObjectResult.getETag())) {
            String errorMsg = Optional.ofNullable(putObjectResult.getResponse())
                    .map(ResponseMessage::getErrorResponseAsString)
                    .orElse("upload file error.");
            throw new ResourceOperationException(errorMsg);
        }
        return new DefaultUploadedFileMeta(resource.getFile().getName(), null);
    }

    @Override
    public boolean del(String role, FileMeta resource) throws ResourceException {
        OSSFileClient ossClient = getOSSClient(role);
        CloudResourceProperties properties = ossClient.getConfiguration();

        permissionValidated(role, properties, HttpMethod.DELETE);

        String bucket = properties.getBucket();
        VoidResult result = ossClient.getSDKClient().deleteObject(bucket, getFileName(properties, resource));
        if (!result.getResponse().isSuccessful()) {
            logger.error("delete resource error, resource[{}], result: {}", resource, result);
            return false;
        }
        return true;
    }


    /**
     * 用于跟踪inputstream有没被关闭
     */
    public static class TrackedInputStream extends InputStream {

        private final InputStream delegate;
        private boolean closed = false;

        public TrackedInputStream(InputStream delegate) {
            this.delegate = delegate;
        }

        @Override
        public int read() throws IOException {
            if (closed) {
                throw new IOException("Stream is closed");
            }
            return delegate.read();
        }

        @Override
        public void close() throws IOException {
            delegate.close();
            closed = true;
        }

        public boolean isClosed() {
            return closed;
        }

        // 重写其他必要的方法
        @Override
        public int available() throws IOException {
            return delegate.available();
        }

        @Override
        public void mark(int readlimit) {
            delegate.mark(readlimit);
        }

        @Override
        public synchronized void reset() throws IOException {
            delegate.reset();
        }

        @Override
        public boolean markSupported() {
            return delegate.markSupported();
        }

        @Override
        public int read(byte[] b, int off, int len) throws IOException {
            return delegate.read(b, off, len);
        }

        @Override
        public int read(byte[] b) throws IOException {
            return delegate.read(b);
        }

        @Override
        public long skip(long n) throws IOException {
            return delegate.skip(n);
        }
    }

}
