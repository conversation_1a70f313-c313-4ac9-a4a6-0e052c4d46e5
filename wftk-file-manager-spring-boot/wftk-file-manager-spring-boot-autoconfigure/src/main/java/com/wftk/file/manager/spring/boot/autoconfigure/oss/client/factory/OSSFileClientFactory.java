package com.wftk.file.manager.spring.boot.autoconfigure.oss.client.factory;

import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.*;
import com.aliyun.oss.common.comm.SignVersion;
import com.wftk.file.manager.spring.boot.autoconfigure.core.client.factory.FileClientFactory;
import com.wftk.file.manager.spring.boot.autoconfigure.core.properties.CloudResourceProperties;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.client.OSSFileClient;

/**
 * <AUTHOR>
 * @create 2023/6/6 13:41
 */
public class OSSFileClientFactory implements FileClientFactory<CloudResourceProperties, OSS, OSSFileClient> {

    @Override
    public OSSFileClient get(CloudResourceProperties param) {
        String endpoint = param.getEndpoint();
        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        conf.setSignatureVersion(SignVersion.V2);
        if (StrUtil.isNotBlank(endpoint)) {
            //自定义域名
            conf.setSupportCname(true);
        } else {
            conf.setSupportCname(false);
            endpoint = param.getCloudEndpoint();
        }
        OSS sdkClient = new OSSClientBuilder().build(endpoint, param.getAccessKey(), param.getAccessSecret(), conf);
        return new OSSFileClient(param, sdkClient);
    }
}
