package com.wftk.file.manager.spring.boot.autoconfigure.core.exception;

/**
 * @Author: ying.dong
 * @Date: 2021/5/25 10:06
 */
public class ResourceException extends RuntimeException {

    private static final long serialVersionUID = 1385539287987922199L;

    public ResourceException() {
        super();
    }

    public ResourceException(String message) {
        super(message);
    }

    public ResourceException(String message, Throwable cause) {
        super(message, cause);
    }

    public ResourceException(Throwable cause) {
        super(cause);
    }

    protected ResourceException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
