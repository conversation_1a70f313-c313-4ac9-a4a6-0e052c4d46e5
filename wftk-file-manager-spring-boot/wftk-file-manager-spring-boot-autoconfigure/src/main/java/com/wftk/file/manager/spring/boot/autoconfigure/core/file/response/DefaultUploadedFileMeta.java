package com.wftk.file.manager.spring.boot.autoconfigure.core.file.response;

import java.net.URL;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/6/6 14:48
 */
public class DefaultUploadedFileMeta implements CloudUploadedFileMeta {

    private final String fileName;
    private final URL url;

    private final Map<String, String> headers;

    public DefaultUploadedFileMeta(String fileName, URL url) {
        this(fileName, url, null);
    }

    public DefaultUploadedFileMeta(String fileName, URL url, Map<String, String> headers) {
        this.fileName = fileName;
        this.url = url;
        this.headers = headers;
    }

    @Override
    public Map<String, String> getHeaders() {
        return headers;
    }

    @Override
    public String getFileName() {
        return fileName;
    }

    @Override
    public URL getUrl() {
        return url;
    }

}
