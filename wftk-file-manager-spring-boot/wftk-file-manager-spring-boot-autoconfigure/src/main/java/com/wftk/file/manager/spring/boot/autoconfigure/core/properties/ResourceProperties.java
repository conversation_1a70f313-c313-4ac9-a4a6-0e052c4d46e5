package com.wftk.file.manager.spring.boot.autoconfigure.core.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.Map;

/**
 * @Author: ying.dong
 * @Date: 2021/5/24 18:11
 */
@ConfigurationProperties(prefix = "config.file")
public class ResourceProperties {

    @NestedConfigurationProperty
    Map<String, CloudResourceProperties> oss;

    public Map<String, CloudResourceProperties> getOss() {
        return oss;
    }

    public void setOss(Map<String, CloudResourceProperties> oss) {
        this.oss = oss;
    }
}
