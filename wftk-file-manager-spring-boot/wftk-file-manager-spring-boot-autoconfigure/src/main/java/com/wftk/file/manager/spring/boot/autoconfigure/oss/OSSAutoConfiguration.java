package com.wftk.file.manager.spring.boot.autoconfigure.oss;

import com.wftk.file.manager.spring.boot.autoconfigure.core.manager.ResourceManager;
import com.wftk.file.manager.spring.boot.autoconfigure.core.properties.ResourceProperties;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.client.factory.OSSFileClientFactory;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.client.registry.OSSFileClientRegistry;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.constant.OSSConstant;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.manager.OSSPubBucketManager;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.manager.OSSServerManager;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.manager.OSSSignedManager;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * @Author: ying.dong
 * @Date: 2021/5/22 14:48
 */
@Configuration
public class OSSAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    OSSFileClientFactory ossClientFactory() {
        return new OSSFileClientFactory();
    }

    @ConditionalOnMissingBean
    @Bean
    OSSFileClientRegistry ossClientRegistry(ResourceProperties resourceProperties, OSSFileClientFactory ossFileClientFactory) {
        return new OSSFileClientRegistry(ossFileClientFactory, resourceProperties.getOss());
    }

    @Primary
    @Bean(name = OSSConstant.BeanName.SIGNED)
    ResourceManager OSSSignedManager(OSSFileClientRegistry ossClientRegistry) {
        return getSignedManager(ossClientRegistry);
    }

    @Bean(name = OSSConstant.BeanName.SERVER)
    ResourceManager OSSServerManager(OSSFileClientRegistry ossClientRegistry) {
        return getServerManager(ossClientRegistry);
    }

    @Bean(name = OSSConstant.BeanName.PUBLIC)
    ResourceManager OSSPubBucketManager(OSSFileClientRegistry ossClientRegistry) {
        return getPublicBucketManager(ossClientRegistry);
    }


    @Bean
    OSSServerManager ossServerManager(OSSFileClientRegistry ossClientRegistry) {
        return getServerManager(ossClientRegistry);
    }

    @Bean
    OSSSignedManager ossSignedManager(OSSFileClientRegistry ossFileClientRegistry) {
        return getSignedManager(ossFileClientRegistry);
    }

    @Bean
    OSSPubBucketManager ossPubBucketManager(OSSFileClientRegistry ossFileClientRegistry) {
        return getPublicBucketManager(ossFileClientRegistry);
    }




    private OSSSignedManager getSignedManager(OSSFileClientRegistry ossClientRegistry) {
        return new OSSSignedManager(ossClientRegistry);
    }

    private OSSServerManager getServerManager(OSSFileClientRegistry ossClientRegistry) {
        return new OSSServerManager(ossClientRegistry);
    }

    private OSSPubBucketManager getPublicBucketManager(OSSFileClientRegistry ossClientRegistry) {
        return new OSSPubBucketManager(ossClientRegistry);
    }


}
