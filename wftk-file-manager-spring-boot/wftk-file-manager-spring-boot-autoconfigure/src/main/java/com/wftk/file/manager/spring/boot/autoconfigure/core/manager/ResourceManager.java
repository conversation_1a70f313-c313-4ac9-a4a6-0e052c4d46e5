package com.wftk.file.manager.spring.boot.autoconfigure.core.manager;


import com.wftk.file.manager.spring.boot.autoconfigure.core.exception.ResourceException;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.request.FileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.response.UploadedFileMeta;


/**
 * @Author: ying.dong
 * @Date: 2021/5/24 15:12
 */
public interface ResourceManager {

    /**
     * 获取资源
     * @param role
     * @param resource
     * @return
     */
    UploadedFileMeta get(String role, FileMeta resource) throws ResourceException;

    /**
     * 新增或者更新资源
     * @param role
     * @param resource
     * @param allowOverride
     * @return
     */
    UploadedFileMeta save(String role, FileMeta resource, boolean allowOverride) throws ResourceException;

    /**
     * 直接删除资源
     * @param role
     * @param resource
     * @return
     * @throws ResourceException
     */
    boolean del(String role, FileMeta resource) throws ResourceException;

    /**
     * 拷贝对象
     * @param role
     * @param resource
     * @param destRole
     * @param destResource
     * @return
     */
    boolean copy(String role, FileMeta resource, String destRole, FileMeta destResource);
}
