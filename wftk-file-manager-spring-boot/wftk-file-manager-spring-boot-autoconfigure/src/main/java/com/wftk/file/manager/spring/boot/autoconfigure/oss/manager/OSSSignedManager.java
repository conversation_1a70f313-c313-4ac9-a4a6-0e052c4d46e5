package com.wftk.file.manager.spring.boot.autoconfigure.oss.manager;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.VoidResult;
import com.wftk.file.manager.spring.boot.autoconfigure.core.exception.AccessRoleNotConfiguredException;
import com.wftk.file.manager.spring.boot.autoconfigure.core.exception.ResourceException;
import com.wftk.file.manager.spring.boot.autoconfigure.core.exception.SignedException;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.request.FileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.response.DefaultUploadedFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.response.UploadedFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.properties.CloudResourceProperties;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.client.OSSFileClient;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.client.registry.OSSFileClientRegistry;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.constant.OSSConstant;
import org.springframework.util.CollectionUtils;

import java.net.URL;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 签名模式
 * @Author: ying.dong
 * @Date: 2021/5/24 19:38
 */
public class OSSSignedManager extends OSSManager{

    public OSSSignedManager(OSSFileClientRegistry ossFileClientRegistry) {
        super(ossFileClientRegistry);
    }

    @Override
    public UploadedFileMeta get(String role, FileMeta resource) throws ResourceException {
        return doSign(role, resource, HttpMethod.GET, null);
    }

    @Override
    public UploadedFileMeta save(String role, FileMeta resource, boolean allowOverride) throws ResourceException {
        if (StrUtil.isBlank(resource.getMD5())) {
            throw new SignedException("MD5 must not be null");
        }
        Map<String, String> headers = new HashMap<>();
        if (!allowOverride) {
            headers.put(OSSConstant.Headers.FORBID_OVERWRITE, "true");
        }
        headers.put(OSSConstant.Headers.CONTENT_MD5, resource.getMD5());
        headers.put(OSSConstant.Headers.CONTENT_TYPE, OSSConstant.Headers.CONTENT_TYPE_STREAM);
        return doSign(role, resource, HttpMethod.PUT, headers);
    }

    @Override
    public boolean del(String role, FileMeta resource) throws ResourceException {
        OSSFileClient ossClient = getOssClientRegistry().get(role)
                .orElseThrow(() -> new AccessRoleNotConfiguredException(role));
        CloudResourceProperties roleProperties = ossClient.getConfiguration();

        permissionValidated(role, roleProperties, HttpMethod.DELETE);

        String bucket = roleProperties.getBucket();
        VoidResult result = ossClient.getSDKClient().deleteObject(bucket, getFileName(roleProperties, resource));
        if (!result.getResponse().isSuccessful()) {
            logger.error("delete resource error, resource[{}], result: {}", resource, result);
            return false;
        }
        return true;
    }

    /**
     * 签名流程
     * @param role
     * @param resource
     * @param httpMethod
     * @param additionalHeaders
     * @return
     */
    private UploadedFileMeta doSign(String role, FileMeta resource, HttpMethod httpMethod, Map<String, String> additionalHeaders) {
        OSSFileClient ossClient = getOssClientRegistry().get(role)
                .orElseThrow(() -> new AccessRoleNotConfiguredException(role));
        CloudResourceProperties roleProperties = ossClient.getConfiguration();
        permissionValidated(role, roleProperties, httpMethod);
        URL url = sign(roleProperties, resource, httpMethod, additionalHeaders, ossClient);
        return new DefaultUploadedFileMeta(resource.getFileName(), url, additionalHeaders);
    }


    /**
     * 签名
     * @param roleProperties
     * @param resource
     * @param httpMethod
     * @param headers
     * @param ossClient
     * @return
     */
    private URL sign(CloudResourceProperties roleProperties, FileMeta resource, HttpMethod httpMethod,
                     Map<String, String> headers, OSSFileClient ossClient) {
        //oss directory + custom directory + name
        String fileName = getFileName(roleProperties, resource);

        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(roleProperties.getBucket(), fileName, httpMethod);
        DateTime dateTime = DateUtil.offsetSecond(new Date(), roleProperties.getExpiredInTimeSeconds());
        request.setExpiration(dateTime);

        if (!CollectionUtils.isEmpty(headers)) {
            headers.forEach(request::addHeader);
        }

        try {
            return ossClient.getSDKClient().generatePresignedUrl(request);
        } catch (Exception e) {
            throw new SignedException(e);
        }
    }
}
