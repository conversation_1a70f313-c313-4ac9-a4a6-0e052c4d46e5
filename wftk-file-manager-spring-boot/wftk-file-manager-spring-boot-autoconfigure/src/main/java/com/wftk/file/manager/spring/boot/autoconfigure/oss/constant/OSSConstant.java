package com.wftk.file.manager.spring.boot.autoconfigure.oss.constant;

/**
 * @Author: ying.dong
 * @Date: 2021/5/25 13:58
 */
public interface OSSConstant {

    String OSS_SEPARATOR = "/";

    interface Headers {

        /**
         * 指定PutObject操作时是否覆盖同名Object。 当目标Bucket处于已开启或已暂停的版本控制状态时，x-oss-forbid-overwrite请求Header设置无效，即允许覆盖同名Object。
         * 不指定x-oss-forbid-overwrite或者指定x-oss-forbid-overwrite为false时，表示允许覆盖同名Object。
         * 指定x-oss-forbid-overwrite为true时，表示禁止覆盖同名Object。
         * 设置x-oss-forbid-overwrite请求Header会导致QPS处理性能下降，如果您有大量的操作需要使用x-oss-forbid-overwrite请求Header（QPS>1000），请联系技术支持，避免影响您的业务。
         *
         * 默认值：false
         */
        String FORBID_OVERWRITE = "x-oss-forbid-overwrite";


        /**
         * 为确保数据完整性，OSS提供多种方式对数据的MD5值进行校验。 如果需要通过Content-MD5进行MD5验证，可将Content-MD5加入到请求头中。
         */
        String CONTENT_MD5 = "Content-MD5";

        /**
         * 自定义头部前缀
         */
        String METADATA_PREFIX = "x-oss-meta-";


        String CONTENT_TYPE = "Content-Type";
        String CONTENT_TYPE_STREAM = "application/octet-stream";
    }

    interface BeanName {

        /**
         * 签名版本
         */
        String SIGNED = "OSSSignedManager";

        /**
         * 服务器端非签名
         */
        String SERVER = "OSSServerManager";

        /**
         * 公共bucket （读不需要签名）
         */
        String PUBLIC = "OSSPubBucketManager";

        /**
         * 云厂商提供的endpoint
         */
        String CLOUD_ENDPOINT = "OSSCloudEndpointManager";
    }
}
