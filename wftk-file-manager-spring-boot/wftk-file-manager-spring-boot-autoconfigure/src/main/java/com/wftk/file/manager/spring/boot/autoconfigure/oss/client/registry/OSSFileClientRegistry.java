package com.wftk.file.manager.spring.boot.autoconfigure.oss.client.registry;

import com.aliyun.oss.OSS;
import com.wftk.file.manager.spring.boot.autoconfigure.core.client.factory.FileClientFactory;
import com.wftk.file.manager.spring.boot.autoconfigure.core.client.registry.FileClientRegistry;
import com.wftk.file.manager.spring.boot.autoconfigure.core.properties.CloudResourceProperties;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.client.OSSFileClient;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/6/6 13:59
 */
public class OSSFileClientRegistry extends FileClientRegistry<CloudResourceProperties, OSS, OSSFileClient> {
    public OSSFileClientRegistry(FileClientFactory<CloudResourceProperties, OSS, OSSFileClient> factory, Map<String, CloudResourceProperties> roleProperties) {
        super(factory, roleProperties);
    }
}
