package com.wftk.file.manager.spring.boot.autoconfigure.oss.client;

import com.aliyun.oss.OSS;
import com.wftk.file.manager.spring.boot.autoconfigure.core.client.FileClient;
import com.wftk.file.manager.spring.boot.autoconfigure.core.properties.CloudResourceProperties;

/**
 * <AUTHOR>
 * @create 2023/6/6 11:36
 */
public class OSSFileClient implements FileClient<CloudResourceProperties, OSS> {

    private final CloudResourceProperties properties;
    private final OSS oss;

    public OSSFileClient(CloudResourceProperties properties, OSS oss) {
        this.properties = properties;
        this.oss = oss;
    }

    @Override
    public CloudResourceProperties getConfiguration() {
        return properties;
    }

    @Override
    public OSS getSDKClient() {
        return oss;
    }
}
