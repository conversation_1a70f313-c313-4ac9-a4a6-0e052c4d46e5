package com.wftk.file.manager.spring.boot.autoconfigure.core.properties;


/**
 * @Author: ying.dong
 * @Date: 2021/5/24 18:11
 */
public class CloudResourceProperties {


    /**
     * oss bucket
     */
    private String bucket;

    /**
     * oss directory
     */
    private String directory;

    private String accessKey;

    private String accessSecret;

    /**
     * 自定义的加速域名
     */
    private String endpoint;

    /**
     * 云厂商提供的endpoint
     */
    private String cloudEndpoint;

    /**
     * 是否拥有读权限
     */
    private Boolean readable;

    /**
     * 是否拥有写权限
     */
    private Boolean writeable;

    /**
     * 签名有效期
     */
    private Integer expiredInTimeSeconds = 1200;

    /**
     * socket超时时间(通过SDK交互时)
     */
    private Integer socketReadTimeout = 30000;

    /**
     * 连接超时时间(通过SDK交互时)
     */
    private Integer connectTimeout = 3000;

    public String getBucket() {
        return bucket;
    }

    public void setBucket(String bucket) {
        this.bucket = bucket;
    }

    public String getDirectory() {
        return directory;
    }

    public void setDirectory(String directory) {
        this.directory = directory;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getAccessSecret() {
        return accessSecret;
    }

    public void setAccessSecret(String accessSecret) {
        this.accessSecret = accessSecret;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getCloudEndpoint() {
        return cloudEndpoint;
    }

    public void setCloudEndpoint(String cloudEndpoint) {
        this.cloudEndpoint = cloudEndpoint;
    }

    public Boolean getReadable() {
        return readable;
    }

    public void setReadable(Boolean readable) {
        this.readable = readable;
    }

    public Boolean getWriteable() {
        return writeable;
    }

    public void setWriteable(Boolean writeable) {
        this.writeable = writeable;
    }

    public Integer getExpiredInTimeSeconds() {
        return expiredInTimeSeconds;
    }

    public void setExpiredInTimeSeconds(Integer expiredInTimeSeconds) {
        this.expiredInTimeSeconds = expiredInTimeSeconds;
    }

    public Integer getSocketReadTimeout() {
        return socketReadTimeout;
    }

    public void setSocketReadTimeout(Integer socketReadTimeout) {
        this.socketReadTimeout = socketReadTimeout;
    }

    public Integer getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(Integer connectTimeout) {
        this.connectTimeout = connectTimeout;
    }
}
