package com.wftk.file.manager.spring.boot.autoconfigure.oss.manager;

import cn.hutool.core.util.URLUtil;
import com.wftk.file.manager.spring.boot.autoconfigure.core.exception.ResourceException;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.request.FileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.response.DefaultUploadedFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.response.UploadedFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.properties.CloudResourceProperties;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.client.OSSFileClient;
import com.wftk.file.manager.spring.boot.autoconfigure.oss.client.registry.OSSFileClientRegistry;

import java.net.MalformedURLException;
import java.net.URL;

/**
 * 适用于公共Bucket (公开读，私有写)
 * @Author: ying.dong
 * @Date: 2021/8/23 14:58
 */
public class OSSPubBucketManager extends OSSSignedManager {

    public OSSPubBucketManager(OSSFileClientRegistry ossFileClientRegistry) {
        super(ossFileClientRegistry);
    }

    @Override
    public UploadedFileMeta get(String role, FileMeta resource) throws ResourceException {
        OSSFileClient ossClient = getOSSClient(role);
        CloudResourceProperties configuration = ossClient.getConfiguration();
        String key = getFileName(configuration, resource);
        String urlStr = getEndpoint(configuration) + "/" + key;
        try {
            URL url = new URL(URLUtil.normalize(urlStr));
            return new DefaultUploadedFileMeta(resource.getFileName(), url);
        } catch (MalformedURLException e) {
            throw new ResourceException(e);
        }
    }
}
