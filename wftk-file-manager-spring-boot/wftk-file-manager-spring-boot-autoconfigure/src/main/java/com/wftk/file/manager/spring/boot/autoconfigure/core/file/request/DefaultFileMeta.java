package com.wftk.file.manager.spring.boot.autoconfigure.core.file.request;


import java.io.File;

/**
 * <AUTHOR>
 * @create 2023/6/5 16:08
 */
public class DefaultFileMeta implements FileMeta {

    private final File file;
    private final String MD5;


    public DefaultFileMeta(File file, String md5) {
        this.file = file;
        MD5 = md5;
    }

    @Override
    public File getFile() {
        return file;
    }

    @Override
    public String getFileName() {
        return file.toString();
    }

    @Override
    public String getMD5() {
        return MD5;
    }

}
