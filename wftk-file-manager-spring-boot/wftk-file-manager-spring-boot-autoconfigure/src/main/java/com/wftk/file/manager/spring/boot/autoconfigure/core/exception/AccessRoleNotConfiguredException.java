package com.wftk.file.manager.spring.boot.autoconfigure.core.exception;

/**
 * @Author: ying.dong
 * @Date: 2021/5/25 10:10
 */
public class AccessRoleNotConfiguredException extends ResourceException {

    private static final long serialVersionUID = 1845864040701813674L;

    private final String role;

    public AccessRoleNotConfiguredException(String role) {
        super("role [" + role + "] haven't  be configured.");
        this.role = role;
    }

    public String getRole() {
        return role;
    }
}
