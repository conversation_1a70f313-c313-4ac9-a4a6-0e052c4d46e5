package com.wftk.file.manager.spring.boot.autoconfigure.core.client.registry;

import com.wftk.common.core.registry.ObjectRegistry;
import com.wftk.file.manager.spring.boot.autoconfigure.core.client.FileClient;
import com.wftk.file.manager.spring.boot.autoconfigure.core.client.factory.FileClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @create 2023/6/5 17:59
 */
public class FileClientRegistry<P, S, C extends FileClient<P, S>> implements ObjectRegistry<String, C> , InitializingBean {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    private final Map<String, C> registry = new ConcurrentHashMap<>();
    private final FileClientFactory<P, S, C> factory;
    private final Map<String, P> roleProperties;

    public FileClientRegistry(FileClientFactory<P, S, C> factory, Map<String, P> roleProperties) {
        this.factory = factory;
        this.roleProperties = roleProperties;
    }


    @Override
    public boolean register(String s, C c) {
        registry.put(s, c);
        return true;
    }

    @Override
    public boolean register(Map<String, C> objects) {
        registry.putAll(objects);
        return true;
    }

    @Override
    public boolean register(ObjectRegistry<String, C> objectRegistry) {
        registry.putAll(objectRegistry.getAll());
        return true;
    }

    @Override
    public Optional<C> get(String s) {
        return Optional.ofNullable(registry.get(s));
    }

    @Override
    public Map<String, C> getAll() {
        return registry;
    }

    @Override
    public boolean remove(String s) {
        registry.remove(s);
        return true;
    }

    @Override
    public boolean removeAll() {
        registry.clear();
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        init();
    }

    private void init() {
        roleProperties.forEach((k, v) -> {
            C client = factory.get(v);
            logger.info("register client: {}, role: {}", client, k);
            register(k, client);
        });
    }
}
