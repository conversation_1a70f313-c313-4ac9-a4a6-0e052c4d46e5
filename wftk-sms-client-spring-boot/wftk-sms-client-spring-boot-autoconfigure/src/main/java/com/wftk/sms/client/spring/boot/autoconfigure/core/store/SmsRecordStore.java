package com.wftk.sms.client.spring.boot.autoconfigure.core.store;

import com.wftk.sms.client.spring.boot.autoconfigure.core.request.send.SendSmsRequest;
import com.wftk.sms.client.spring.boot.autoconfigure.core.response.SmsResponse;

/**
 * <AUTHOR>
 * @create 2023/9/14 14:43
 */
public interface SmsRecordStore {

    /**
     * 保存短信记录
     * @param request
     * @param response
     * @param <P>
     * @param <R>
     */
    <P extends SendSmsRequest, R extends SmsResponse> void save(P request, R response);
}
