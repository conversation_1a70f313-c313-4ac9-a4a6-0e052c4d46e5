package com.wftk.sms.client.spring.boot.autoconfigure.core.request.builder.send;

import com.wftk.sms.client.spring.boot.autoconfigure.core.request.send.SendSmsRequest;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/5/29 18:03
 */
public interface SendSmsRequestBuilder {

    /**
     * 场景编码
     * @param scene
     * @return
     */
    SendSmsRequestBuilder scene(String scene);


    /**
     * 手机号
     * @param tel
     * @return
     */
    SendSmsRequestBuilder tel(String tel);


    /**
     * 模板值
     * @param name
     * @param value
     * @return
     */
    SendSmsRequestBuilder templateParams(String name, Object value);


    /**
     * 模板Map值
     * @param map
     * @return
     */
    SendSmsRequestBuilder templateParams(Map<String ,Object> map);


    /**
     * 构造参数
     * @return
     * @param <SR>
     * @throws IllegalArgumentException
     */
    <SR extends SendSmsRequest> SR build() throws IllegalArgumentException;
}
