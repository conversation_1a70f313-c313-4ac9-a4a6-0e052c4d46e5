package com.wftk.sms.client.spring.boot.autoconfigure.ext.jinlou.properties;

import com.wftk.sms.client.spring.boot.autoconfigure.core.properties.TemplateProperties;
import lombok.Data;

import java.util.List;

/**
 * 金楼世纪 配置
 * <AUTHOR>
 * @create 2023/8/14 10:35
 */
@Data
public class JinLouProperties {

    private boolean enable = false;

    private String name;
    private String pwd;
    private String domain;

    /**
     * 模板classPath路径 (路径 + templateCode共同组成一个完整的模板路径)
     * 金楼发送短信的内容是自定义的，不像阿里等厂商有预制模板，因此此处自定义模板，通过模板引擎来渲染
     */
    private String templateClassPath = "templates/sms";

    private List<TemplateProperties> templates;
}
