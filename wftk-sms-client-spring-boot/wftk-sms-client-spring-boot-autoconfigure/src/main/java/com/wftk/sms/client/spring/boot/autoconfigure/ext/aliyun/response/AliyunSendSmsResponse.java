package com.wftk.sms.client.spring.boot.autoconfigure.ext.aliyun.response;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.wftk.sms.client.spring.boot.autoconfigure.core.response.SmsResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/5/29 18:58
 */
@Data
public class AliyunSendSmsResponse implements SmsResponse {

    @JsonProperty("Code")
    private String code;

    @JsonProperty("Message")
    private String message;

    @JsonProperty("BizId")
    private String bizId;

    @JsonProperty("RequestId")
    private String requestId;


    @Override
    public boolean isSuccess() {
        return code.equalsIgnoreCase("OK");
    }
}
