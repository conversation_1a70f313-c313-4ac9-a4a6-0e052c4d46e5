package com.wftk.sms.client.spring.boot.autoconfigure.core.validator;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;

/**
 * <AUTHOR>
 * @create 2023/5/29 17:36
 */
public class DelegatingSmsValidator implements SmsValidator {

    private static final Logger LOGGER = LoggerFactory.getLogger(DelegatingSmsValidator.class);

    private final Set<SmsValidator> validators;

    public DelegatingSmsValidator(Set<SmsValidator> validators) {
        this.validators = validators;
    }

    @Override
    public boolean validate(String tel) {
        for (SmsValidator validator : validators) {
            if (!validator.validate(tel)) {
                LOGGER.warn("invalid tel: [tel], validator: [{}]", validator.getClass().getName());
                return false;
            }
        }
        return true;
    }
}
