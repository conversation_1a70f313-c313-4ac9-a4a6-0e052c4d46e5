package com.wftk.sms.client.spring.boot.autoconfigure.properties;

import com.wftk.sms.client.spring.boot.autoconfigure.ext.aliyun.properties.AliyunSmsProperties;
import com.wftk.sms.client.spring.boot.autoconfigure.ext.jinlou.properties.JinLouProperties;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;


/**
 * <AUTHOR>
 * @create 2023/5/30 09:37
 */
@ConfigurationProperties(prefix = "config.sms")
@Data
public class SmsProperties {

    @NestedConfigurationProperty
    private AliyunSmsProperties aliyun;

    @NestedConfigurationProperty
    private JinLouProperties jinLou;
}
