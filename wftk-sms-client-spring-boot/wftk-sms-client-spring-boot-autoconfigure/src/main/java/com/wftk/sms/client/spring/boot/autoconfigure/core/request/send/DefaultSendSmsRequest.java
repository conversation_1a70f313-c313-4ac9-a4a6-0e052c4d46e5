package com.wftk.sms.client.spring.boot.autoconfigure.core.request.send;


import com.wftk.sms.client.spring.boot.autoconfigure.core.enums.VendorType;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2023/5/29 17:59
 */
public class DefaultSendSmsRequest implements SendSmsRequest {

    private final String scene;
    private Set<String> tels;
    private Map<String, Object> templateParams;

    public DefaultSendSmsRequest(String scene) {
        this.scene = scene;
    }

    @Override
    public VendorType getVendor() {
        return null;
    }

    @Override
    public String getScene() {
        return scene;
    }

    @Override
    public Set<String> getTels() {
        return tels;
    }


    @Override
    public Map<String, Object> getTemplateParams() {
        return templateParams;
    }


    public void setTels(Set<String> tels) {
        this.tels = tels;
    }


    public void setTemplateParams(Map<String, Object> templateParams) {
        this.templateParams = templateParams;
    }

}
