package com.wftk.sms.client.spring.boot.autoconfigure.ext.jinlou.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wftk.sms.client.spring.boot.autoconfigure.core.response.SmsResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/8/14 13:48
 */
@Data
public class JinLouResponse implements SmsResponse {

    private static final String SUCCESS_CODE = "00";

    @JsonProperty("ReqCode")
    private String reqCode;

    @JsonProperty("ReqMsg")
    private String ReqMsg;

    @JsonProperty("ReqId")
    private String ReqId;

    @Override
    public boolean isSuccess() {
        return SUCCESS_CODE.equalsIgnoreCase(reqCode);
    }

    @Override
    public boolean isFail() {
        return !isSuccess();
    }
}
