package com.wftk.sms.client.spring.boot.autoconfigure.ext.aliyun.request;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wftk.jackson.core.JSONObject;
import com.wftk.sms.client.spring.boot.autoconfigure.core.enums.VendorType;
import com.wftk.sms.client.spring.boot.autoconfigure.core.request.send.SendSmsRequest;

import java.util.Map;
import java.util.Set;

/**
 * 阿里云发送短信接口
 * <AUTHOR>
 * @create 2023/5/29 17:58
 */
public class AliyunSendSmsRequest extends BaseAliyunRequest implements SendSmsRequest {

    @JsonIgnore
    private Set<String> tels;

    @JsonProperty("TemplateCode")
    private String templateCode;

    @JsonIgnore
    private Map<String, Object> templateParams;

    @JsonProperty("SignName")
    private String templateSignature;


    @JsonIgnore
    private String scene;

    @JsonIgnore
    @Override
    public VendorType getVendor() {
        return VendorType.ALIYUN;
    }

    @Override
    public String getScene() {
        return scene;
    }

    @Override
    public Set<String> getTels() {
        return tels;
    }

    @JsonProperty("PhoneNumbers")
    public String getTelString() {
        if (CollectionUtil.isEmpty(tels)) {
            return null;
        }
        return String.join(",", tels);
    }


    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JsonProperty("TemplateParam")
    public String getTemplateParamString() {
        if (CollectionUtil.isEmpty(templateParams)) {
            return null;
        }
        return JSONObject.getInstance().toJSONString(templateParams);
    }


    public String getTemplateCode() {
        return templateCode;
    }

    @Override
    public Map<String, Object> getTemplateParams() {
        return templateParams;
    }

    public String getTemplateSignature() {
        return templateSignature;
    }

    public void setTels(Set<String> tels) {
        this.tels = tels;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public void setTemplateParams(Map<String, Object> templateParams) {
        this.templateParams = templateParams;
    }

    public void setTemplateSignature(String templateSignature) {
        this.templateSignature = templateSignature;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }
}
