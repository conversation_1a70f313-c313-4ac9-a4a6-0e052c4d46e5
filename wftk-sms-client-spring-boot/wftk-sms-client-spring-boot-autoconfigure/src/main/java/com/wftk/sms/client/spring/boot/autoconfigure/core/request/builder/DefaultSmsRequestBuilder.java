package com.wftk.sms.client.spring.boot.autoconfigure.core.request.builder;

import com.wftk.sms.client.spring.boot.autoconfigure.core.request.builder.send.DefaultSendSmsRequestBuilder;
import com.wftk.sms.client.spring.boot.autoconfigure.core.request.builder.send.SendSmsRequestBuilder;

/**
 * <AUTHOR>
 * @create 2023/5/29 18:53
 */
public class DefaultSmsRequestBuilder implements SmsRequestBuilder {
    @Override
    public SendSmsRequestBuilder sendSms() {
        return new DefaultSendSmsRequestBuilder();
    }
}
