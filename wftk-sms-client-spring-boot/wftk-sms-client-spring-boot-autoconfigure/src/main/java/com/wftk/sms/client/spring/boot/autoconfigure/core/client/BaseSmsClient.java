package com.wftk.sms.client.spring.boot.autoconfigure.core.client;

import cn.hutool.core.collection.CollectionUtil;
import com.wftk.sms.client.spring.boot.autoconfigure.core.exception.SmsRequestFailureException;
import com.wftk.sms.client.spring.boot.autoconfigure.core.exception.SmsValidatedFailureException;
import com.wftk.sms.client.spring.boot.autoconfigure.core.request.send.SendSmsRequest;
import com.wftk.sms.client.spring.boot.autoconfigure.core.response.SmsResponse;
import com.wftk.sms.client.spring.boot.autoconfigure.core.store.SmsRecordStore;
import com.wftk.sms.client.spring.boot.autoconfigure.core.validator.SmsValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.Set;


/**
 * <AUTHOR>
 * @create 2023/5/29 17:19
 */
public abstract class BaseSmsClient implements SmsClient {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    protected final SmsValidator smsValidator;
    protected final boolean enable;

    protected final SmsRecordStore smsRecordStore;

    protected BaseSmsClient(SmsValidator smsValidator, boolean enable) {
        this(smsValidator, enable, null);
    }

    protected BaseSmsClient(SmsValidator smsValidator, boolean enable, SmsRecordStore smsRecordStore) {
        this.smsValidator = smsValidator;
        this.enable = enable;
        this.smsRecordStore = smsRecordStore;
    }

    @Override
    public <R extends SmsResponse, P extends SendSmsRequest, SR extends SendSmsRequest> R sendSms(P request)
            throws SmsValidatedFailureException, SmsRequestFailureException {
        if (!enable) {
            throw new SmsValidatedFailureException("sms disabled.");
        }
        if (request == null) {
            throw new SmsValidatedFailureException("request must not be null.");
        }
        Set<String> tels = request.getTels();
        if (CollectionUtil.isEmpty(tels)) {
            throw new SmsValidatedFailureException("tels must not be null.");
        }
        Iterator<String> iterator = tels.iterator();
        while (iterator.hasNext()) {
            String tel = iterator.next();
            if (!smsValidator.validate(tel)) {
                logger.warn("invalid tel: [{}], ignore...", tel);
                iterator.remove();
            }
        }
        if (CollectionUtil.isEmpty(tels)) {
            throw new SmsValidatedFailureException("tels must not be null.");
        }
        SR enhanceSendSmsRequest = enhanceSendSmsRequest(request);
        if (enhanceSendSmsRequest == null) {
            throw new SmsValidatedFailureException("enhancedRequest must not be null.");
        }
        SmsResponse smsResponse = doSendSms(enhanceSendSmsRequest);
        if (smsRecordStore != null) {
            smsRecordStore.save(enhanceSendSmsRequest, smsResponse);
        }
        return (R) smsResponse;
    }


    /**
     * 增强/转换发送短信请求
     * @param request
     * @return
     * @param <P>
     * @param <SR>
     */
    protected abstract <P extends SendSmsRequest, SR extends SendSmsRequest> SR enhanceSendSmsRequest(P request);


    /**
     * 发送短信
     * @param request
     * @return
     * @param <R>
     * @param <SR>
     * @throws SmsRequestFailureException
     */
    protected abstract <R extends SmsResponse, SR extends SendSmsRequest> R doSendSms(SR request) throws SmsRequestFailureException;
}
