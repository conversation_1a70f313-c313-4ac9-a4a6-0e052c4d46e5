package com.wftk.sms.client.spring.boot.autoconfigure.core.request.builder.send;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.sms.client.spring.boot.autoconfigure.core.request.send.DefaultSendSmsRequest;
import com.wftk.sms.client.spring.boot.autoconfigure.core.request.send.SendSmsRequest;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2023/5/29 18:09
 */
public class DefaultSendSmsRequestBuilder implements SendSmsRequestBuilder {

    private final Set<String> tels;
    private final Map<String, Object> templateParamsMap;
    private String scene;


    public DefaultSendSmsRequestBuilder() {
        this.tels = new HashSet<>();
        this.templateParamsMap = new HashMap<>();
    }

    @Override
    public SendSmsRequestBuilder scene(String scene) {
        this.scene = scene;
        return this;
    }

    @Override
    public SendSmsRequestBuilder tel(String tel) {
        if (StrUtil.isNotBlank(tel)) {
            tels.add(tel);
        }
        return this;
    }

    @Override
    public SendSmsRequestBuilder templateParams(String name, Object value) {
        if (StrUtil.isNotBlank(name) && value != null) {
            templateParamsMap.put(name, value);
        }
        return this;
    }

    @Override
    public SendSmsRequestBuilder templateParams(Map<String, Object> map) {
        if (CollectionUtil.isNotEmpty(map)) {
            templateParamsMap.putAll(map);
        }
        return this;
    }


    @Override
    public <SR extends SendSmsRequest> SR build() {
        if (StrUtil.isBlank(scene)) {
            throw new IllegalArgumentException("sms scene must not be null");
        }
        if (CollectionUtil.isEmpty(tels)) {
            throw new IllegalArgumentException("tels must not be empty");
        }

        DefaultSendSmsRequest sendSmsRequest = new DefaultSendSmsRequest(scene);
        sendSmsRequest.setTels(tels);
        sendSmsRequest.setTemplateParams(templateParamsMap);
        return (SR) sendSmsRequest;
    }
}
