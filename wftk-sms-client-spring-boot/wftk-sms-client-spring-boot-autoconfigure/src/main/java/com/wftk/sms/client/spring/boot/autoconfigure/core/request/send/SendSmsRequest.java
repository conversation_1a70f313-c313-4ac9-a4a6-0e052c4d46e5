package com.wftk.sms.client.spring.boot.autoconfigure.core.request.send;

import com.wftk.sms.client.spring.boot.autoconfigure.core.enums.VendorType;
import com.wftk.sms.client.spring.boot.autoconfigure.core.request.SmsRequest;
import org.springframework.lang.Nullable;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2023/5/29 18:00
 */
public interface SendSmsRequest extends SmsRequest {

    /**
     * 获取厂商
     * @return
     */
    @Nullable
    VendorType getVendor();


    /**
     * 获取场景编码
     * @return
     */
    String getScene();



    /**
     * 获取手机号
     * @return
     */
    Set<String> getTels();



    /**
     * 获取模板参数
     * @return
     */
    Map<String, Object> getTemplateParams();

}
