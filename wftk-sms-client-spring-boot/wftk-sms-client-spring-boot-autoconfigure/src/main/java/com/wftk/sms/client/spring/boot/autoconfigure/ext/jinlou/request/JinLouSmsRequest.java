package com.wftk.sms.client.spring.boot.autoconfigure.ext.jinlou.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wftk.sms.client.spring.boot.autoconfigure.core.enums.VendorType;
import com.wftk.sms.client.spring.boot.autoconfigure.core.request.send.SendSmsRequest;
import lombok.Data;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2023/8/14 10:25
 */
@Data
public class JinLouSmsRequest implements SendSmsRequest {

    /**
     * 账号
     */
    private String name;

    /**
     * 密码
     * 32位MD5小写值。
     * 需要用给定的密码拼接发送时间进行再次MD5加密。MD5(pwd+mttime)
     */
    private String pwd;

    /**
     * 扩展码（可选）
     * 用户自定义的子用户接入号扩展码。
     * 当有多个签名时，该字段必选。
     * 一个扩展码必须对应一个签名。
     * 一个签名可以对应多个扩展码。
     * 扩展码支持2位。
     * 例如：106900001234是分配给A客户的短信业务代码，A客户可以这么分配扩展码： 10690000123401分配给产品A使用，对应的签名为【产品A】，10690000123402分配给产品B使用，对应的签名为【产品B】，其中01/02为扩展码，并且每个扩展码要对应一个报备的签名。
     */
    private String subid;


    /**
     * 手机号
     */
    private String phone;


    /**
     * 短信内容
     * POST提交不需要对内容进行URL编码。
     * 短信内容最长为1000个字符。
     * 为避免%造成乱码问题，建议将短信内容中英文%替换成中文％
     */
    private String content;

    /**
     * 提交时间（必选）
     * 时间格式：yyyyMMddHHmmss
     * 须取当前时间，不能比当前时间早或晚太多。
     * 只做密码拼接加密用，不会影响短信发送
     */
    private String mttime;


    /**
     * 返回数据类型（必选）
     * 值为：0 返回Xml格式（过时）
     * 值为：1 返回Json格式（推荐）
     */
    private Integer rpttype;


    /**
     * 定时发送时间（可选）
     * 时间格式：yyyyMMddHHmmss
     * 备注：定时上限默认24小时，最大168小时
     */
    private String scheduletime;

    /**
     * 扩展内容（可选）
     * 用户自定义扩展字段，最大长度64位
     */
    private String extend;



    @JsonIgnore
    private Set<String> tels;

    @JsonIgnore
    private Map<String, Object> templateParams;

    @JsonIgnore
    private String scene;

    @JsonIgnore
    @Override
    public VendorType getVendor() {
        return VendorType.JIN_LOU;
    }

    @Override
    public String getScene() {
        return scene;
    }

    @Override
    public Set<String> getTels() {
        return Set.of(phone);
    }

    @Override
    public Map<String, Object> getTemplateParams() {
        return templateParams;
    }

    public void setTemplateParams(Map<String, Object> templateParams) {
        this.templateParams = templateParams;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }
}
