package com.wftk.sms.client.spring.boot.autoconfigure.ext.aliyun.client;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.http.client.core.common.type.DataType;
import com.wftk.http.client.core.executor.HttpRequestExecutor;
import com.wftk.http.client.core.request.HttpRequest;
import com.wftk.http.client.core.request.RequestMethod;
import com.wftk.http.client.core.request.builder.RequestBuilders;
import com.wftk.jackson.core.JSONObject;
import com.wftk.jackson.core.TargetType;
import com.wftk.sms.client.spring.boot.autoconfigure.core.store.SmsRecordStore;
import com.wftk.sms.client.spring.boot.autoconfigure.ext.aliyun.constant.AliyunApiConstant;
import com.wftk.sms.client.spring.boot.autoconfigure.ext.aliyun.properties.AliyunSmsProperties;
import com.wftk.sms.client.spring.boot.autoconfigure.ext.aliyun.request.AliyunSendSmsRequest;
import com.wftk.sms.client.spring.boot.autoconfigure.ext.aliyun.response.AliyunSendSmsResponse;
import com.wftk.sms.client.spring.boot.autoconfigure.ext.aliyun.signature.AliyunSignatureBuilder;
import com.wftk.sms.client.spring.boot.autoconfigure.core.client.BaseSmsClient;
import com.wftk.sms.client.spring.boot.autoconfigure.core.exception.SmsRequestFailureException;
import com.wftk.sms.client.spring.boot.autoconfigure.core.exception.SmsValidatedFailureException;
import com.wftk.sms.client.spring.boot.autoconfigure.core.properties.TemplateProperties;
import com.wftk.sms.client.spring.boot.autoconfigure.core.request.send.SendSmsRequest;
import com.wftk.sms.client.spring.boot.autoconfigure.core.response.SmsResponse;
import com.wftk.sms.client.spring.boot.autoconfigure.core.validator.SmsValidator;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @create 2023/5/29 19:05
 */
@Slf4j
public class AliyunSmsClient extends BaseSmsClient {

    private final HttpRequestExecutor executor;
    private final AliyunSmsProperties aliyunSmsProperties;
    private final Map<String, TemplateProperties> templateMap;

    public AliyunSmsClient(SmsValidator smsValidator, HttpRequestExecutor executor, AliyunSmsProperties aliyunSmsProperties) {
        this(smsValidator, executor, null, aliyunSmsProperties);
    }

    public AliyunSmsClient(SmsValidator smsValidator, HttpRequestExecutor executor, SmsRecordStore smsRecordStore, AliyunSmsProperties aliyunSmsProperties) {
        super(smsValidator, aliyunSmsProperties.isEnable(), smsRecordStore);
        this.executor = executor;
        this.aliyunSmsProperties = aliyunSmsProperties;
        if (CollectionUtil.isEmpty(aliyunSmsProperties.getTemplates())) {
            templateMap = null;
        } else {
            templateMap = aliyunSmsProperties.getTemplates().stream()
                    .collect(Collectors.toMap(TemplateProperties::getScene, it -> it));
        }
    }



    @Override
    protected <P extends SendSmsRequest, SR extends SendSmsRequest> SR enhanceSendSmsRequest(P request) {
        AliyunSendSmsRequest aliyunSendSmsRequest = new AliyunSendSmsRequest();
        aliyunSendSmsRequest.setTels(request.getTels());
        aliyunSendSmsRequest.setTemplateParams(request.getTemplateParams());
        String scene = request.getScene();
        aliyunSendSmsRequest.setScene(scene);
        TemplateProperties template = templateMap.get(scene);
        if (template == null) {
            throw new SmsValidatedFailureException("invalid sms scene: [" + scene + "]");
        }
        String templateCode = template.getCode();
        if (StrUtil.isBlank(templateCode)) {
            throw new SmsValidatedFailureException("sms template code must not be null. scene: [" + scene + "]");
        }
        String templateSignature = template.getSignature();
        if (StrUtil.isBlank(templateSignature)) {
            throw new SmsValidatedFailureException("sms template signature must not be null. scene: [" + scene + "]");
        }
        aliyunSendSmsRequest.setTemplateCode(templateCode);
        aliyunSendSmsRequest.setTemplateSignature(templateSignature);
        aliyunSendSmsRequest.setAccessKeyId(aliyunSmsProperties.getAccessKey());
        aliyunSendSmsRequest.setAction(AliyunApiConstant.SEND_SMS);

        AliyunSignatureBuilder aliyunSignatureBuilder = new AliyunSignatureBuilder(aliyunSmsProperties.getAccessSecret(), RequestMethod.POST.name());

        //阿里云的参数首字母为大写
        Map<String, Object> paramsMap = JSONObject.getInstance().convertValue(aliyunSendSmsRequest, new TargetType<>() {
        });

        aliyunSignatureBuilder.putAll(paramsMap);
        String signature = aliyunSignatureBuilder.build();
        aliyunSendSmsRequest.setSignature(signature);
        return (SR) aliyunSendSmsRequest;
    }

    @Override
    protected <R extends SmsResponse, SR extends SendSmsRequest> R doSendSms(SR request) throws SmsRequestFailureException {
        //阿里云的参数首字母为大写
        Map<String, Object> paramsMap = JSONObject.getInstance().convertValue(request, new TargetType<>() {
        });
        HttpRequest<Map<String, Object>, AliyunSendSmsResponse> httpRequest = RequestBuilders.<Map<String, Object>, AliyunSendSmsResponse>bodyBuilder(AliyunApiConstant.DOMAIN)
                .method(RequestMethod.POST)
                .form(paramsMap)
                .resultType(new DataType<>() {})
                .build();
        AliyunSendSmsResponse response = executor.execute(httpRequest);
        log.info("aliyun sms reponse: {}", response);
        return (R) response;
    }
}
