package com.wftk.sms.client.spring.boot.autoconfigure.ext.aliyun.request;

import cn.hutool.core.util.IdUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wftk.sms.client.spring.boot.autoconfigure.util.UTCTimeUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/5/29 17:47
 */
@Data
public class BaseAliyunRequest {

    private static final String DEFAULT_SIGN_METHOD = "HMAC-SHA1";
    private static final String DEFAULT_SIGNATURE_VERSION = "1.0";
    private static final String DEFAULT_API_VERSION = "2017-05-25";
    private static final String DEFAULT_FORMAT = "json";

    /**
     * 签名
     */
    @JsonProperty("Signature")
    private String signature;

    /**
     * 访问密钥 ID。AccessKey 用于调用 API。
     * 必填
     */
    @JsonProperty("AccessKeyId")
    private String accessKeyId;

    /**
     * API 的名称
     * 必填
     */
    @JsonProperty("Action")
    private String action;

    /**
     * 返回参数的语言类型。取值范围：json | xml。默认值：json
     * 非必填
     */
    @JsonProperty("Format")
    private String format = DEFAULT_FORMAT;


    /**
     * 签名方式。取值范围：HMAC-SHA1
     * 必填
     */
    @JsonProperty("SignatureMethod")
    private String signatureMethod = DEFAULT_SIGN_METHOD;

    /**
     * 签名唯一随机数。用于防止网络重放攻击，建议您每一次请求都使用不同的随机数。
     * 必填
     */
    @JsonProperty("SignatureNonce")
    private String signatureNonce = IdUtil.randomUUID();

    /**
     * 签名算法版本。取值范围：1.0
     * 必填
     */
    @JsonProperty("SignatureVersion")
    private String signatureVersion = DEFAULT_SIGNATURE_VERSION;

    /**
     * 请求的时间戳。按照ISO8601 标准表示，并需要使用UTC时间，格式为yyyy-MM-ddTHH:mm:ssZ。示例：2018-01-01T12:00:00Z 表示北京时间 2018 年 01 月 01 日 20 点 00 分 00 秒
     * 必填
     */
    @JsonProperty("Timestamp")
    private String timestamp = UTCTimeUtil.now2UTCString();

    /**
     * API 的版本号，格式为 YYYY-MM-DD。取值范围：2017-05-25
     * 必填
     */
    @JsonProperty("Version")
    private String version = DEFAULT_API_VERSION;
}
