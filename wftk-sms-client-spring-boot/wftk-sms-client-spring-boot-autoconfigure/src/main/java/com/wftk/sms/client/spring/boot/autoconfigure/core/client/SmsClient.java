package com.wftk.sms.client.spring.boot.autoconfigure.core.client;

import com.wftk.sms.client.spring.boot.autoconfigure.core.exception.SmsRequestFailureException;
import com.wftk.sms.client.spring.boot.autoconfigure.core.exception.SmsValidatedFailureException;
import com.wftk.sms.client.spring.boot.autoconfigure.core.request.send.SendSmsRequest;
import com.wftk.sms.client.spring.boot.autoconfigure.core.response.SmsResponse;

/**
 * <AUTHOR>
 * @create 2023/5/29 17:11
 */
public interface SmsClient {

    /**
     * 发送短信
     *
     * @param request
     * @param <P>
     * @param <R>
     * @param <SR>
     * @return
     * @throws SmsValidatedFailureException
     * @throws SmsRequestFailureException
     */
    <R extends SmsResponse, P extends SendSmsRequest, SR extends SendSmsRequest> R sendSms(P request)
            throws SmsValidatedFailureException, SmsRequestFailureException;
}
