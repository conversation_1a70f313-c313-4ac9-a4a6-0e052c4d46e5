package com.wftk.sms.client.spring.boot.autoconfigure.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * @Author: ying.dong
 * @Date: 2021/5/11 18:21
 */
public class UTCTimeUtil {

    static DateTimeFormatter UTC_FORMATTER =  DateTimeFormatter.ofPattern(DatePattern.UTC_PATTERN);

    static DateTimeFormatter PURE_DATETIME_FORMATTER = DatePattern.PURE_DATETIME_FORMATTER;

    public static String now2UTCString() {
        return LocalDateTimeUtil.format(LocalDateTimeUtil.ofUTC(Instant.now()), UTC_FORMATTER);
    }

    public static String now2PureDateTimeString() {
        return LocalDateTimeUtil.format(LocalDateTime.now(), PURE_DATETIME_FORMATTER);
    }
}
