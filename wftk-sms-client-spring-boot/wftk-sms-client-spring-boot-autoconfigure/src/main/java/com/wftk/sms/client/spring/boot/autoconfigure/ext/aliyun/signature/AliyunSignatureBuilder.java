package com.wftk.sms.client.spring.boot.autoconfigure.ext.aliyun.signature;

import com.wftk.signature.builder.BaseSignBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.utils.Base64;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @create 2023/5/29 19:52
 */
@Slf4j
public class AliyunSignatureBuilder extends BaseSignBuilder {

    private static final String HMAC = "HmacSHA1";

    private final Charset charset;

    private final String httpMethod;

    public AliyunSignatureBuilder(String secret, String httpMethod) {
        this(secret, StandardCharsets.UTF_8, httpMethod);
    }

    public AliyunSignatureBuilder(String secret, Charset charset, String httpMethod) {
        super(secret);
        this.charset = charset;
        this.httpMethod = httpMethod;
    }


    @Override
    public String build() {
        try {
            String params = buildParams();
            log.info("aliyun sms sign before: [{}]", params);
            String sign = buildSignature(params);
            log.info("aliyun sms after sign: [{}]", sign);
            return sign;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    private String buildParams() throws Exception {
        java.util.Iterator<String> it = this.keySet().iterator();
        StringBuilder sortQueryStringTmp = new StringBuilder();
        while (it.hasNext()) {
            String key = it.next();
            if (this.get(key) == null) {
                continue;
            }
            sortQueryStringTmp.append("&").append(specialUrlEncode(key)).append("=").append(specialUrlEncode(this.get(key).toString()));
        }
        return sortQueryStringTmp.substring(1);
    }

    /**
     * 构造签名
     * @param params
     * @return
     * @throws Exception
     */
    private String buildSignature(String params) throws Exception {
        String stringToSign = httpMethod + "&" +
                specialUrlEncode("/") + "&" +
                specialUrlEncode(params);
        String sign = sign(secret + "&", stringToSign);
        // 签名最后也要做特殊URL编码
        return specialUrlEncode(sign);
    }


    private String specialUrlEncode(String value) throws Exception {
        return java.net.URLEncoder.encode(value, charset).replace("+", "%20").replace("*", "%2A").replace("%7E", "~");
    }

    private String sign(String accessSecret, String stringToSign) throws Exception {
        javax.crypto.Mac mac = javax.crypto.Mac.getInstance(HMAC);
        mac.init(new javax.crypto.spec.SecretKeySpec(accessSecret.getBytes(charset), HMAC));
        byte[] signData = mac.doFinal(stringToSign.getBytes(charset));
        return Base64.encodeBase64String(signData);
    }
}
