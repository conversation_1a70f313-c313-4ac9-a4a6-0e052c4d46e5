package com.wftk.sms.client.spring.boot.autoconfigure;

import com.wftk.http.client.core.executor.HttpRequestExecutor;
import com.wftk.sms.client.spring.boot.autoconfigure.core.store.SmsRecordStore;
import com.wftk.sms.client.spring.boot.autoconfigure.ext.aliyun.client.AliyunSmsClient;
import com.wftk.sms.client.spring.boot.autoconfigure.core.client.SmsClient;
import com.wftk.sms.client.spring.boot.autoconfigure.core.validator.DelegatingSmsValidator;
import com.wftk.sms.client.spring.boot.autoconfigure.core.validator.InvalidTelValidator;
import com.wftk.sms.client.spring.boot.autoconfigure.core.validator.SmsValidator;
import com.wftk.sms.client.spring.boot.autoconfigure.ext.jinlou.client.JinLouSmsClient;
import com.wftk.sms.client.spring.boot.autoconfigure.properties.SmsProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * <AUTHOR>
 * @create 2023/5/30 11:05
 */
@Configuration
@EnableConfigurationProperties(SmsProperties.class)
public class AutoConfiguration {


    @Bean
    InvalidTelValidator invalidTelValidator() {
        return new InvalidTelValidator();
    }

    @ConditionalOnMissingBean
    @Bean
    DelegatingSmsValidator delegatingSmsValidator(Set<SmsValidator> validators) {
        return new DelegatingSmsValidator(validators);
    }


    @ConditionalOnProperty(value = "config.sms.aliyun.enable", havingValue = "true", matchIfMissing = true)
    @ConditionalOnMissingBean(SmsClient.class)
    @Bean
    SmsClient aliyunSmsClient(DelegatingSmsValidator delegatingSmsValidator, HttpRequestExecutor httpRequestExecutor,
                              @Autowired(required = false) SmsRecordStore smsRecordStore, SmsProperties smsProperties) {
        return new AliyunSmsClient(delegatingSmsValidator, httpRequestExecutor, smsRecordStore, smsProperties.getAliyun());
    }


    @ConditionalOnProperty(value = "config.sms.jin-lou.enable", havingValue = "true")
    @ConditionalOnMissingBean(SmsClient.class)
    @Bean
    SmsClient jinLouSmsClient(DelegatingSmsValidator delegatingSmsValidator, HttpRequestExecutor httpRequestExecutor,
                              @Autowired(required = false) SmsRecordStore smsRecordStore, SmsProperties smsProperties) {
        return new JinLouSmsClient(delegatingSmsValidator, httpRequestExecutor, smsRecordStore, smsProperties.getJinLou());
    }
}
