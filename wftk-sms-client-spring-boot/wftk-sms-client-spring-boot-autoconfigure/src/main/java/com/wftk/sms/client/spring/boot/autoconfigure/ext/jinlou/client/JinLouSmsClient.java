package com.wftk.sms.client.spring.boot.autoconfigure.ext.jinlou.client;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.extra.template.Template;
import cn.hutool.extra.template.TemplateConfig;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.TemplateUtil;
import com.wftk.http.client.core.common.type.DataType;
import com.wftk.http.client.core.executor.HttpRequestExecutor;
import com.wftk.http.client.core.request.HttpRequest;
import com.wftk.http.client.core.request.RequestMethod;
import com.wftk.http.client.core.request.builder.RequestBuilders;
import com.wftk.jackson.core.JSONObject;
import com.wftk.jackson.core.TargetType;
import com.wftk.sms.client.spring.boot.autoconfigure.core.client.BaseSmsClient;
import com.wftk.sms.client.spring.boot.autoconfigure.core.exception.SmsRequestFailureException;
import com.wftk.sms.client.spring.boot.autoconfigure.core.exception.SmsValidatedFailureException;
import com.wftk.sms.client.spring.boot.autoconfigure.core.properties.TemplateProperties;
import com.wftk.sms.client.spring.boot.autoconfigure.core.request.send.SendSmsRequest;
import com.wftk.sms.client.spring.boot.autoconfigure.core.response.SmsResponse;
import com.wftk.sms.client.spring.boot.autoconfigure.core.store.SmsRecordStore;
import com.wftk.sms.client.spring.boot.autoconfigure.core.validator.SmsValidator;
import com.wftk.sms.client.spring.boot.autoconfigure.ext.jinlou.constant.JinLouApiConstant;
import com.wftk.sms.client.spring.boot.autoconfigure.ext.jinlou.properties.JinLouProperties;
import com.wftk.sms.client.spring.boot.autoconfigure.ext.jinlou.request.JinLouSmsRequest;
import com.wftk.sms.client.spring.boot.autoconfigure.ext.jinlou.response.JinLouResponse;
import com.wftk.sms.client.spring.boot.autoconfigure.util.UTCTimeUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * 金楼世纪
 * <AUTHOR>
 * @create 2023/8/14 11:04
 */
@Slf4j
public class JinLouSmsClient extends BaseSmsClient {
    private final HttpRequestExecutor executor;
    private final JinLouProperties jinLouProperties;
    private final Map<String, TemplateProperties> templateMap;

    public JinLouSmsClient(SmsValidator smsValidator, HttpRequestExecutor executor, JinLouProperties jinLouProperties) {
        this(smsValidator, executor, null, jinLouProperties);
    }

    public JinLouSmsClient(SmsValidator smsValidator, HttpRequestExecutor executor, SmsRecordStore smsRecordStore, JinLouProperties jinLouProperties) {
        super(smsValidator, jinLouProperties.isEnable(), smsRecordStore);
        this.executor = executor;
        this.jinLouProperties = jinLouProperties;
        if (CollectionUtil.isEmpty(jinLouProperties.getTemplates())) {
            templateMap = null;
        } else {
            templateMap = jinLouProperties.getTemplates().stream()
                    .collect(Collectors.toMap(TemplateProperties::getScene, it -> it));
        }
    }


    @Override
    protected <P extends SendSmsRequest, SR extends SendSmsRequest> SR enhanceSendSmsRequest(P request) {
        String templatePath = jinLouProperties.getTemplateClassPath();
        if (StrUtil.isBlank(templatePath)) {
            throw new SmsValidatedFailureException("sms template path must not be null");
        }
        String scene = request.getScene();
        TemplateProperties templateProperties = templateMap.get(scene);
        if (templateProperties == null) {
            throw new SmsValidatedFailureException("invalid sms scene: [" + scene + "]");
        }
        String templateCode = templateProperties.getCode();
        if (StrUtil.isBlank(templateCode)) {
            throw new SmsValidatedFailureException("sms template code must not be null. scene: [" + scene + "]");
        }

        String mttime = UTCTimeUtil.now2PureDateTimeString();
        String pwd = SecureUtil.md5(jinLouProperties.getPwd() + mttime);
        JinLouSmsRequest jinLouSmsRequest = new JinLouSmsRequest();
        jinLouSmsRequest.setName(jinLouProperties.getName());
        jinLouSmsRequest.setPwd(pwd);
        jinLouSmsRequest.setMttime(mttime);
        jinLouSmsRequest.setPhone(StrUtil.join(",", request.getTels()));
        jinLouSmsRequest.setScene(scene);
        jinLouSmsRequest.setTemplateParams(request.getTemplateParams());
        jinLouSmsRequest.setTels(request.getTels());

        //通过freemarker模板引擎渲染短信内容
        TemplateEngine templateEngine = TemplateUtil.createEngine(new TemplateConfig(templatePath, TemplateConfig.ResourceMode.CLASSPATH));
        Template template = templateEngine.getTemplate(templateCode + ".ftl");
        String content = template.render(request.getTemplateParams());
        jinLouSmsRequest.setContent(content);
        jinLouSmsRequest.setRpttype(1);
        return (SR) jinLouSmsRequest;
    }

    @Override
    protected <R extends SmsResponse, SR extends SendSmsRequest> R doSendSms(SR request) throws SmsRequestFailureException {
        String domain = jinLouProperties.getDomain();
        if (StrUtil.isBlank(domain)) {
            throw new SmsValidatedFailureException("sms domain must not be null.");
        }
        String url = domain;
        if (!url.endsWith("/")) {
            url = url + "/";
        }
        url = url + JinLouApiConstant.NORMAL_SMS_API;
        Map<String, Object> paramsMap = JSONObject.getInstance().convertValue(request, new TargetType<>() {
        });
        MapUtil.removeNullValue(paramsMap);
        HttpRequest<Map<String, Object>, JinLouResponse> httpRequest = RequestBuilders.<Map<String, Object>, JinLouResponse>bodyBuilder(url)
                .method(RequestMethod.POST)
                .form(paramsMap)
                .resultType(new DataType<>() {
                }).build();
        JinLouResponse response = executor.execute(httpRequest);
        log.info("jinLou sms response: {}", response);
        return (R) response;
    }
}
