package com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.resolver;

import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.MessageMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitAdmin;

/**
 * @Author: ying.dong
 * @Date: 2021/6/4 11:55
 */
public abstract class AbstractMessageMetadataResolver implements MessageMetadataResolver {

    protected final Logger log = LoggerFactory.getLogger(getClass());

    protected final RabbitAdmin rabbitAdmin;

    public AbstractMessageMetadataResolver(RabbitAdmin rabbitAdmin) {
        this.rabbitAdmin = rabbitAdmin;
    }

    @Override
    public boolean resolve(MessageMetadata messageMetadata) {
        if (!isSupport(messageMetadata)) {
            return false;
        }
        return doResolve(messageMetadata);
    }

    /**
     * 创建/申明 交换机
     * @param exchange
     */
    protected void declareExchange(Exchange exchange) {
        rabbitAdmin.declareExchange(exchange);
        log.info("exchange [{}] declared.", exchange);
    }

    /**
     * 创建/申明 队列
     * @param queue
     */
    protected void declareQueue(Queue queue) {
        rabbitAdmin.declareQueue(queue);
        log.info("queue [{}] declared.", queue);
    }

    protected void declareBinding(Binding binding) {
        rabbitAdmin.declareBinding(binding);
        log.info("binding [{}] declared.", binding);
    }


    public abstract boolean doResolve(MessageMetadata messageMetadata);
}
