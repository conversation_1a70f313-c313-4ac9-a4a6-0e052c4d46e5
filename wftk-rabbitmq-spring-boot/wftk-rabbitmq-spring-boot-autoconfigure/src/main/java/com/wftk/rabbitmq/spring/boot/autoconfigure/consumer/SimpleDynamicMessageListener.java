package com.wftk.rabbitmq.spring.boot.autoconfigure.consumer;

import com.rabbitmq.client.Channel;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.MessageMetadata;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.resolver.MessageMetadataResolver;
import org.springframework.amqp.core.Message;
import org.springframework.lang.NonNull;

/**
 * @Author: ying.dong
 * @Date: 2021/6/3 18:25
 */
public abstract class SimpleDynamicMessageListener<T> extends DynamicMessageListener<T> {

    public SimpleDynamicMessageListener(@NonNull MessageMetadata messageMetadata, @NonNull MessageMetadataResolver messageMetadataResolver) {
        super(messageMetadata, messageMetadataResolver);
    }

    public SimpleDynamicMessageListener(@NonNull MessageMetadata messageMetadata, @NonNull MessageMetadataResolver messageMetadataResolver, boolean autoCreateMQ) {
        super(messageMetadata, messageMetadataResolver, autoCreateMQ);
    }


    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        doExecute(message, channel, this::onMessage);
    }

    /**
     * 处理消息
     * @param t
     */
    public abstract void onMessage(T t);
}
