package com.wftk.rabbitmq.spring.boot.autoconfigure.producer;

import com.wftk.rabbitmq.spring.boot.autoconfigure.common.ExchangeTypeEnum;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.DirectMessageMetadata;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.MessageMetadata;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.resolver.MessageMetadataResolver;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.lang.NonNull;

/**
 * @Author: ying.dong
 * @Date: 2021/6/5 09:20
 */
public class DirectMessageDelivery extends AbstractMessageDelivery<DirectMessageMetadata> {

    public DirectMessageDelivery(@NonNull MessageMetadataResolver messageMetadataResolver, @NonNull RabbitTemplate rabbitTemplate) {
        super(messageMetadataResolver, rabbitTemplate);
    }

    public DirectMessageDelivery(@NonNull MessageMetadataResolver messageMetadataResolver, @NonNull RabbitTemplate rabbitTemplate, @NonNull MessageNotDeliveredCallback messageNotDeliveredCallback, boolean autoDeclareMQ) {
        super(messageMetadataResolver, rabbitTemplate, messageNotDeliveredCallback, autoDeclareMQ);
    }

    @Override
    public boolean isSupport(MessageMetadata messageMetadata) {
        return ExchangeTypeEnum.DIRECT.equals(messageMetadata.getExchangeType());
    }

    @Override
    protected boolean doDeliver(DirectMessageMetadata messageMetadata, Message message) {
        rabbitTemplate.convertAndSend(messageMetadata.getExchange(), messageMetadata.getQueue(), message);
        return true;
    }
}
