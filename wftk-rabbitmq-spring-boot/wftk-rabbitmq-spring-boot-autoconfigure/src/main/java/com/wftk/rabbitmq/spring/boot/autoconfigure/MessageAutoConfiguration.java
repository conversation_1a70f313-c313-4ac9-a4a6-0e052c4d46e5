package com.wftk.rabbitmq.spring.boot.autoconfigure;

import com.wftk.rabbitmq.spring.boot.autoconfigure.common.ExchangeTypeEnum;
import com.wftk.rabbitmq.spring.boot.autoconfigure.consumer.DynamicMessageListenerContainerFactory;
import com.wftk.rabbitmq.spring.boot.autoconfigure.consumer.initializer.DefaultDynamicMessageListenerInitializer;
import com.wftk.rabbitmq.spring.boot.autoconfigure.consumer.initializer.DynamicMessageListenerInitializer;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.resolver.DelegatingMessageMetadataResolver;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.resolver.DirectMessageMetadataResolver;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.resolver.FanoutMessageMetadataResolver;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.resolver.MessageMetadataResolver;
import com.wftk.rabbitmq.spring.boot.autoconfigure.producer.DelegatingMessageDelivery;
import com.wftk.rabbitmq.spring.boot.autoconfigure.producer.DirectMessageDelivery;
import com.wftk.rabbitmq.spring.boot.autoconfigure.producer.FanoutMessageDelivery;
import com.wftk.rabbitmq.spring.boot.autoconfigure.producer.MessageDelivery;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: ying.dong
 * @Date: 2021/6/3 13:45
 */
@Configuration
public class MessageAutoConfiguration {


    @Bean
    @ConditionalOnMissingBean
    public RabbitAdmin amqpAdmin(ConnectionFactory connectionFactory) {
        return new RabbitAdmin(connectionFactory);
    }

    @Bean
    @Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE, proxyMode = ScopedProxyMode.TARGET_CLASS)
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        return new RabbitTemplate(connectionFactory);
    }

    @ConditionalOnMissingBean
    @Bean
    MessageMetadataResolver messageMetadataResolver(RabbitAdmin rabbitAdmin) {
        List<MessageMetadataResolver> metadataResolverList = new ArrayList<>();
        metadataResolverList.add(new FanoutMessageMetadataResolver(rabbitAdmin));
        metadataResolverList.add(new DirectMessageMetadataResolver(rabbitAdmin));
        return new DelegatingMessageMetadataResolver(metadataResolverList);
    }


    @Bean
    @ConditionalOnMissingBean
    MessageDelivery messageDelivery(MessageMetadataResolver messageMetadataResolver,
                                    RabbitTemplate rabbitTemplate) {
        Map<ExchangeTypeEnum, MessageDelivery> messageDeliveryMap = new HashMap<>();
        messageDeliveryMap.put(ExchangeTypeEnum.DIRECT, new DirectMessageDelivery(messageMetadataResolver, rabbitTemplate));
        messageDeliveryMap.put(ExchangeTypeEnum.FANOUT, new FanoutMessageDelivery(messageMetadataResolver, rabbitTemplate));
        return new DelegatingMessageDelivery(messageDeliveryMap, true);
    }

    @Bean
    @ConditionalOnMissingBean
    DynamicMessageListenerContainerFactory factory(ConnectionFactory connectionFactory) {
        return new DynamicMessageListenerContainerFactory(connectionFactory);
    }

    @Bean
    @ConditionalOnMissingBean
    DynamicMessageListenerInitializer dynamicMessageListenerInitializer() {
        return new DefaultDynamicMessageListenerInitializer();
    }

}
