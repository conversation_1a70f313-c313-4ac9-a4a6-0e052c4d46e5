package com.wftk.rabbitmq.spring.boot.autoconfigure.metadata;

import com.wftk.rabbitmq.spring.boot.autoconfigure.common.ExchangeTypeEnum;

/**
 * @Author: ying.dong
 * @Date: 2021/6/4 14:08
 */
public abstract class DirectMessageMetadata implements MessageMetadata {

    @Override
    public final ExchangeTypeEnum getExchangeType() {
        return ExchangeTypeEnum.DIRECT;
    }

    /**
     * 获取交换机名
     * @return
     */
    public abstract String getExchange();
}
