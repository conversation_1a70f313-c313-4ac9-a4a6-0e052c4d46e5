package com.wftk.rabbitmq.spring.boot.autoconfigure.common.context;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/3/6 15:58
 */
public interface MessageContext extends Serializable {

    Map<String, Object> getOptions();

    boolean addOption(String key, Object value);

    Object getOption(String key);

    boolean removeOption(String key);

    void reset();
}
