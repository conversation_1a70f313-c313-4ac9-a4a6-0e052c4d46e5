package com.wftk.rabbitmq.spring.boot.autoconfigure.producer;

import cn.hutool.core.util.IdUtil;
import com.wftk.rabbitmq.spring.boot.autoconfigure.common.Support;
import com.wftk.rabbitmq.spring.boot.autoconfigure.common.context.MessageContext;
import com.wftk.rabbitmq.spring.boot.autoconfigure.common.context.MessageContextHolder;
import com.wftk.rabbitmq.spring.boot.autoconfigure.common.wrapper.DefaultMessageDataWrapper;
import com.wftk.rabbitmq.spring.boot.autoconfigure.common.wrapper.MessageDataWrapper;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.MessageMetadata;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.resolver.MessageMetadataResolver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.core.ReturnedMessage;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.lang.NonNull;

import java.util.Collection;
import java.util.LinkedList;

/**
 * @Author: ying.dong
 * @Date: 2021/6/4 15:47
 */
public abstract class AbstractMessageDelivery<M extends MessageMetadata> implements MessageDelivery, Support<MessageMetadata> {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    protected final MessageMetadataResolver messageMetadataResolver;

    protected final RabbitTemplate rabbitTemplate;

    protected ApplicationContext applicationContext;

    /**
     * 是否自动声明/创建资源
     */
    protected boolean autoDeclareMQ;

    public AbstractMessageDelivery(@NonNull MessageMetadataResolver messageMetadataResolver, @NonNull RabbitTemplate rabbitTemplate) {
        this(messageMetadataResolver, rabbitTemplate, new LogMessageNotDeliveredCallback(), true);
    }

    public AbstractMessageDelivery(@NonNull MessageMetadataResolver messageMetadataResolver, @NonNull RabbitTemplate rabbitTemplate,
                                   @NonNull MessageNotDeliveredCallback messageNotDeliveredCallback, boolean autoDeclareMQ) {
        this.messageMetadataResolver = messageMetadataResolver;
        this.autoDeclareMQ = autoDeclareMQ;
        //消息未入队，则回调
        rabbitTemplate.setMandatory(true);
        rabbitTemplate.setReturnsCallback(messageNotDeliveredCallback);
        this.rabbitTemplate = rabbitTemplate;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (autoDeclareMQ) {
            autoDeclareMQResources();
        }
    }

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public <T> boolean deliver(MessageMetadata messageMetadata, MessageContext messageContext, T data) {
        if (!isSupport(messageMetadata)) {
            return false;
        }
        try {
            //如果外部传入context,则使用外部context
            if (messageContext != null) {
                MessageContextHolder.setMessageContext(messageContext);
            } else {
                messageContext = MessageContextHolder.getMessageContext();
            }
            MessageProperties messageProperties = new MessageProperties();
            messageProperties.setMessageId(IdUtil.objectId());
            //序列化context
            MessageDataWrapper<T> messageDataWrapper = new DefaultMessageDataWrapper<>(messageContext, data);
            Message message = rabbitTemplate.getMessageConverter().toMessage(messageDataWrapper, messageProperties);
            boolean isSuccess = doDeliver((M) messageMetadata, message);
            logger.info("message delivered: {}", isSuccess);
            return isSuccess;
        } finally {
            MessageContextHolder.resetContext();
        }
    }

    /**
     * 投递消息
     * @param messageMetadata
     * @param message
     */
    protected abstract boolean doDeliver(M messageMetadata, Message message);


    /**
     * 自动创建资源
     */
    protected void autoDeclareMQResources() {
        Collection<MessageMetadata> messageMetadataItems = new LinkedList<>(
                this.applicationContext.getBeansOfType(MessageMetadata.class).values());
        logger.info("start resolve message metadata.");
        messageMetadataItems.forEach(it -> {
            boolean resolved = messageMetadataResolver.resolve(it);
            if (!resolved) {
                logger.warn("found MessageMetadata [{}], but hasn't resolved.", it);
            }
        });
    }


    static class LogMessageNotDeliveredCallback implements MessageNotDeliveredCallback {

        private static final Logger LOGGER = LoggerFactory.getLogger(LogMessageNotDeliveredCallback.class);

        @Override
        public void returnedMessage(ReturnedMessage returned) {
            LOGGER.warn("message [{}] deliver failed", returned);
        }
    }
}
