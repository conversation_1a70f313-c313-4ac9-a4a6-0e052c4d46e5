package com.wftk.rabbitmq.spring.boot.autoconfigure.consumer;

import com.rabbitmq.client.Channel;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.MessageMetadata;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.resolver.MessageMetadataResolver;
import org.springframework.amqp.core.Message;
import org.springframework.lang.NonNull;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * @Author: ying.dong
 * @Date: 2021/6/3 18:04
 */
public abstract class BatchDynamicMessageListener<T> extends DynamicMessageListener<T> {

    public BatchDynamicMessageListener(@NonNull MessageMetadata messageMetadata, @NonNull MessageMetadataResolver messageMetadataResolver) {
        super(messageMetadata, messageMetadataResolver);
    }

    public BatchDynamicMessageListener(@NonNull MessageMetadata messageMetadata, @NonNull MessageMetadataResolver messageMetadataResolver, boolean autoCreateMQ) {
        super(messageMetadata, messageMetadataResolver, autoCreateMQ);
    }

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        doExecute(message, channel, (obj) -> {
            if (obj instanceof Collection) {
                onMessageBatch((Collection<T>) obj);
            } else {
                List<T> list = new ArrayList<>();
                list.add((T) obj);
                onMessageBatch(list);
            }
        });
    }

    public abstract void onMessageBatch(Collection<T> collection);
}
