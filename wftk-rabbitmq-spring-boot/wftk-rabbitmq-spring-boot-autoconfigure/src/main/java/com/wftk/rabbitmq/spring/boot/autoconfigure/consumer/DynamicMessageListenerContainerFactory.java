package com.wftk.rabbitmq.spring.boot.autoconfigure.consumer;

import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.MessageMetadata;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;

/**
 * @Author: ying.dong
 * @Date: 2021/6/3 15:09
 */
public class  DynamicMessageListenerContainerFactory extends SimpleRabbitListenerContainerFactory {

    private final int DEFAULT_PREFETCH_COUNT = 1;

    private final ConnectionFactory connectionFactory;

    private int prefetchCount = DEFAULT_PREFETCH_COUNT;

    public DynamicMessageListenerContainerFactory(ConnectionFactory connectionFactory) {
        this.connectionFactory = connectionFactory;
    }

    public <T> SimpleMessageListenerContainer createListenerContainer(DynamicMessageListener<T> listener) {
        MessageMetadata messageMetadata = listener.getMessageMetadata();
        SimpleMessageListenerContainer container = super.createListenerContainer();
        container.setQueueNames(messageMetadata.getQueue());
        container.setMessageListener(listener);
        //手动回执
        container.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        container.setConnectionFactory(connectionFactory);
        container.setPrefetchCount(prefetchCount);
        return container;
    }

    public void setPrefetchCount(int prefetchCount) {
        this.prefetchCount = prefetchCount;
    }
}
