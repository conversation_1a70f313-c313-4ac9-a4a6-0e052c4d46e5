package com.wftk.rabbitmq.spring.boot.autoconfigure.producer;

import com.wftk.rabbitmq.spring.boot.autoconfigure.common.ExchangeTypeEnum;
import com.wftk.rabbitmq.spring.boot.autoconfigure.common.context.MessageContext;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.MessageMetadata;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.lang.NonNull;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: ying.dong
 * @Date: 2021/6/5 09:25
 */
public class DelegatingMessageDelivery implements MessageDelivery {

    private final Map<ExchangeTypeEnum, MessageDelivery> messageDeliveryMap = new ConcurrentHashMap<>();

    private ApplicationContext applicationContext;

    /**
     * 是否触发初始化方法
     */
    private final boolean initialDelivery;

    public DelegatingMessageDelivery(@NonNull Map<ExchangeTypeEnum, MessageDelivery> messageDeliveryMap) {
        this(messageDeliveryMap, false);
    }

    public DelegatingMessageDelivery(@NonNull Map<ExchangeTypeEnum, MessageDelivery> messageDeliveryMap, boolean initialDelivery) {
        this.messageDeliveryMap.putAll(messageDeliveryMap);
        this.initialDelivery = initialDelivery;
    }

    @Override
    public <T> boolean deliver(MessageMetadata messageMetadata, MessageContext messageContext, T data) {
        MessageDelivery messageDelivery = messageDeliveryMap.get(messageMetadata.getExchangeType());
        if (messageDelivery == null) {
            throw new RuntimeException("MessageDelivery with key [" + messageMetadata.getExchangeType() + "] not configured.");
        }
        return messageDelivery.deliver(messageMetadata, messageContext, data);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (!initialDelivery) {
            return;
        }
        for (MessageDelivery messageDelivery: messageDeliveryMap.values()) {
            messageDelivery.setApplicationContext(applicationContext);
            messageDelivery.afterPropertiesSet();
        }
    }

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
