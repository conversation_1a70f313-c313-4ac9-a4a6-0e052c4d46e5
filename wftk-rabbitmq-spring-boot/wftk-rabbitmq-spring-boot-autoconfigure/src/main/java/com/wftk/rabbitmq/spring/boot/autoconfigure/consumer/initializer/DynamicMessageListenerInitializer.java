package com.wftk.rabbitmq.spring.boot.autoconfigure.consumer.initializer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.Ordered;

/**
 * @Author: ying.dong
 * @Date: 2021/8/9 16:46
 */
public abstract class DynamicMessageListenerInitializer implements ApplicationContextAware, InitializingBean, Ordered {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    protected ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        initial(applicationContext);
        logger.info("message listeners initialed complete.");
    }

    @Override
    public int getOrder() {
        return Integer.MAX_VALUE;
    }

    abstract void initial(ApplicationContext applicationContext);
}
