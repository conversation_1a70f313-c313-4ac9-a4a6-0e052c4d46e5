package com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.resolver;


import com.wftk.rabbitmq.spring.boot.autoconfigure.common.Support;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.MessageMetadata;

/**
 * 解析已经注册到容器的MessageMetadata，用于自动创建（exchange、 queue）等资源
 * @Author: ying.dong
 * @Date: 2021/6/4 11:50
 */
public interface MessageMetadataResolver extends Support<MessageMetadata> {

    /**
     * 解析处理
     * @param messageMetadata
     */
    boolean resolve(MessageMetadata messageMetadata);
}
