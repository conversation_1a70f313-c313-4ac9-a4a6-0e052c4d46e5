package com.wftk.rabbitmq.spring.boot.autoconfigure.consumer;

import cn.hutool.core.util.StrUtil;
import com.rabbitmq.client.Channel;
import com.wftk.rabbitmq.spring.boot.autoconfigure.common.context.MessageContext;
import com.wftk.rabbitmq.spring.boot.autoconfigure.common.context.MessageContextHolder;
import com.wftk.rabbitmq.spring.boot.autoconfigure.common.wrapper.MessageDataWrapper;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.MessageMetadata;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.resolver.MessageMetadataResolver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.adapter.AbstractAdaptableMessageListener;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.lang.NonNull;

import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

/**
 * @Author: ying.dong
 * @Date: 2021/6/3 16:32
 */
public abstract class DynamicMessageListener<T> extends AbstractAdaptableMessageListener implements InitializingBean {

    protected final MessageMetadata messageMetadata;

    protected final MessageMetadataResolver messageMetadataResolver;

    /**
     * 最大重试3次
     */
    private final int DEFAULT_MAX_RETRIES = 3;

    private int maxRetries = DEFAULT_MAX_RETRIES;

    private final Map<String, AtomicInteger> retryCache = new ConcurrentHashMap<>();

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    protected final Type _type;


    /**
     * 是否自动创建MQ资源
     */
    protected final boolean autoCreateMQ;

    public DynamicMessageListener(@NonNull MessageMetadata messageMetadata, @NonNull MessageMetadataResolver messageMetadataResolver) {
        this(messageMetadata, messageMetadataResolver, true);
    }

    public DynamicMessageListener(@NonNull MessageMetadata messageMetadata, @NonNull MessageMetadataResolver messageMetadataResolver, boolean autoCreateMQ) {
        this.messageMetadata = messageMetadata;
        this.messageMetadataResolver = messageMetadataResolver;
        this.autoCreateMQ = autoCreateMQ;
        this._type = determineType(getClass());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        logger.debug("autoCreateMQ configured with [{}]", autoCreateMQ);
        if (autoCreateMQ) {
            messageMetadataResolver.resolve(messageMetadata);
            logger.info("MQ auto declared with metadata [{}]", messageMetadata);
        }
    }


    private Type determineType(Type type) {
        if (type instanceof ParameterizedType) {
            return ((ParameterizedType) type).getActualTypeArguments()[0];
        }
        Type superClass = ((Class<?>)type).getGenericSuperclass();
        return determineType(superClass);
    }

    protected Type getType() {
        return this._type;
    }


    /**
     * 处理完毕，正常回应，此消息将被删除
     * @param message
     * @param channel
     * @throws IOException
     */
    protected void ackOK(Message message, Channel channel) throws IOException {
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        logger.info("message [{}] ack: ok.", message);
    }

    /**
     * 消息将再次入队
     * @param message
     * @param channel
     */
    protected void ackEnqueue(Message message, Channel channel) throws IOException {
        channel.basicReject(message.getMessageProperties().getDeliveryTag(), true);
        logger.info("message [{}] ack: enqueue.", message);
    }

    /**
     * 丢弃该条消息
     * @param message
     * @param channel
     * @throws IOException
     */
    protected void ackDiscard(Message message, Channel channel) throws IOException {
        channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
        logger.warn("message [{}] ack: discard.", message);
    }

    /**
     * 失败时处理
     * @param message
     * @param channel
     * @return
     */
    public boolean onError(Message message, Channel channel) throws IOException {
        ackEnqueue(message, channel);
        return true;
    }

    /**
     * 达到最大重试时处理
     * @param message
     * @param channel
     * @return
     * @throws IOException
     */
    public boolean onMaxRetries(Message message, Channel channel) throws IOException {
        ackDiscard(message, channel);
        return true;
    }

    /**
     * 执行模板
     * @param message
     * @param channel
     * @param consumer
     * @throws IOException
     */
    protected final void doExecute(Message message, Channel channel, Consumer<T> consumer) throws IOException {
        boolean alreadyAck = false;
        String messageId = null;
        try {
            messageId = message.getMessageProperties().getMessageId();
            Object data = getMessageConverter().fromMessage(message);
            if (data instanceof MessageDataWrapper<?> wrapper) {
                data = wrapper.getData();
                //获取并设置messageContext
                MessageContext messageContext = wrapper.getMessageContext();
                if (messageContext != null) {
                    MessageContextHolder.setMessageContext(messageContext);
                }
            }
            //如果泛型类型不匹配，则不处理
            if (!((Class<?>)getType()).isAssignableFrom(data.getClass())) {
                return;
            }
            if (StrUtil.isNotBlank(messageId)) {
                if (!retryCache.containsKey(messageId)) {
                    retryCache.put(messageId, new AtomicInteger(0));
                } else {
                    retryCache.get(messageId).addAndGet(1);
                }
            }
            consumer.accept((T)data);
        } catch (Exception e) {
            logger.error("consume message error.", e);
            if (StrUtil.isBlank(messageId) || retryCache.get(messageId).get() > maxRetries) {
                logger.warn("consume message retry limited. [{}]", message);
                alreadyAck = onMaxRetries(message, channel);
                return;
            }
            alreadyAck = onError(message, channel);
        } finally {
            if (!alreadyAck) {
                ackOK(message, channel);
                if (StrUtil.isNotBlank(messageId)) {
                    retryCache.remove(messageId);
                }
            }
            //重置MessageContext
            MessageContextHolder.resetContext();
        }
    }

    public MessageMetadata getMessageMetadata() {
        return messageMetadata;
    }

    public void setMaxRetries(int maxRetries) {
        this.maxRetries = maxRetries;
    }
}
