package com.wftk.rabbitmq.spring.boot.autoconfigure.common.context;

/**
 * <AUTHOR>
 * @create 2024/3/6 16:11
 */
public class MessageContextHolder {


    private static final ThreadLocal<MessageContext> contextHolder = new InheritableThreadLocal<>();

    public synchronized static MessageContext getMessageContext() {
        MessageContext messageContext = contextHolder.get();
        if (messageContext == null) {
            messageContext = new DefaultMessageContext();
            setMessageContext(messageContext);
            return messageContext;
        }
        return messageContext;
    }

    public static void setMessageContext(MessageContext messageContext) {
        contextHolder.set(messageContext);
    }


    public static void resetContext() {
        contextHolder.remove();
    }

}
