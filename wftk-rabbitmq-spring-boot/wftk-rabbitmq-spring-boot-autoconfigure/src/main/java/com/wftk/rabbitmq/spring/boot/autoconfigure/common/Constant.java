package com.wftk.rabbitmq.spring.boot.autoconfigure.common;

/**
 * @Author: ying.dong
 * @Date: 2021/6/7 16:35
 */
public interface Constant {

    interface DeadLetterArgs {

        /**
         * 死信队列交换机
         */
        String DEAD_LETTER_EXCHANGE = "x-dead-letter-exchange";

        /**
         * 死信队列
         */
        String DEAD_LETTER_ROUTING_KEY = "x-dead-letter-routing-key";

        /**
         * 超时
         */
        String TTL = "x-message-ttl";

    }


    interface CommonArgs {

        String MESSAGE_CONTEXT = "x-message-context";

    }

}
