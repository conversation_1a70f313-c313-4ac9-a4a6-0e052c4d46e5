package com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.resolver;

import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.MessageMetadata;
import org.springframework.lang.NonNull;

import java.util.List;

/**
 * @Author: ying.dong
 * @Date: 2021/6/4 13:49
 */
public class DelegatingMessageMetadataResolver implements MessageMetadataResolver {

    private final List<MessageMetadataResolver> metadataResolverList;

    public DelegatingMessageMetadataResolver(@NonNull List<MessageMetadataResolver> metadataResolverList) {
        this.metadataResolverList = metadataResolverList;
    }

    @Override
    public boolean isSupport(MessageMetadata messageMetadata) {
        return true;
    }

    @Override
    public boolean resolve(MessageMetadata messageMetadata) {
        for (MessageMetadataResolver resolver: metadataResolverList) {
            if (resolver.resolve(messageMetadata)) {
                return true;
            }
        }
        return false;
    }
}
