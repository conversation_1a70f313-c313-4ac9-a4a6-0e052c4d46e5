package com.wftk.rabbitmq.spring.boot.autoconfigure.common.wrapper;

import com.wftk.rabbitmq.spring.boot.autoconfigure.common.context.MessageContext;

import java.io.Serial;

/**
 * <AUTHOR>
 * @create 2024/3/8 16:59
 */
public class DefaultMessageDataWrapper<T> implements MessageDataWrapper<T> {

    @Serial
    private static final long serialVersionUID = -8604631617843900556L;
    private final MessageContext messageContext;
    private final T data;

    public DefaultMessageDataWrapper(MessageContext messageContext, T data) {
        this.messageContext = messageContext;
        this.data = data;
    }

    @Override
    public MessageContext getMessageContext() {
        return messageContext;
    }

    @Override
    public T getData() {
        return data;
    }
}
