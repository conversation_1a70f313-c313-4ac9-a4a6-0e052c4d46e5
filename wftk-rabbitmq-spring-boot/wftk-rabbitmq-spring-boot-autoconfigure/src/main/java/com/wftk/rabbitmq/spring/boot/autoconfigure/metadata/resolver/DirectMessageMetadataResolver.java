package com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.resolver;

import com.wftk.rabbitmq.spring.boot.autoconfigure.common.ExchangeTypeEnum;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.DirectMessageMetadata;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.MessageMetadata;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.core.RabbitAdmin;

/**
 * direct
 * @Author: ying.dong
 * @Date: 2021/6/4 13:57
 */
public class DirectMessageMetadataResolver extends AbstractMessageMetadataResolver {

    public DirectMessageMetadataResolver(RabbitAdmin rabbitAdmin) {
        super(rabbitAdmin);
    }

    @Override
    public boolean isSupport(MessageMetadata messageMetadata) {
        return ExchangeTypeEnum.DIRECT.equals(messageMetadata.getExchangeType());
    }

    @Override
    public boolean doResolve(MessageMetadata messageMetadata) {
        DirectMessageMetadata directMessageMetadata = (DirectMessageMetadata) messageMetadata;
        DirectExchange exchange = new DirectExchange(directMessageMetadata.getExchange(), true, false, directMessageMetadata.getExchangeArgs());
        Queue queue = new Queue(directMessageMetadata.getQueue(), true, false, false, directMessageMetadata.getQueueArgs());
        Binding binding = BindingBuilder.bind(queue).to(exchange).withQueueName();

        declareQueue(queue);
        declareExchange(exchange);
        declareBinding(binding);
        return true;
    }
}
