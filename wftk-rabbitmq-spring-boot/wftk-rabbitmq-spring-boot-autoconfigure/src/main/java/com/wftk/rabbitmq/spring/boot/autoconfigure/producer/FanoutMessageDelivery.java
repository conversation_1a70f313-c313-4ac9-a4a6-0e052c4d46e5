package com.wftk.rabbitmq.spring.boot.autoconfigure.producer;

import com.wftk.rabbitmq.spring.boot.autoconfigure.common.ExchangeTypeEnum;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.FanoutMessageMetadata;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.MessageMetadata;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.resolver.MessageMetadataResolver;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.lang.NonNull;

/**
 * 广播消息
 * @Author: ying.dong
 * @Date: 2021/6/4 17:39
 */
public class FanoutMessageDelivery extends AbstractMessageDelivery<FanoutMessageMetadata> {

    public FanoutMessageDelivery(@NonNull MessageMetadataResolver messageMetadataResolver, @NonNull RabbitTemplate rabbitTemplate) {
        super(messageMetadataResolver, rabbitTemplate);
    }

    public FanoutMessageDelivery(@NonNull MessageMetadataResolver messageMetadataResolver, @NonNull RabbitTemplate rabbitTemplate, @NonNull MessageNotDeliveredCallback messageNotDeliveredCallback, boolean autoDeclareMQ) {
        super(messageMetadataResolver, rabbitTemplate, messageNotDeliveredCallback, autoDeclareMQ);
    }

    @Override
    protected boolean doDeliver(FanoutMessageMetadata messageMetadata, Message message) {
        rabbitTemplate.convertAndSend(messageMetadata.getExchange(), "", message);
        return true;
    }

    @Override
    public boolean isSupport(MessageMetadata messageMetadata) {
        return ExchangeTypeEnum.FANOUT.equals(messageMetadata.getExchangeType());
    }
}
