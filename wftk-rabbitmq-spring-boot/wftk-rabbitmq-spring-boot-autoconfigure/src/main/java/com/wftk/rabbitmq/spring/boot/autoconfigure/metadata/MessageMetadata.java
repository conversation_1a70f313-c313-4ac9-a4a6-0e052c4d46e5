package com.wftk.rabbitmq.spring.boot.autoconfigure.metadata;


import com.wftk.rabbitmq.spring.boot.autoconfigure.common.ExchangeTypeEnum;

import java.util.Map;

/**
 * 消息元属性定义（决定自动创建哪些资源，消息去处，监听队列）
 * @Author: ying.dong
 * @Date: 2021/6/3 13:47
 */
public interface MessageMetadata {

    /**
     * 获取队列名
     * @return
     */
    String getQueue();

    /**
     * 获取交换机类型
     * @return
     */
    ExchangeTypeEnum getExchangeType();

    /**
     * 获取队列参数
     * @return
     */
    default Map<String, Object> getQueueArgs() {
        return null;
    }

    /**
     * 获取交换机参数
     * @return
     */
    default Map<String, Object> getExchangeArgs() {
        return null;
    }
}
