package com.wftk.rabbitmq.spring.boot.autoconfigure.common.context;




import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @create 2024/3/6 16:05
 */
public class DefaultMessageContext implements MessageContext {

    private final Map<String, Object> options;


    public DefaultMessageContext() {
        this(new ConcurrentHashMap<>());
    }

    public DefaultMessageContext(Map<String, Object> options) {
        this.options = options;
    }


    @Override
    public Map<String, Object> getOptions() {
        return options;
    }

    @Override
    public boolean addOption(String key, Object value) {
        return options.put(key, value) != null;
    }

    @Override
    public Object getOption(String key) {
        return options.get(key);
    }

    @Override
    public boolean removeOption(String key) {
        return options.remove(key) != null;
    }

    @Override
    public void reset() {
        options.clear();
    }

}
