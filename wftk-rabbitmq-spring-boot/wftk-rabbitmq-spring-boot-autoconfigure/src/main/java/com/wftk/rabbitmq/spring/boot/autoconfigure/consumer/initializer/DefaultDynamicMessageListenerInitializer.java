package com.wftk.rabbitmq.spring.boot.autoconfigure.consumer.initializer;

import com.wftk.rabbitmq.spring.boot.autoconfigure.consumer.DynamicMessageListener;
import com.wftk.rabbitmq.spring.boot.autoconfigure.consumer.DynamicMessageListenerContainerFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;

import java.util.Map;

/**
 * @Author: ying.dong
 * @Date: 2021/8/9 16:55
 */
public class DefaultDynamicMessageListenerInitializer extends DynamicMessageListenerInitializer {

    @Override
    void initial(ApplicationContext applicationContext) {
        Map<String, DynamicMessageListener> dynamicMessageListenerMap = applicationContext.getBeansOfType(DynamicMessageListener.class);
        DynamicMessageListenerContainerFactory factory = applicationContext.getBean(DynamicMessageListenerContainerFactory.class);
        ConfigurableApplicationContext configurableApplicationContext = (ConfigurableApplicationContext) applicationContext;
        ConfigurableListableBeanFactory beanFactory = configurableApplicationContext.getBeanFactory();
        dynamicMessageListenerMap.forEach((k, v) -> {
            SimpleMessageListenerContainer listenerContainer = factory.createListenerContainer(v);
            String containerBeanName = k + "SimpleMessageListenerContainer";
            beanFactory.registerSingleton(containerBeanName, listenerContainer);
            logger.debug("message listener [{}] initialed.", k);
        });
    }
}
