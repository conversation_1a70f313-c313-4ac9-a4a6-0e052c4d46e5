package com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.resolver;

import cn.hutool.core.util.StrUtil;
import com.wftk.rabbitmq.spring.boot.autoconfigure.common.ExchangeTypeEnum;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.FanoutMessageMetadata;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.MessageMetadata;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.util.StringUtils;

/**
 * @Author: ying.dong
 * @Date: 2021/6/4 14:35
 */
public class FanoutMessageMetadataResolver extends AbstractMessageMetadataResolver {

    public FanoutMessageMetadataResolver(RabbitAdmin rabbitAdmin) {
        super(rabbitAdmin);
    }

    @Override
    public boolean isSupport(MessageMetadata messageMetadata) {
        return ExchangeTypeEnum.FANOUT.equals(messageMetadata.getExchangeType());
    }

    @Override
    public boolean doResolve(MessageMetadata messageMetadata) {
        FanoutMessageMetadata fanoutMessageMetadata = (FanoutMessageMetadata) messageMetadata;
        FanoutExchange exchange = new FanoutExchange(fanoutMessageMetadata.getExchange(), true, false, messageMetadata.getExchangeArgs());
        declareExchange(exchange);

        if (StrUtil.isNotBlank(fanoutMessageMetadata.getQueue())) {
            Queue queue = new Queue(fanoutMessageMetadata.getQueue(), true, false, false, messageMetadata.getQueueArgs());
            Binding binding = BindingBuilder.bind(queue).to(exchange);
            declareQueue(queue);
            declareBinding(binding);
        }
        return true;
    }
}
