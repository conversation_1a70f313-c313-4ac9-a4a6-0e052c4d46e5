package com.wftk.rabbitmq.spring.boot.autoconfigure.producer;

import com.wftk.rabbitmq.spring.boot.autoconfigure.common.context.MessageContext;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.MessageMetadata;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContextAware;

/**
 * 消息发送
 * @Author: ying.dong
 * @Date: 2021/6/3 14:17
 */
public interface MessageDelivery extends InitializingBean, ApplicationContextAware {

    /**
     * 投递消息
     * @param messageMetadata
     * @param data
     * @param <T>
     * @return
     */
    default <T> boolean deliver(MessageMetadata messageMetadata, T data) {
        return deliver(messageMetadata, null, data);
    }

    <T> boolean deliver(MessageMetadata messageMetadata, MessageContext messageContext, T data);
}
