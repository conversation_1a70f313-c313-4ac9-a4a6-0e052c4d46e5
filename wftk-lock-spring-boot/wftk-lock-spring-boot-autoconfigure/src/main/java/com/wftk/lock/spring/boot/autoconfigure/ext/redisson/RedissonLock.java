package com.wftk.lock.spring.boot.autoconfigure.ext.redisson;


import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;
import org.redisson.api.RLock;

import java.util.concurrent.TimeUnit;

/**
 * 基于Redisson的实现
 * <AUTHOR>
 * @create 2023/4/26 13:44
 */
public class RedissonLock implements DLock {

    private final RLock lock;

    public RedissonLock(RLock lock) {
        this.lock = lock;
    }

    @Override
    public boolean tryLock() {
        return lock.tryLock();
    }

    @Override
    public boolean tryLock(long maxWaitTime, TimeUnit timeUnit) throws InterruptedException {
        return lock.tryLock(maxWaitTime, timeUnit);
    }

    @Override
    public boolean tryLock(long maxWaitTime, long leaseTime, TimeUnit timeUnit) throws InterruptedException {
        return lock.tryLock(maxWaitTime, leaseTime, timeUnit);
    }

    @Override
    public void lock() {
        lock.lock();
    }

    @Override
    public void lock(long leaseTime, TimeUnit timeUnit) {
        lock.lock(leaseTime, timeUnit);
    }

    @Override
    public boolean isLocked() {
        return lock.isLocked();
    }

    @Override
    public void unLock() {
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}
