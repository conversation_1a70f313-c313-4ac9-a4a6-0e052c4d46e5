package com.wftk.lock.spring.boot.autoconfigure;

import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;
import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DReadWriteLock;
import com.wftk.lock.spring.boot.autoconfigure.core.factory.DLockFactory;
import com.wftk.lock.spring.boot.autoconfigure.core.factory.DReadWriteLockFactory;
import com.wftk.lock.spring.boot.autoconfigure.ext.redisson.factory.RedissonLockFactory;
import com.wftk.lock.spring.boot.autoconfigure.ext.redisson.factory.RedissonReadWriteLockFactory;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2023/9/6 14:16
 */
@Configuration
public class DLockAutoConfiguration {

    @ConditionalOnMissingBean
    @Bean
    DLockFactory<? extends DLock> lockFactory(RedissonClient redissonClient) {
        return new RedissonLockFactory(redissonClient);
    }

    @ConditionalOnMissingBean
    @Bean
    DReadWriteLockFactory<? extends DReadWriteLock> readWriteLockFactory(RedissonClient redissonClient) {
        return new RedissonReadWriteLockFactory(redissonClient);
    }

}
