package com.wftk.lock.spring.boot.autoconfigure.ext.redisson.factory;

import com.wftk.lock.spring.boot.autoconfigure.core.factory.DReadWriteLockFactory;
import com.wftk.lock.spring.boot.autoconfigure.ext.redisson.RedissonReadWriteLock;
import org.redisson.api.RReadWriteLock;
import org.redisson.api.RedissonClient;

/**
 * <AUTHOR>
 * @create 2023/9/6 13:54
 */
public class RedissonReadWriteLockFactory implements DReadWriteLockFactory<RedissonReadWriteLock> {

    private final RedissonClient redissonClient;

    public RedissonReadWriteLockFactory(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    @Override
    public RedissonReadWriteLock get(String lockName) {
        RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(lockName);
        return new RedissonReadWriteLock(readWriteLock);
    }
}
