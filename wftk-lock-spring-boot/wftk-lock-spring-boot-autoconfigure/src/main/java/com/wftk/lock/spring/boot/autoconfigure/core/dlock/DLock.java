package com.wftk.lock.spring.boot.autoconfigure.core.dlock;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 分布式锁
 * <AUTHOR>
 * @create 2023/4/26 11:09
 */
public interface DLock {

    /**
     * 尝试获取锁(非阻塞)，如果未获取到，则会返回false
     * @return
     */
    boolean tryLock();

    /**
     * 尝试获取锁(非阻塞)，如果未获取到，则会返回false
     * @param maxWaitTime 最大等待时间
     * @param timeUnit
     * @return
     */
    boolean tryLock(long maxWaitTime, TimeUnit timeUnit) throws InterruptedException;


    /**
     * 尝试获取锁(非阻塞)，如果未获取到，则会返回false
     * @param maxWaitTime  最大等待时间
     * @param leaseTime  最长持有锁的时间
     * @param timeUnit
     * @return
     */
    boolean tryLock(long maxWaitTime, long leaseTime, TimeUnit timeUnit) throws InterruptedException;


    /**
     *
     * @param lockedExecutionSupplier
     * @param lockFailedExecutionSupplier
     * @return
     * @param <T>
     */
    default <T> T tryLock(Supplier<T> lockedExecutionSupplier, Supplier<T> lockFailedExecutionSupplier) {
        try {
            if(tryLock()) {
                return lockedExecutionSupplier.get();
            }
            return lockFailedExecutionSupplier == null ? null : lockFailedExecutionSupplier.get();
        } finally {
            unLock();
        }
    }


    /**
     *
     * @param maxWaitTime
     * @param timeUnit
     * @param lockedExecutionSupplier
     * @param lockFailedExecutionSupplier
     * @return
     * @param <T>
     */
    default <T> T tryLock(long maxWaitTime, TimeUnit timeUnit, Supplier<T> lockedExecutionSupplier, Supplier<T> lockFailedExecutionSupplier) {
        try {
            if(tryLock(maxWaitTime, timeUnit)) {
                return lockedExecutionSupplier.get();
            }
            return lockFailedExecutionSupplier == null ? null : lockFailedExecutionSupplier.get();
        } catch (InterruptedException e) {
            return lockFailedExecutionSupplier == null ? null : lockFailedExecutionSupplier.get();
        } finally {
            unLock();
        }
    }


    /**
     *
     * @param maxWaitTime
     * @param leaseTime
     * @param timeUnit
     * @param lockedExecutionSupplier
     * @param lockFailedExecutionSupplier
     * @return
     * @param <T>
     */
    default <T> T tryLock(long maxWaitTime, long leaseTime, TimeUnit timeUnit, Supplier<T> lockedExecutionSupplier, Supplier<T> lockFailedExecutionSupplier) {
        try {
            if(tryLock(maxWaitTime, leaseTime, timeUnit)) {
                return lockedExecutionSupplier.get();
            }
            return lockFailedExecutionSupplier == null ? null : lockFailedExecutionSupplier.get();
        } catch (InterruptedException e) {
            return lockFailedExecutionSupplier == null ? null : lockFailedExecutionSupplier.get();
        } finally {
            unLock();
        }
    }




    /**
     * 获取锁(阻塞)
     */
    void lock();


    /**
     * 获取锁(阻塞)
     * @param leaseTime  最大等待时间
     * @param timeUnit
     */
    void lock(long leaseTime, TimeUnit timeUnit);



    /**
     *
     * @param leaseTime
     * @param timeUnit
     * @param supplier
     * @return
     * @param <T>
     */
    default <T> T lock(long leaseTime, TimeUnit timeUnit, Supplier<T> supplier) {
        try {
            lock(leaseTime, timeUnit);
            return supplier.get();
        } finally {
            unLock();
        }
    }


    /**
     * 判断是否已加锁
     * @return
     */
    boolean isLocked();


    /**
     * 解锁
     */
    void unLock();
}
