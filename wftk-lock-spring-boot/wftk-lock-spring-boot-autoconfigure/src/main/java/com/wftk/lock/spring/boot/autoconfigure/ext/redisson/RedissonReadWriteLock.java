package com.wftk.lock.spring.boot.autoconfigure.ext.redisson;

import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;
import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DReadWriteLock;
import org.redisson.api.RReadWriteLock;

/**
 * <AUTHOR>
 * @create 2023/9/6 10:59
 */
public class RedissonReadWriteLock implements DReadWriteLock {

    private final RReadWriteLock readWriteLock;

    public RedissonReadWriteLock(RReadWriteLock readWriteLock) {
        this.readWriteLock = readWriteLock;
    }

    @Override
    public DLock getReadLock() {
        return new RedissonLock(readWriteLock.readLock());
    }

    @Override
    public DLock getWriteLock() {
        return new RedissonLock(readWriteLock.writeLock());
    }
}
