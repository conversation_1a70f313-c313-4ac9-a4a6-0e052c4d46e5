package com.wftk.lock.spring.boot.autoconfigure.ext.redisson.factory;

import com.wftk.lock.spring.boot.autoconfigure.core.factory.DLockFactory;
import com.wftk.lock.spring.boot.autoconfigure.ext.redisson.RedissonLock;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

/**
 * <AUTHOR>
 * @create 2023/4/26 13:53
 */
public class RedissonLockFactory implements DLockFactory<RedissonLock> {

    private final RedissonClient redissonClient;

    public RedissonLockFactory(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    @Override
    public RedissonLock get(String lockName) {
        RLock lock = redissonClient.getLock(lockName);
        return new RedissonLock(lock);
    }
}
