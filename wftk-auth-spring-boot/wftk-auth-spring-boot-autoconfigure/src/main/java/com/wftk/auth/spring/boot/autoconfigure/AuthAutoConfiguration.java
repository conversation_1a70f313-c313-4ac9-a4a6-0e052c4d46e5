package com.wftk.auth.spring.boot.autoconfigure;

import com.wftk.auth.spring.boot.autoconfigure.core.auth.manager.AuthenticationManager;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.manager.PreAuthenticationManager;
import com.wftk.auth.spring.boot.autoconfigure.core.limitation.factory.RateLimiterFactory;
import com.wftk.auth.spring.boot.autoconfigure.core.limitation.factory.RedissonRateLimiterFactory;
import com.wftk.auth.spring.boot.autoconfigure.core.loader.ClientLoader;
import com.wftk.auth.spring.boot.autoconfigure.core.loader.UserLoader;
import com.wftk.auth.spring.boot.autoconfigure.core.password.PasswordEncoder;
import com.wftk.auth.spring.boot.autoconfigure.core.persistent.ObjectPersistent;
import com.wftk.auth.spring.boot.autoconfigure.core.token.AccessTokenManager;
import com.wftk.auth.spring.boot.autoconfigure.core.validator.PreAuthAccountValidator;
import com.wftk.auth.spring.boot.autoconfigure.ext.auth.manager.DefaultAuthenticationManager;
import com.wftk.auth.spring.boot.autoconfigure.ext.password.BcryptPasswordEncoder;
import com.wftk.auth.spring.boot.autoconfigure.ext.token.DefaultAccessTokenManager;
import com.wftk.auth.spring.boot.autoconfigure.properties.AuthProperties;

import org.redisson.api.RedissonClient;
import org.redisson.spring.starter.RedissonAutoConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2024/11/19 19:28
 */
@Configuration
@EnableConfigurationProperties(AuthProperties.class)
@AutoConfigureAfter(RedissonAutoConfiguration.class)
public class AuthAutoConfiguration {

    @ConditionalOnMissingBean
    @Bean
    PasswordEncoder passwordEncoder() {
        return new BcryptPasswordEncoder();
    }

    @ConditionalOnMissingBean
    @ConditionalOnClass(RedissonClient.class)
    @Bean
    RateLimiterFactory rateLimiterFactory(RedissonClient redissonClient) {
        return new RedissonRateLimiterFactory(redissonClient);
    }


    @ConditionalOnMissingBean
    @Bean
    AuthenticationManager authenticationManager(ClientLoader clientLoader, UserLoader<?> userLoader,
                                                ObjectPersistent objectPersistent, PasswordEncoder passwordEncoder,
                                                AuthProperties authProperties, @Autowired(required = false) PreAuthAccountValidator preAuthAccountValidator,
                                                RateLimiterFactory rateLimiterFactory) {
        return createAuthenticationManager(clientLoader, userLoader, objectPersistent, passwordEncoder, authProperties, preAuthAccountValidator, rateLimiterFactory);
    }


    @ConditionalOnMissingBean
    @Bean
    PreAuthenticationManager preAuthenticationManager(ClientLoader clientLoader, UserLoader<?> userLoader,
                                                   ObjectPersistent objectPersistent, PasswordEncoder passwordEncoder,
                                                   AuthProperties authProperties, @Autowired(required = false) PreAuthAccountValidator preAuthAccountValidator,
                                                   RateLimiterFactory rateLimiterFactory) {
        return createAuthenticationManager(clientLoader, userLoader, objectPersistent, passwordEncoder, authProperties, preAuthAccountValidator, rateLimiterFactory);
    }

    @ConditionalOnMissingBean
    @Bean
    AccessTokenManager accessTokenManager(ObjectPersistent objectPersistent, RedissonClient redissonClient) {
        return new DefaultAccessTokenManager(objectPersistent, redissonClient);
    }


    /**
     * 创建认证管理器
     * @param clientLoader
     * @param userLoader
     * @param objectPersistent
     * @param passwordEncoder
     * @param authProperties
     * @return
     */
    private DefaultAuthenticationManager createAuthenticationManager(ClientLoader clientLoader, UserLoader<?> userLoader,
                                                                     ObjectPersistent objectPersistent, PasswordEncoder passwordEncoder,
                                                                     AuthProperties authProperties, PreAuthAccountValidator preAuthAccountValidator,
                                                                     RateLimiterFactory rateLimiterFactory) {
        return new DefaultAuthenticationManager(clientLoader, userLoader, objectPersistent, authProperties, preAuthAccountValidator, passwordEncoder, rateLimiterFactory);
    }

}
