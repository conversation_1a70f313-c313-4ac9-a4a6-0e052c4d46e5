package com.wftk.auth.spring.boot.autoconfigure.ext.auth;

import com.wftk.auth.spring.boot.autoconfigure.core.auth.PreAuthenticationInfo;
import org.springframework.lang.NonNull;

import java.io.Serial;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2024/11/22 14:23
 */
public class DefaultPreAuthenticationInfo implements PreAuthenticationInfo {

    @Serial
    private static final long serialVersionUID = -416212685121574144L;
    private String clientId;
    private String clientSecret;

    /**
     * 授权类型
     */
    private String grantType;

    /**
     * 预授权类型（例如：短信验证码、图形验证码）
     */
    private String preGrantType;

    /**
     * 预授权账号（例如：短信验证码场景下的手机号，图形验证码场景下的请求ID）
     */
    private String preAuthAccount;

    /**
     * 例如：短信验证码场景下的短信验证码，图形验证码场景下的验证码
     */
    private String preAuthCode;


    @NonNull
    @Override
    public String getPreGrantType() {
        return preGrantType;
    }

    @NonNull
    @Override
    public String getGrantType() {
        return grantType;
    }

    @NonNull
    @Override
    public String getClientId() {
        return clientId;
    }

    @NonNull
    @Override
    public String getClientSecret() {
        return clientSecret;
    }

    @NonNull
    @Override
    public String getPreAuthAccount() {
        return preAuthAccount;
    }

    @NonNull
    @Override
    public String getPreAuthCode() {
        return preAuthCode;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }

    public void setPreGrantType(String preGrantType) {
        this.preGrantType = preGrantType;
    }

    public void setPreAuthAccount(String preAuthAccount) {
        this.preAuthAccount = preAuthAccount;
    }

    public void setPreAuthCode(String preAuthCode) {
        this.preAuthCode = preAuthCode;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        DefaultPreAuthenticationInfo that = (DefaultPreAuthenticationInfo) o;
        return Objects.equals(clientId, that.clientId) && Objects.equals(clientSecret, that.clientSecret) && Objects.equals(grantType, that.grantType) && Objects.equals(preGrantType, that.preGrantType) && Objects.equals(preAuthAccount, that.preAuthAccount) && Objects.equals(preAuthCode, that.preAuthCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(clientId, clientSecret, grantType, preGrantType, preAuthAccount, preAuthCode);
    }

    @Override
    public String toString() {
        return "DefaultPreAuthenticationInfo{" +
                "clientId='" + clientId + '\'' +
                ", clientSecret='" + clientSecret + '\'' +
                ", grantType='" + grantType + '\'' +
                ", preGrantType='" + preGrantType + '\'' +
                ", preAuthAccount='" + preAuthAccount + '\'' +
                ", preAuthCode='" + preAuthCode + '\'' +
                '}';
    }
}
