package com.wftk.auth.spring.boot.autoconfigure.ext.auth.user;

import cn.hutool.core.util.ReflectUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.user.AuthUser;

import java.io.Serial;

/**
 * <AUTHOR>
 * @create 2024/12/3 17:11
 */
public class DefaultAuthUser<U> extends AuthUser<U> {

    @Serial
    private static final long serialVersionUID = -3001426402341479233L;

    @JsonCreator
    public DefaultAuthUser(@JsonProperty("user") U user) {
        super(user);
    }

    @Override
    public String getTenantId() {
        return ReflectUtil.invoke(getUser(), "getTenantId");
    }


    @Override
    public Long getId() {
        return ReflectUtil.invoke(getUser(), "getId");
    }

    @Override
    public String getAccount() {
        return ReflectUtil.invoke(getUser(), "getAccount");
    }

    @Override
    public String getPassword() {
        return ReflectUtil.invoke(getUser(), "getPassword");
    }

    @Override
    public boolean isDisabled() {
        Boolean enable = ReflectUtil.invoke(getUser(), "getEnable");
        return enable != null && !enable;
    }
}
