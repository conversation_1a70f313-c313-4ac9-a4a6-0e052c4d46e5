package com.wftk.auth.spring.boot.autoconfigure.ext.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wftk.auth.spring.boot.autoconfigure.ext.auth.DefaultPreAuthenticationInfo;
import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @create 2024/11/26 11:36
 */
public class PreAuthenticationDTO {

    @JsonProperty("client_id")
    @NotBlank
    private String clientId;

    @JsonProperty("client_secret")
    @NotBlank
    private String clientSecret;

    /**
     * 授权类型
     */
    @JsonProperty("grant_type")
    @NotBlank
    private String grantType;

    /**
     * 预授权类型（例如：短信验证码、图形验证码）
     */
    @JsonProperty("pre_grant_type")
    @NotBlank
    private String preGrantType;

    /**
     * 预授权账号（例如：短信验证码场景下的手机号，图形验证码场景下的请求ID）
     */
    @JsonProperty("pre_auth_account")
    @NotBlank
    private String preAuthAccount;

    /**
     * 转entity
     * @return
     */
    public DefaultPreAuthenticationInfo toEntity() {
        DefaultPreAuthenticationInfo preAuthenticationInfo = new DefaultPreAuthenticationInfo();
        preAuthenticationInfo.setPreGrantType(preGrantType);
        preAuthenticationInfo.setClientId(clientId);
        preAuthenticationInfo.setClientSecret(clientSecret);
        preAuthenticationInfo.setGrantType(grantType);
        preAuthenticationInfo.setPreAuthAccount(preAuthAccount);
        return preAuthenticationInfo;
    }




    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getGrantType() {
        return grantType;
    }

    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }

    public String getPreGrantType() {
        return preGrantType;
    }

    public void setPreGrantType(String preGrantType) {
        this.preGrantType = preGrantType;
    }

    public String getPreAuthAccount() {
        return preAuthAccount;
    }

    public void setPreAuthAccount(String preAuthAccount) {
        this.preAuthAccount = preAuthAccount;
    }

    @Override
    public String toString() {
        return "PreAuthenticationDTO{" +
                "clientId='" + clientId + '\'' +
                ", clientSecret='" + clientSecret + '\'' +
                ", grantType='" + grantType + '\'' +
                ", preGrantType='" + preGrantType + '\'' +
                ", preAuthAccount='" + preAuthAccount + '\'' +
                '}';
    }
}
