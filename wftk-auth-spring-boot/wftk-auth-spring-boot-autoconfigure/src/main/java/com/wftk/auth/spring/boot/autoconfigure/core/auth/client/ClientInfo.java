package com.wftk.auth.spring.boot.autoconfigure.core.auth.client;


import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.wftk.auth.spring.boot.autoconfigure.properties.AuthProperties;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.io.Serializable;
import java.util.Collection;

/**
 * <AUTHOR>
 * @create 2024/11/20 14:20
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, property = "class")
public interface ClientInfo extends Serializable {

    /**
     * 租户ID
     * @return
     */
    @Nullable
    String getTenantId();

    @NonNull
    String getClientId();

    String getClientSecret();

    /**
     * 获取配置
     * @return
     */
    @NonNull
    ClientConfig getClientConfig();





    /**
     * client配置
     */
    @JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, property = "class")
    interface ClientConfig {

        /**
         * 预授权类型
         * @return
         */
        Collection<String> getPreGrantType();


        /**
         * 授权类型
         * @return
         */
        Collection<String> getGrantType();


        /**
         * 预认证信息留存时间
         */
        Long getPreAuthExpireInSeconds();

        /**
         * 预认证间隔时间（例如60S内只能发送1次短信）
         */
        Long getPreAuthDurationInSeconds();

        /**
         * accessToken强制重置时间(即此时间后强制清空用户所有accessToken)
         * @return
         */
        Long getAccessTokenForceResetInSeconds();

        /**
         * 令牌过期时间
         */
        Long getAccessTokenExpireInSeconds();


        /**
         * 当accessToken尚未过期，又重新生成新accessToken后，老accessToken过渡时间（过渡时间内仍可使用）
         */
        Long getAccessTokenTransitionInSeconds();


        /**
         * 最大并发会话数
         * @return
         */
        Integer getMaxConcurrentSessions();

        /**
         * 合并配置
         * @param clientProperties
         * @return
         */
        void mergeProperties(AuthProperties.ClientProperties clientProperties);

    }
}
