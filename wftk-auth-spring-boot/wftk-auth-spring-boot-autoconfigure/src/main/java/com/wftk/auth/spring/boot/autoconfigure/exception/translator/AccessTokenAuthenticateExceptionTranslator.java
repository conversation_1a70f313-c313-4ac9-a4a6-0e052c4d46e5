package com.wftk.auth.spring.boot.autoconfigure.exception.translator;

import com.wftk.auth.spring.boot.autoconfigure.exception.token.AccessTokenAuthenticateException;
import com.wftk.exception.common.GlobalErrorConstants;
import com.wftk.exception.common.HttpStatusCode;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.translator.base.AbstractExceptionTranslator;

/**
 * <AUTHOR>
 * @create 2024/11/21 18:26
 */
public class AccessTokenAuthenticateExceptionTranslator extends AbstractExceptionTranslator {
    @Override
    protected ResolvedResult doTranslate(Throwable throwable) {
        return ResolvedResultBuilder.build(GlobalErrorConstants.UNAUTHORIZED.code(), "无效令牌", HttpStatusCode.UNAUTHORIZED);
    }

    @Override
    public boolean support(Throwable throwable) {
        return throwable instanceof AccessTokenAuthenticateException;
    }
}
