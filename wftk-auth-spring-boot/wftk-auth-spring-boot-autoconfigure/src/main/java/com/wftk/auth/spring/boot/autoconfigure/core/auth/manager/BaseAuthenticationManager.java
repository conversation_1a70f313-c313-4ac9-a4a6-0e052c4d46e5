package com.wftk.auth.spring.boot.autoconfigure.core.auth.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.Authentication;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.AuthenticationInfo;
import com.wftk.auth.spring.boot.autoconfigure.core.validator.PreAuthAccountValidator;
import com.wftk.auth.spring.boot.autoconfigure.exception.auth.*;
import com.wftk.auth.spring.boot.autoconfigure.ext.auth.DefaultAuthentication;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.PreAuthenticationInfo;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.client.ClientInfo;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.user.AuthUser;
import com.wftk.auth.spring.boot.autoconfigure.core.limitation.RateLimiter;
import com.wftk.auth.spring.boot.autoconfigure.core.limitation.factory.RateLimiterFactory;
import com.wftk.auth.spring.boot.autoconfigure.core.loader.ClientLoader;
import com.wftk.auth.spring.boot.autoconfigure.core.loader.UserLoader;
import com.wftk.auth.spring.boot.autoconfigure.core.persistent.ObjectPersistent;
import com.wftk.auth.spring.boot.autoconfigure.properties.AuthProperties;

import java.time.Duration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.Nullable;

/**
 * <AUTHOR>
 * @create 2024/11/20 13:45
 */
public abstract class BaseAuthenticationManager implements PreAuthenticationManager {

    protected final Logger logger = LoggerFactory.getLogger(getClass());
    protected final ClientLoader clientLoader;
    protected final UserLoader<?> userLoader;
    protected final ObjectPersistent objectPersistent;
    protected final AuthProperties authProperties;
    protected final PreAuthAccountValidator preAuthAccountValidator;
    protected final RateLimiterFactory rateLimiterFactory;

    protected BaseAuthenticationManager(ClientLoader clientLoader, UserLoader<?> userLoader, ObjectPersistent objectPersistent,
                                        AuthProperties authProperties, @Nullable PreAuthAccountValidator preAuthAccountValidator) {
        this(clientLoader, userLoader, objectPersistent, authProperties, preAuthAccountValidator, null);
    }


    protected BaseAuthenticationManager(ClientLoader clientLoader, UserLoader<?> userLoader, ObjectPersistent objectPersistent,
                                        AuthProperties authProperties, @Nullable PreAuthAccountValidator preAuthAccountValidator, RateLimiterFactory rateLimiterFactory) {
        this.clientLoader = clientLoader;
        this.userLoader = userLoader;
        this.objectPersistent = objectPersistent;
        this.authProperties = authProperties;
        this.preAuthAccountValidator = preAuthAccountValidator;
        this.rateLimiterFactory = rateLimiterFactory;
    }

    @Override
    public void preAuthenticate(PreAuthenticationInfo preAuthenticationInfo) throws AuthenticateException {
        ClientInfo clientInfo = loadClientInfoAndCombineProperties(preAuthenticationInfo.getClientId());
        String preAuthKey = getPreAuthKey(clientInfo.getTenantId(), preAuthenticationInfo);
        if (rateLimiterFactory != null) {
            RateLimiter rateLimiter = rateLimiterFactory.getRateLimiter(getPreAuthLimiterKey(preAuthKey), 1, Duration.ofSeconds(clientInfo.getClientConfig().getPreAuthDurationInSeconds()));
            if (!rateLimiter.tryAcquire()) {
                throw new PreAuthAccountLimitedException("请求过于频繁，请稍后再试");
            }
        }
        validateClientInfo(clientInfo, preAuthenticationInfo.getClientSecret(), preAuthenticationInfo.getPreGrantType(), null);
        if (preAuthAccountValidator != null && !preAuthAccountValidator.validate(preAuthenticationInfo.getPreAuthAccount(), preAuthenticationInfo.getPreGrantType())) {
            throw new InvalidPreAuthAccountException("invalid preAuthAccount.");
        }
        if (objectPersistent.isExist(preAuthKey)) {
            logger.warn("preAuthKey [{}] will be deleted.", preAuthKey);
            //如果存在，则清除之前的数据
            objectPersistent.delete(preAuthKey);
        }
        objectPersistent.save(preAuthKey, preAuthenticationInfo, clientInfo.getClientConfig().getPreAuthExpireInSeconds());
        logger.info("preAuthenticationInfo: [{}] saved.", preAuthenticationInfo);
    }


    @Override
    public boolean preAuthenticateExist(PreAuthenticationInfo preAuthenticationInfo) {
        ClientInfo clientInfo = loadClientInfoAndCombineProperties(preAuthenticationInfo.getClientId());
        String preAuthKey = getPreAuthKey(clientInfo.getTenantId(), preAuthenticationInfo);
        return objectPersistent.isExist(preAuthKey);
    }


    @Override
    public void preAuthenticateValidate(ClientInfo clientInfo, PreAuthenticationInfo preAuthenticationInfo) throws AuthenticateException {
        if (CollUtil.isEmpty(clientInfo.getClientConfig().getPreGrantType())) {
            return;
        }
        if (!CollUtil.contains(clientInfo.getClientConfig().getPreGrantType(), preAuthenticationInfo.getPreGrantType())) {
            throw new InvalidPreAuthenticationInfoException("invalid preGrantType.");
        }
        String preAuthKey = getPreAuthKey(clientInfo.getTenantId(), preAuthenticationInfo);
        PreAuthenticationInfo persistedInfo = objectPersistent.get(preAuthKey, preAuthenticationInfo.getClass());
        if (!ObjectUtil.equals(persistedInfo, preAuthenticationInfo)) {
            logger.error("invalid preAuthenticationInfo: {}", preAuthenticationInfo);
            throw new InvalidPreAuthenticationInfoException("invalid preAuthenticationInfo.");
        }
    }

    @Override
    public Authentication authenticate(AuthenticationInfo authenticationInfo) throws AuthenticateException {
        ClientInfo clientInfo = loadClientInfoAndCombineProperties(authenticationInfo.getClientId());
        validateClientInfo(clientInfo, authenticationInfo.getClientSecret(), authenticationInfo.getPreGrantType(), authenticationInfo.getGrantType());
        //预授权信息校验
        preAuthenticateValidate(clientInfo, authenticationInfo.toPreAuthenticationInfo());

        AuthUser<?> authUser = userLoader.load(authenticationInfo);
        if (authUser == null) {
            throw new InvalidUserException("user is not exists: " + authenticationInfo.getAccount());
        }
        //用户信息校验
        doUserInfoValidate(authUser, authenticationInfo);
        Authentication authentication = createAuthentication(clientInfo, authUser);
        //清除预授权信息
        if (StrUtil.isNotBlank(authenticationInfo.getPreGrantType())
                || CollUtil.contains(clientInfo.getClientConfig().getPreGrantType(), authenticationInfo.getGrantType())) {
            //预授权信息
            PreAuthenticationInfo preAuthenticationInfo = authenticationInfo.toPreAuthenticationInfo();
            String preAuthKey = getPreAuthKey(clientInfo.getTenantId(), preAuthenticationInfo);
            objectPersistent.delete(preAuthKey);
            logger.info("preAuthenticationInfo: [{}] deleted.", preAuthenticationInfo);
            //清除limiter
            if (rateLimiterFactory != null) {
                RateLimiter rateLimiter = rateLimiterFactory.getRateLimiter(getPreAuthLimiterKey(preAuthKey), 1, Duration.ofSeconds(clientInfo.getClientConfig().getPreAuthDurationInSeconds()));
                rateLimiter.close();
                logger.info("rateLimiter: [{}] closed.", rateLimiter);
            }
        }
        return authentication;
    }


    /**
     * 获取clientInfo并合并配置信息
     * @param clientId
     * @return
     */
    protected ClientInfo loadClientInfoAndCombineProperties(String clientId) {
        ClientInfo clientInfo = clientLoader.load(clientId);
        if (clientInfo == null) {
            throw new InvalidClientException("client is not exists: " + clientId);
        }
        ClientInfo.ClientConfig clientConfig = clientInfo.getClientConfig();
        clientConfig.mergeProperties(authProperties.getClient());
        return clientInfo;
    }


    /**
     * 校验用户信息
     * @param authUser
     * @param authenticationInfo
     */
    protected void doUserInfoValidate(AuthUser<?> authUser, AuthenticationInfo authenticationInfo) throws AuthenticateException {
        if (authUser.isDisabled()) {
            throw new InvalidUserException("user is disabled: " + authUser.getAccount());
        }
        if (!passwordValidate(authenticationInfo.getPassword(), authUser.getPassword())) {
            throw new InvalidPasswordException("invalid password.");
        }
    }


    /**
     *
     * @param authUser
     * @return
     */
    protected Authentication createAuthentication(ClientInfo clientInfo, AuthUser<?> authUser) {
       return new DefaultAuthentication(clientInfo, authUser, true);
    }


    /**
     * 校验密码
     * @param rawPassword
     * @param encodedPassword
     * @return
     */
    protected abstract boolean passwordValidate(String rawPassword, String encodedPassword);


    /**
     *
     * @param clientInfoInDB
     * @param clientSecret
     * @param preGrantType
     * @param grantType
     * @throws AuthenticateException
     */
    protected void validateClientInfo(ClientInfo clientInfoInDB, String clientSecret, String preGrantType, String grantType) throws AuthenticateException {
        if (!StrUtil.equalsIgnoreCase(clientInfoInDB.getClientSecret(), clientSecret)) {
            throw new InvalidClientException("invalid client secret. clientId: " + clientInfoInDB.getClientId() + ", clientSecret: " + clientSecret);
        }
        if (StrUtil.isNotBlank(preGrantType) && !CollUtil.contains(clientInfoInDB.getClientConfig().getPreGrantType(), preGrantType)) {
            throw new InvalidClientException("invalid preGrantType. clientId: " + clientInfoInDB.getClientId() + ", preGrantType: " + preGrantType);
        }
        if (StrUtil.isNotBlank(grantType) && !CollUtil.contains(clientInfoInDB.getClientConfig().getGrantType(), grantType)) {
            throw new InvalidClientException("invalid grantType. clientId: " + clientInfoInDB.getClientId() + ", grantType: " + grantType);
        }
    }

    /**
     * 获取预授权key
     * @param tenantId
     * @param preAuthenticationInfo
     * @return
     */
    protected String getPreAuthKey(String tenantId, PreAuthenticationInfo preAuthenticationInfo) {
        if (StrUtil.isBlank(tenantId)) {
            return StrUtil.format("PRE_AUTH:{}:{}:{}", preAuthenticationInfo.getClientId(),
                    preAuthenticationInfo.getPreGrantType(), preAuthenticationInfo.getPreAuthAccount());
        }
        return StrUtil.format("PRE_AUTH:{}:{}:{}:{}", tenantId, preAuthenticationInfo.getClientId(),
                preAuthenticationInfo.getPreGrantType(), preAuthenticationInfo.getPreAuthAccount());
    }


    /**
     * 获取预授权限流key
     * @param preAuthKey
     * @return
     */
    protected String getPreAuthLimiterKey(String preAuthKey) {
        return StrUtil.format("PRE_AUTH_LIMITER:{}", preAuthKey);
    }
}
