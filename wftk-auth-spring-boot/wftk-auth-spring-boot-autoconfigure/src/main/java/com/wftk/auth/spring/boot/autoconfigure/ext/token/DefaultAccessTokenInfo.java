package com.wftk.auth.spring.boot.autoconfigure.ext.token;

import com.wftk.auth.spring.boot.autoconfigure.enums.TokenType;

import cn.hutool.core.util.IdUtil;

import java.io.Serial;
import java.io.Serializable;

import com.wftk.auth.spring.boot.autoconfigure.core.token.AccessTokenInfo;

/**
 * <AUTHOR>
 * @create 2024/11/21 10:35
 */
public class DefaultAccessTokenInfo implements AccessTokenInfo, Serializable {

    private final Long id;
    private final String accessToken;
    private final String type;
    private final Long expireInSeconds;
    private final String userAccount;

    @Serial
    private static final long serialVersionUID = 1346002279225024329L;

    public DefaultAccessTokenInfo(String accessToken, TokenType type, Long expireInSeconds, String userAccount) {
        this.id = IdUtil.getSnowflakeNextId();
        this.accessToken = accessToken;
        this.type = type.getType();
        this.expireInSeconds = expireInSeconds;
        this.userAccount = userAccount;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public String getAccessToken() {
        return accessToken;
    }

    @Override
    public String getType() {
        return type;
    }

    @Override
    public Long getExpireInSeconds() {
        return expireInSeconds;
    }

    @Override
    public String getUserAccount() {
        return userAccount;
    }
}
