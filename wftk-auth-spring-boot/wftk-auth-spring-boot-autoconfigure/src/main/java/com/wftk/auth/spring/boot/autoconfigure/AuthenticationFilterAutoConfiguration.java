package com.wftk.auth.spring.boot.autoconfigure;

import com.wftk.auth.spring.boot.autoconfigure.core.token.AccessTokenManager;
import com.wftk.auth.spring.boot.autoconfigure.exception.translator.AccessTokenAuthenticateExceptionTranslator;
import com.wftk.auth.spring.boot.autoconfigure.exception.translator.AuthenticateExceptionTranslator;
import com.wftk.auth.spring.boot.autoconfigure.exception.translator.InvalidRequestExceptionTranslator;
import com.wftk.auth.spring.boot.autoconfigure.filter.AuthenticationFilter;
import com.wftk.auth.spring.boot.autoconfigure.filter.AuthenticationValidationFilter;
import com.wftk.auth.spring.boot.autoconfigure.properties.AuthProperties;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerExceptionResolver;

/**
 * <AUTHOR>
 * @create 2024/11/21 18:30
 */
@AutoConfigureAfter(AuthAutoConfiguration.class)
@Configuration
@ConditionalOnProperty(prefix = "config.auth.filter", name = "enable", havingValue = "true", matchIfMissing = true)
public class AuthenticationFilterAutoConfiguration {


    @ConditionalOnMissingBean
    @Bean
    AuthenticateExceptionTranslator authenticateExceptionTranslator() {
        return new AuthenticateExceptionTranslator();
    }

    @ConditionalOnMissingBean
    @Bean
    AccessTokenAuthenticateExceptionTranslator accessTokenAuthenticateExceptionTranslator() {
        return new AccessTokenAuthenticateExceptionTranslator();
    }

    @ConditionalOnMissingBean
    @Bean
    InvalidRequestExceptionTranslator invalidRequestExceptionTranslator() {
        return new InvalidRequestExceptionTranslator();
    }



    @ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
    @Bean
    FilterRegistrationBean<AuthenticationFilter> authenticationFilterRegistrationBean(AccessTokenManager accessTokenManager,
                                                                                  AuthProperties authProperties,
                                                                                  @Qualifier("handlerExceptionResolver") HandlerExceptionResolver handlerExceptionResolver) {
        FilterRegistrationBean<AuthenticationFilter> authenticationFilterRegistrationBean = new FilterRegistrationBean<>();
        AuthProperties.FilterProperties filterConfig = authProperties.getFilter();
        AuthenticationFilter authenticationFilter = new AuthenticationFilter(handlerExceptionResolver, accessTokenManager, filterConfig.getIgnorePatterns());
        authenticationFilterRegistrationBean.setFilter(authenticationFilter);
        authenticationFilterRegistrationBean.setName(filterConfig.getName());
        authenticationFilterRegistrationBean.setOrder(filterConfig.getOrder());
        authenticationFilterRegistrationBean.setUrlPatterns(filterConfig.getPatterns());
        return authenticationFilterRegistrationBean;
    }


    @ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
    @Bean
    FilterRegistrationBean<AuthenticationValidationFilter> authenticationValidationFilterFilterRegistrationBean(AuthProperties authProperties,
                                                                                                @Qualifier("handlerExceptionResolver") HandlerExceptionResolver handlerExceptionResolver) {
        FilterRegistrationBean<AuthenticationValidationFilter> authenticationValidationFilterFilterRegistrationBean = new FilterRegistrationBean<>();
        AuthProperties.FilterProperties filterConfig = authProperties.getFilter();
        AuthenticationValidationFilter authenticationValidationFilter = new AuthenticationValidationFilter(handlerExceptionResolver,
                filterConfig.getIgnorePatterns(), authProperties.getValidation());
        authenticationValidationFilterFilterRegistrationBean.setFilter(authenticationValidationFilter);
        authenticationValidationFilterFilterRegistrationBean.setName("authenticationValidationFilter");
        authenticationValidationFilterFilterRegistrationBean.setOrder(filterConfig.getOrder() + 100);
        authenticationValidationFilterFilterRegistrationBean.setUrlPatterns(filterConfig.getPatterns());
        return authenticationValidationFilterFilterRegistrationBean;
    }

}
