package com.wftk.auth.spring.boot.autoconfigure.exception.auth;

/**
 * <AUTHOR>
 * @create 2024/11/20 14:38
 */
public class InvalidUserException extends AuthenticateException {

    public InvalidUserException() {
    }

    public InvalidUserException(String message) {
        super(message);
    }

    public InvalidUserException(String message, Throwable cause) {
        super(message, cause);
    }

    public InvalidUserException(Throwable cause) {
        super(cause);
    }

    public InvalidUserException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
