package com.wftk.auth.spring.boot.autoconfigure.ext.password;

import cn.hutool.crypto.digest.BCrypt;
import com.wftk.auth.spring.boot.autoconfigure.core.password.PasswordEncoder;

/**
 * BCrypt密码编码器
 * <AUTHOR>
 * @create 2024/11/21 14:39
 */
public class BcryptPasswordEncoder implements PasswordEncoder {
    @Override
    public String encode(String rawPassword) {
        return BCrypt.hashpw(rawPassword);
    }

    @Override
    public boolean matches(String rawPassword, String encodedPassword) {
        return BCrypt.checkpw(rawPassword, encodedPassword);
    }
}
