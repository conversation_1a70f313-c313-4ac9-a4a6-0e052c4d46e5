package com.wftk.auth.spring.boot.autoconfigure.ext.persistent;

import cn.hutool.core.util.StrUtil;
import com.wftk.auth.spring.boot.autoconfigure.core.persistent.ObjectPersistent;
import com.wftk.auth.spring.boot.autoconfigure.core.persistent.serializer.PersistentSerializer;
import com.wftk.cache.spring.boot.autoconfigure.store.redisson.ObjectRedissonObjectStore;


/**
 * <AUTHOR>
 * @create 2024/11/20 14:02
 */
public class RedissonObjectPersistent implements ObjectPersistent {

    private final ObjectRedissonObjectStore objectRedissonObjectStore;
    private final PersistentSerializer persistentSerializer;
    private final String namespace;

    public RedissonObjectPersistent(ObjectRedissonObjectStore objectRedissonObjectStore, PersistentSerializer persistentSerializer) {
        this(objectRedissonObjectStore, persistentSerializer, null);
    }

    public RedissonObjectPersistent(ObjectRedissonObjectStore objectRedissonObjectStore, PersistentSerializer persistentSerializer, String namespace) {
        this.objectRedissonObjectStore = objectRedissonObjectStore;
        this.persistentSerializer = persistentSerializer;
        this.namespace = namespace;
    }

    @Override
    public <V> void save(String key, V value) {
        key = getKey(key);
        byte[] serialize = persistentSerializer.serialize(value);
        objectRedissonObjectStore.save(key, serialize);
    }

    @Override
    public <V> void save(String key, V value, long expireInSeconds) {
        key = getKey(key);
        byte[] serialize = persistentSerializer.serialize(value);
        objectRedissonObjectStore.save(key, serialize, expireInSeconds);
    }

    @Override
    public <V> V get(String key, Class<V> clazz) {
        key = getKey(key);
        Object object = objectRedissonObjectStore.getObject(key);
        if (object == null) {
            return null;
        }
        if (object instanceof byte[]) {
            return persistentSerializer.deserialize((byte[]) object, clazz);
        }
        return (V) object;
    }

    @Override
    public boolean isExist(String key) {
        key = getKey(key);
        return objectRedissonObjectStore.getObject(key) != null;
    }

    @Override
    public Long ttl(String key) {
        key = getKey(key);
        return objectRedissonObjectStore.ttl(key);
    }

    @Override
    public void delete(String key) {
        key = getKey(key);
        objectRedissonObjectStore.remove(key);
    }

    /**
     * 获取key
     * @param key
     * @return
     */
    private String getKey(String key) {
        return StrUtil.isBlank(namespace) ? key : namespace.toLowerCase() + ":" + key;
    }
}
