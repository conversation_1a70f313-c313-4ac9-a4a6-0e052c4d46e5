package com.wftk.auth.spring.boot.autoconfigure.ext.auth.client;

import cn.hutool.core.collection.CollUtil;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.client.ClientInfo;
import com.wftk.auth.spring.boot.autoconfigure.properties.AuthProperties;

import java.util.Collection;

/**
 * <AUTHOR>
 * @create 2024/11/21 14:26
 */
public class DefaultClientConfig implements ClientInfo.ClientConfig {

    private Collection<String> preGrantType;
    private Collection<String> grantType;
    private Long preAuthExpireInSeconds;
    private Long accessTokenExpireInSeconds;
    private Long accessTokenTransitionInSeconds;
    private Integer maxConcurrentSessions;
    private Long preAuthDurationInSeconds;
    private Long accessTokenForceResetInSeconds;

    @Override
    public Collection<String> getPreGrantType() {
        return preGrantType;
    }

    @Override
    public Collection<String> getGrantType() {
        return grantType;
    }

    @Override
    public Long getPreAuthExpireInSeconds() {
        return preAuthExpireInSeconds;
    }

    @Override
    public Long getAccessTokenExpireInSeconds() {
        return accessTokenExpireInSeconds;
    }

    @Override
    public Long getAccessTokenTransitionInSeconds() {
        return accessTokenTransitionInSeconds;
    }

    @Override
    public Integer getMaxConcurrentSessions() {
        return maxConcurrentSessions;
    }

    @Override
    public void mergeProperties(AuthProperties.ClientProperties clientProperties) {
        if (CollUtil.isEmpty(preGrantType)) {
            this.preGrantType = clientProperties.getPreGrantType();
        }
        if (CollUtil.isEmpty(grantType)) {
            this.grantType = clientProperties.getGrantType();
        }
        if (preAuthExpireInSeconds == null) {
            this.preAuthExpireInSeconds = clientProperties.getPreAuthExpireInSeconds();
        }
        if (accessTokenExpireInSeconds == null) {
            this.accessTokenExpireInSeconds = clientProperties.getAccessTokenExpireInSeconds();
        }
        if (accessTokenTransitionInSeconds == null) {
            this.accessTokenTransitionInSeconds = clientProperties.getAccessTokenTransitionInSeconds();
        }
        if (maxConcurrentSessions == null) {
            setMaxConcurrentSessions(clientProperties.getMaxConcurrentSessions());
        }
        if (preAuthDurationInSeconds == null) {
            this.preAuthDurationInSeconds = clientProperties.getPreAuthDurationInSeconds();
        }
        if (accessTokenForceResetInSeconds == null) {
            this.accessTokenForceResetInSeconds = clientProperties.getAccessTokenForceResetInSeconds();
        }
    }


    public void setPreGrantType(Collection<String> preGrantType) {
        if (CollUtil.isNotEmpty(preGrantType)) {
            this.preGrantType = preGrantType;
        }
    }

    public void setGrantType(Collection<String> grantType) {
        if (CollUtil.isNotEmpty(grantType)) {
            this.grantType = grantType;
        }
    }

    public void setPreAuthExpireInSeconds(Long preAuthExpireInSeconds) {
        if (preAuthExpireInSeconds != null) {
            this.preAuthExpireInSeconds = preAuthExpireInSeconds;
        }
    }

    public void setAccessTokenExpireInSeconds(Long accessTokenExpireInSeconds) {
        if (accessTokenExpireInSeconds != null) {
            this.accessTokenExpireInSeconds = accessTokenExpireInSeconds;
        }
    }

    public void setAccessTokenTransitionInSeconds(Long accessTokenTransitionInSeconds) {
        if (accessTokenTransitionInSeconds != null) {
            this.accessTokenTransitionInSeconds = accessTokenTransitionInSeconds;
        }
    }

    public void setMaxConcurrentSessions(Integer maxConcurrentSessions) {
        if (maxConcurrentSessions != null) {
            if (maxConcurrentSessions < 1) {
                throw new IllegalArgumentException("max concurrent sessions must be greater than 0");
            }
            this.maxConcurrentSessions = maxConcurrentSessions;
        }
    }

    public void setPreAuthDurationInSeconds(Long preAuthDurationInSeconds) {
        if (preAuthDurationInSeconds != null) {
            this.preAuthDurationInSeconds = preAuthDurationInSeconds;
        }
    }

    @Override
    public Long getPreAuthDurationInSeconds() {
        return preAuthDurationInSeconds;
    }

    @Override
    public Long getAccessTokenForceResetInSeconds() {
        return accessTokenForceResetInSeconds;
    }
}
