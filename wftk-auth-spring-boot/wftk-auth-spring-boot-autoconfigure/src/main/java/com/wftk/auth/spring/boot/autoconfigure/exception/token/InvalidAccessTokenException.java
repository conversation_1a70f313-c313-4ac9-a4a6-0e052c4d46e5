package com.wftk.auth.spring.boot.autoconfigure.exception.token;


/**
 * <AUTHOR>
 * @create 2024/11/21 15:50
 */
public class InvalidAccessTokenException extends AccessTokenAuthenticateException {

    public InvalidAccessTokenException() {
    }

    public InvalidAccessTokenException(String message) {
        super(message);
    }

    public InvalidAccessTokenException(String message, Throwable cause) {
        super(message, cause);
    }

    public InvalidAccessTokenException(Throwable cause) {
        super(cause);
    }

    public InvalidAccessTokenException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
