package com.wftk.auth.spring.boot.autoconfigure.ext.token;

import cn.hutool.core.util.IdUtil;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.Authentication;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.client.ClientInfo;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.client.ClientInfo.ClientConfig;
import com.wftk.auth.spring.boot.autoconfigure.enums.TokenType;
import com.wftk.auth.spring.boot.autoconfigure.exception.token.InvalidAccessTokenException;
import com.wftk.auth.spring.boot.autoconfigure.core.persistent.ObjectPersistent;
import com.wftk.auth.spring.boot.autoconfigure.core.token.AccessTokenInfo;
import com.wftk.auth.spring.boot.autoconfigure.core.token.AccessTokenManager;

import org.redisson.api.RedissonClient;
import org.redisson.api.RSetCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Comparator;
import java.util.concurrent.TimeUnit;

/**
 *<AUTHOR>
 *@create 2024/11/20 18:25
 */
public class DefaultAccessTokenManager implements AccessTokenManager {


    protected final Logger logger = LoggerFactory.getLogger(getClass());
    private final ObjectPersistent objectPersistent;
    private final RedissonClient redissonClient;

    public DefaultAccessTokenManager(ObjectPersistent objectPersistent, RedissonClient redissonClient) {
        this.objectPersistent = objectPersistent;
        this.redissonClient = redissonClient;
    }

    @Override
    public AccessTokenInfo grant(Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new IllegalStateException("illegal authentication.");
        }
        ClientInfo clientInfo = authentication.getClientInfo();
        String accessToken = IdUtil.fastSimpleUUID();
        String account = authentication.getAuthUser().getAccount();
        AccessTokenInfo tokenInfo = new DefaultAccessTokenInfo(accessToken, TokenType.BEARER, clientInfo.getClientConfig().getAccessTokenExpireInSeconds(), account);
        saveToken(authentication, tokenInfo, clientInfo.getClientConfig());
        return tokenInfo;
    }

    @Override
    public void remove(String accessToken) {
        Authentication authentication = objectPersistent.get(accessToken, Authentication.class);
        if (authentication == null) {
            return;
        }
        objectPersistent.delete(accessToken);
        String account = authentication.getAuthUser().getAccount();
        RSetCache<AccessTokenInfo> userTokens = redissonClient.getSetCache(getUserTokenKey(account));
        userTokens.removeIf(it -> it.getAccessToken().equals(accessToken));
        logger.info("access token removed.[{}]", accessToken);
    }

    @Override
    public Authentication extract(String accessToken) {
        Authentication authentication = objectPersistent.get(accessToken, Authentication.class);
        if (authentication == null) {
            throw new InvalidAccessTokenException("invalid access token: " + accessToken);
        }
        return authentication;
    }


    /**
     * 保存token
     * @param authentication
     * @param accessTokenInfo
     */
    private void saveToken(Authentication authentication, AccessTokenInfo accessTokenInfo, ClientConfig clientConfig) {
        String account = authentication.getAuthUser().getAccount();
        String accessToken = accessTokenInfo.getAccessToken();
        RSetCache<AccessTokenInfo> userTokens = redissonClient.getSetCache(getUserTokenKey(account));
        if (userTokens.remainTimeToLive() == -1) {
            //如果未设置过期时间，则设置过期时间
            userTokens.expire(Duration.ofSeconds(clientConfig.getAccessTokenForceResetInSeconds()));
            logger.info("access token force reset time set.[{}]", clientConfig.getAccessTokenForceResetInSeconds());
        }
        AccessTokenInfo firstAccessTokenInfo = userTokens.stream()
                .sorted(Comparator.comparing(AccessTokenInfo::getId))
                .findFirst()
                .orElse(null);
        if (firstAccessTokenInfo != null) {
            String firstAccessToken = firstAccessTokenInfo.getAccessToken();
            if (clientConfig.getMaxConcurrentSessions() == 1) {
                if (clientConfig.getAccessTokenTransitionInSeconds() != null && clientConfig.getAccessTokenTransitionInSeconds() > 0) {
                    //为老令牌设置过渡期
                    objectPersistent.save(firstAccessToken, extract(firstAccessToken), clientConfig.getAccessTokenTransitionInSeconds());
                    userTokens.remove(firstAccessTokenInfo);
                    userTokens.add(accessTokenInfo, clientConfig.getAccessTokenTransitionInSeconds(), TimeUnit.SECONDS);
                    logger.info("first access token transition period set.[{}]", firstAccessToken);
                } else {
                    objectPersistent.delete(firstAccessToken);
                    userTokens.remove(firstAccessTokenInfo);
                    logger.info("first access token removed.[{}]", firstAccessToken);
                }
            } else {
                //允许多个会话，则先判断当前会话数，如果会话数大于最大会话数，则删除最早的会话
                int currentConcurrentSessions = userTokens.size();
                logger.info("current concurrent sessions: [{}]", currentConcurrentSessions);
                if (currentConcurrentSessions >= clientConfig.getMaxConcurrentSessions()) {
                    objectPersistent.delete(firstAccessToken);
                    userTokens.remove(firstAccessTokenInfo);
                    logger.info("first access token removed.[{}]", firstAccessToken);
                }
            }
        }
        //添加新令牌
        Long expireInSeconds = accessTokenInfo.getExpireInSeconds();
        objectPersistent.save(accessToken, authentication, expireInSeconds);
        userTokens.add(accessTokenInfo, expireInSeconds, TimeUnit.SECONDS);
        logger.info("access token saved.[{}]", accessToken);
    }


    @Override
    public void removeWithAccount(String account) {
        RSetCache<AccessTokenInfo> userTokens = redissonClient.getSetCache(getUserTokenKey(account));
        userTokens.stream().forEach(it -> {
            objectPersistent.delete(it.getAccessToken());
        });
        userTokens.delete();
        logger.info("access tokens removed. user account: [{}]", account);
    }


    private String getUserTokenKey(String account) {
        return "USER_TOKEN:" + account;
    }
}
