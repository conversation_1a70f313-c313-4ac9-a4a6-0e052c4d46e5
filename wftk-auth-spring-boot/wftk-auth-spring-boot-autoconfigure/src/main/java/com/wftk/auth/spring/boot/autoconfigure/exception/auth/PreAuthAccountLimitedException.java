package com.wftk.auth.spring.boot.autoconfigure.exception.auth;

public class PreAuthAccountLimitedException extends AuthenticateException {

    public PreAuthAccountLimitedException() {
    }

    public PreAuthAccountLimitedException(String message) {
        super(message);
    }

    public PreAuthAccountLimitedException(String message, Throwable cause) {
        super(message, cause);
    }

    public PreAuthAccountLimitedException(Throwable cause) {
        super(cause);
    }

    public PreAuthAccountLimitedException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
