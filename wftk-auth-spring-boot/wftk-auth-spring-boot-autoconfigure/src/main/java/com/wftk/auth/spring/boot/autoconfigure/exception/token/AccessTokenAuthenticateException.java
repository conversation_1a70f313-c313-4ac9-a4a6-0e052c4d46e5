package com.wftk.auth.spring.boot.autoconfigure.exception.token;

/**
 * <AUTHOR>
 * @create 2024/11/21 17:21
 */
public abstract class AccessTokenAuthenticateException extends RuntimeException {

    public AccessTokenAuthenticateException() {
    }

    public AccessTokenAuthenticateException(String message) {
        super(message);
    }

    public AccessTokenAuthenticateException(String message, Throwable cause) {
        super(message, cause);
    }

    public AccessTokenAuthenticateException(Throwable cause) {
        super(cause);
    }

    public AccessTokenAuthenticateException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
