package com.wftk.auth.spring.boot.autoconfigure.exception.auth;

import java.io.Serial;

/**
 * <AUTHOR>
 * @create 2024/11/20 15:36
 */
public class InvalidPreAuthenticationInfoException extends AuthenticateException {

    @Serial
    private static final long serialVersionUID = 1346002279225024329L;

    public InvalidPreAuthenticationInfoException() {
    }

    public InvalidPreAuthenticationInfoException(String message) {
        super(message);
    }

    public InvalidPreAuthenticationInfoException(String message, Throwable cause) {
        super(message, cause);
    }

    public InvalidPreAuthenticationInfoException(Throwable cause) {
        super(cause);
    }

    public InvalidPreAuthenticationInfoException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
