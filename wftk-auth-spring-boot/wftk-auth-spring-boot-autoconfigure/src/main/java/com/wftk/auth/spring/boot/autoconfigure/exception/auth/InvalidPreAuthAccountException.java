package com.wftk.auth.spring.boot.autoconfigure.exception.auth;

import java.io.Serial;

/**
 * <AUTHOR>
 * @create 2024/12/20 17:40
 */
public class InvalidPreAuthAccountException extends AuthenticateException {
    @Serial
    private static final long serialVersionUID = -2638810277656512310L;

    public InvalidPreAuthAccountException() {
    }

    public InvalidPreAuthAccountException(String message) {
        super(message);
    }

    public InvalidPreAuthAccountException(String message, Throwable cause) {
        super(message, cause);
    }

    public InvalidPreAuthAccountException(Throwable cause) {
        super(cause);
    }

    public InvalidPreAuthAccountException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
