package com.wftk.auth.spring.boot.autoconfigure.exception.auth;

/**
 * <AUTHOR>
 * @create 2024/11/20 14:37
 */
public class InvalidClientException extends AuthenticateException {

    public InvalidClientException() {
    }

    public InvalidClientException(String message) {
        super(message);
    }

    public InvalidClientException(String message, Throwable cause) {
        super(message, cause);
    }

    public InvalidClientException(Throwable cause) {
        super(cause);
    }

    public InvalidClientException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
