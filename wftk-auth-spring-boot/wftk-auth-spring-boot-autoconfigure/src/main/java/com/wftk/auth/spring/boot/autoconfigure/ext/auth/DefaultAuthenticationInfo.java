package com.wftk.auth.spring.boot.autoconfigure.ext.auth;

import com.wftk.auth.spring.boot.autoconfigure.core.auth.AuthenticationInfo;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.PreAuthenticationInfo;
import org.springframework.lang.NonNull;

import java.io.Serial;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2024/11/22 14:31
 */
public class DefaultAuthenticationInfo implements AuthenticationInfo {

    @Serial
    private static final long serialVersionUID = 2067984837096635942L;
    private String clientId;
    private String clientSecret;

    /**
     * 授权类型
     */
    private String grantType;

    /**
     * 预授权类型（例如：短信验证码、图形验证码）
     */
    private String preGrantType;

    /**
     * 预授权账号（例如：短信验证码场景下的手机号，图形验证码场景下的请求ID）
     */
    private String preAuthAccount;

    /**
     * 例如：短信验证码场景下的短信验证码，图形验证码场景下的验证码
     */
    private String preAuthCode;

    private String account;

    private String password;


    @Override
    public String getPreGrantType() {
        return preGrantType;
    }

    @NonNull
    @Override
    public String getGrantType() {
        return grantType;
    }

    @NonNull
    @Override
    public String getClientId() {
        return clientId;
    }

    @NonNull
    @Override
    public String getClientSecret() {
        return clientSecret;
    }

    @Override
    public String preAuthAccount() {
        return preAuthAccount;
    }

    @Override
    public String preAuthCode() {
        return preAuthCode;
    }

    @Override
    public String getAccount() {
        return account;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public PreAuthenticationInfo toPreAuthenticationInfo() {
        DefaultPreAuthenticationInfo preAuthenticationInfo = new DefaultPreAuthenticationInfo();
        preAuthenticationInfo.setClientId(clientId);
        preAuthenticationInfo.setClientSecret(clientSecret);
        preAuthenticationInfo.setGrantType(grantType);
        preAuthenticationInfo.setPreAuthAccount(preAuthAccount);
        preAuthenticationInfo.setPreAuthCode(preAuthCode);
        preAuthenticationInfo.setPreGrantType(preGrantType);
        return preAuthenticationInfo;
    }


    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }

    public void setPreGrantType(String preGrantType) {
        this.preGrantType = preGrantType;
    }

    public void setPreAuthAccount(String preAuthAccount) {
        this.preAuthAccount = preAuthAccount;
    }

    public void setPreAuthCode(String preAuthCode) {
        this.preAuthCode = preAuthCode;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Override
    public String toString() {
        return "DefaultAuthenticationInfo{" +
                "clientId='" + clientId + '\'' +
                ", clientSecret='" + clientSecret + '\'' +
                ", grantType='" + grantType + '\'' +
                ", preGrantType='" + preGrantType + '\'' +
                ", preAuthAccount='" + preAuthAccount + '\'' +
                ", preAuthCode='" + preAuthCode + '\'' +
                ", account='" + account + '\'' +
                ", password='" + password + '\'' +
                '}';
    }


    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        DefaultAuthenticationInfo that = (DefaultAuthenticationInfo) o;
        return Objects.equals(clientId, that.clientId) && Objects.equals(clientSecret, that.clientSecret) && Objects.equals(grantType, that.grantType) && Objects.equals(preGrantType, that.preGrantType) && Objects.equals(preAuthAccount, that.preAuthAccount) && Objects.equals(preAuthCode, that.preAuthCode) && Objects.equals(account, that.account) && Objects.equals(password, that.password);
    }

    @Override
    public int hashCode() {
        return Objects.hash(clientId, clientSecret, grantType, preGrantType, preAuthAccount, preAuthCode, account, password);
    }
}
