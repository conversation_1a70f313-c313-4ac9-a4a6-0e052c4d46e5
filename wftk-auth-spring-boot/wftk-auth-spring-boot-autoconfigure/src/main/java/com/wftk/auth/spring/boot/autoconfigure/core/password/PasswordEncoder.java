package com.wftk.auth.spring.boot.autoconfigure.core.password;

/**
 * <AUTHOR>
 * @create 2024/11/20 17:49
 */
public interface PasswordEncoder {

    /**
     * 加密
     * @param rawPassword
     * @return
     */
    String encode(String rawPassword);

    /**
     * 密码校验
     * @param rawPassword
     * @param encodedPassword
     * @return
     */
    boolean matches(String rawPassword, String encodedPassword);

}
