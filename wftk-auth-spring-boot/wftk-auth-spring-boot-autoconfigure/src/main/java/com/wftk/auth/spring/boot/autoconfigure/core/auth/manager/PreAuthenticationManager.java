package com.wftk.auth.spring.boot.autoconfigure.core.auth.manager;

import com.wftk.auth.spring.boot.autoconfigure.core.auth.PreAuthenticationInfo;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.client.ClientInfo;
import com.wftk.auth.spring.boot.autoconfigure.exception.auth.AuthenticateException;

/**
 * <AUTHOR>
 * @create 2024/11/20 10:53
 */
public interface PreAuthenticationManager extends AuthenticationManager {

    /**
     * 针对短信验证码 / 图形验证码等场景，需要提前获取验证码
     * @param preAuthenticationInfo
     */
    void preAuthenticate(PreAuthenticationInfo preAuthenticationInfo) throws AuthenticateException;


    /**
     * 检查预授权信息是否存在
     * @param preAuthenticationInfo
     */
    boolean preAuthenticateExist(PreAuthenticationInfo preAuthenticationInfo);


    /**
     * 针对短信验证码 / 图形验证码等场景, 校验验证码
     * @param preAuthenticationInfo
     * @throws AuthenticateException
     */
    void preAuthenticateValidate(ClientInfo clientInfo, PreAuthenticationInfo preAuthenticationInfo) throws AuthenticateException;
}
