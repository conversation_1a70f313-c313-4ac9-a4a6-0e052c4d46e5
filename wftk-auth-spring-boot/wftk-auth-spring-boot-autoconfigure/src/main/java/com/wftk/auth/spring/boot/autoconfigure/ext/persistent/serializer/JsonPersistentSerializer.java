package com.wftk.auth.spring.boot.autoconfigure.ext.persistent.serializer;

import com.wftk.auth.spring.boot.autoconfigure.core.persistent.serializer.PersistentSerializer;
import com.wftk.jackson.core.JSONObject;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @create 2024/11/20 14:05
 */
public class JsonPersistentSerializer implements PersistentSerializer {

    @Override
    public byte[] serialize(Object object) {
        return JSONObject.getInstance().toJSONString(object).getBytes(StandardCharsets.UTF_8);
    }

    @Override
    public <T> T deserialize(byte[] value, Class<T> clazz) {
        return JSONObject.getInstance().parseObject(new String(value, StandardCharsets.UTF_8), clazz);
    }
}
