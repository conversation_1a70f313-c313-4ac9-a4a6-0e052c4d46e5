package com.wftk.auth.spring.boot.autoconfigure.core.auth;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.client.ClientInfo;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.user.AuthUser;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/11/20 17:59
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, property = "class")
public interface Authentication extends Serializable {

    /**
     * 是否通过认证
     * @return
     */
    boolean isAuthenticated();

    /**
     * 设置认证状态
     * @param isAuthenticated
     */
    void setAuthenticated(boolean isAuthenticated);

    /**
     * 获取认证的client信息
     * @return
     */
    ClientInfo getClientInfo();

    /**
     * 获取认证用户信息
     * @return
     */
    AuthUser<?> getAuthUser();

}
