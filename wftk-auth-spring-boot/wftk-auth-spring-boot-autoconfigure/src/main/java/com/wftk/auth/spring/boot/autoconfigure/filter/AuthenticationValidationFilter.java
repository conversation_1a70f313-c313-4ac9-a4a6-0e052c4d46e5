package com.wftk.auth.spring.boot.autoconfigure.filter;

import cn.hutool.core.util.StrUtil;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.Authentication;
import com.wftk.auth.spring.boot.autoconfigure.exception.auth.InvalidRequestException;
import com.wftk.auth.spring.boot.autoconfigure.properties.AuthProperties;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.common.core.request.matcher.AntIgnoredRequestMatcher;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.HandlerExceptionResolver;

import java.io.IOException;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2024/11/30 13:48
 */
public class AuthenticationValidationFilter extends OncePerRequestFilter {

    private final AntIgnoredRequestMatcher tokenIgnoreRequestMatcher;

    private final HandlerExceptionResolver handlerExceptionResolver;

    /**
     * 校验相关参数
     */
    private final AuthProperties.ValidationProperties validationProperties;


    public AuthenticationValidationFilter(HandlerExceptionResolver handlerExceptionResolver, Set<String> ignorePatterns,
                                          AuthProperties.ValidationProperties validationProperties) {
        this.tokenIgnoreRequestMatcher = new AntIgnoredRequestMatcher(ignorePatterns);
        this.handlerExceptionResolver = handlerExceptionResolver;
        this.validationProperties = validationProperties;
    }


    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        return tokenIgnoreRequestMatcher.ignore(request);
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        Authentication authentication = AuthenticationHolder.getAuthentication();

        // 租户相关校验
        if (validationProperties.getTenant() != null && (validationProperties.getTenant().getEnable() == null || validationProperties.getTenant().getEnable())) {
            String tenantParamName = validationProperties.getTenant().getTenantParamName();
            String tenantId = request.getParameter(tenantParamName);
            if (!StrUtil.equalsIgnoreCase(authentication.getClientInfo().getTenantId(), tenantId)) {
                handlerExceptionResolver.resolveException(request, response, null, new InvalidRequestException("invalid tenant, " + tenantParamName + ": " + tenantId));
                return;
            }
        }

        // 租户下client相关校验
        if (validationProperties.getClient() != null && (validationProperties.getClient().getEnable() == null || validationProperties.getClient().getEnable())) {
            String clientParamName = validationProperties.getClient().getClientParamName();
            String clientId = request.getParameter(clientParamName);
            if (!StrUtil.equalsIgnoreCase(authentication.getClientInfo().getClientId(), clientId)) {
                handlerExceptionResolver.resolveException(request, response, null, new InvalidRequestException("invalid client, " + clientParamName + ": " + clientId));
                return;
            }
        }

        if (validationProperties.getAccount() != null && (validationProperties.getAccount().getEnable() == null || validationProperties.getAccount().getEnable())) {
            String accountParamName = validationProperties.getAccount().getAccountParamName();
            String account = request.getParameter(accountParamName);
            if (!StrUtil.equalsIgnoreCase(authentication.getAuthUser().getAccount(), account)) {
                handlerExceptionResolver.resolveException(request, response, null, new InvalidRequestException("invalid account, " + accountParamName + ": " + account));
                return;
            }
        }

        doFilter(request, response, filterChain);
    }
}
