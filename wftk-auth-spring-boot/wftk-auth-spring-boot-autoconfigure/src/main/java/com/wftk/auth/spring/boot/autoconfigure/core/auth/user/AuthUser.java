package com.wftk.auth.spring.boot.autoconfigure.core.auth.user;

import com.fasterxml.jackson.annotation.JsonTypeInfo;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/11/20 14:17
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, property = "class")
public abstract class AuthUser<U> implements Serializable {

    @Serial
    private static final long serialVersionUID = 9061649512872577474L;

    @JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, property = "class")
    private final U user;

    public AuthUser(U user) {
        this.user = user;
    }

    /**
     * 获取租户ID
     * @return
     */
    public abstract String getTenantId();

    /**
     * 获取用户ID
     * @return
     */
    public abstract Long getId();

    /**
     * 获取账号
     * @return
     */
    public abstract String getAccount();

    /**
     * 获取密码
     * @return
     */
    public abstract String getPassword();

    /**
     * 是否被禁用
     * @return
     */
    public abstract boolean isDisabled();

    /**
     * 获取用户
     * @return
     */
    public U getUser() {
        return user;
    }
}
