package com.wftk.auth.spring.boot.autoconfigure.ext.persistent.serializer;

import cn.hutool.core.util.ObjectUtil;
import com.wftk.auth.spring.boot.autoconfigure.core.persistent.serializer.PersistentSerializer;

/**
 * <AUTHOR>
 * @create 2024/11/25 17:10
 */
public class DefaultPersistentSerializer implements PersistentSerializer {
    @Override
    public byte[] serialize(Object object) {
        return ObjectUtil.serialize(object);
    }

    @Override
    public <T> T deserialize(byte[] value, Class<T> clazz) {
        return ObjectUtil.deserialize(value, clazz);
    }
}
