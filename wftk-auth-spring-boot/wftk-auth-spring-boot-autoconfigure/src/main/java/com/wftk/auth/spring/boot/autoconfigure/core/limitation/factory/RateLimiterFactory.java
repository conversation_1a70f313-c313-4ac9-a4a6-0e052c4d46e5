package com.wftk.auth.spring.boot.autoconfigure.core.limitation.factory;

import java.time.Duration;

import com.wftk.auth.spring.boot.autoconfigure.core.limitation.RateLimiter;

/**
 * 限流器工厂
 */
public interface RateLimiterFactory {

    /**
     * 创建限流器
     * @param name 限流器名称
     * @param permits 每秒许可数
     * @param duration 限流时间
     * @return
     */
    RateLimiter getRateLimiter(String name, double permits, Duration duration);

}
