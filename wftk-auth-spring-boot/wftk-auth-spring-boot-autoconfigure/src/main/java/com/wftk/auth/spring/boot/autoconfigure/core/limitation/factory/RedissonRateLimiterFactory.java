package com.wftk.auth.spring.boot.autoconfigure.core.limitation.factory;

import java.time.Duration;

import org.redisson.api.RateType;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RedissonClient;

import com.wftk.auth.spring.boot.autoconfigure.core.limitation.RateLimiter;
import com.wftk.auth.spring.boot.autoconfigure.core.limitation.RedissonRateLimiter;

public class RedissonRateLimiterFactory implements RateLimiterFactory {

    private final RedissonClient redissonClient;

    public RedissonRateLimiterFactory(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    @Override
    public RateLimiter getRateLimiter(String name, double permits, Duration duration) {
        RRateLimiter rateLimiter = redissonClient.getRateLimiter(name);
        if (rateLimiter.isExists()) {
            return new RedissonRateLimiter(rateLimiter);
        }
        if ((long) permits != permits) {
            throw new IllegalArgumentException("permits must be long.");
        }
        rateLimiter.trySetRate(RateType.OVERALL, (long) permits, duration);
        return new RedissonRateLimiter(rateLimiter);
    }

}