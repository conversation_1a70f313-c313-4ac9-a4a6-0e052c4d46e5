package com.wftk.auth.spring.boot.autoconfigure.exception.translator;

import com.wftk.auth.spring.boot.autoconfigure.exception.auth.AuthenticateException;
import com.wftk.exception.common.GlobalErrorConstants;
import com.wftk.exception.common.HttpStatusCode;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.translator.base.AbstractExceptionTranslator;

/**
 * <AUTHOR>
 * @create 2024/11/21 18:22
 */
public class AuthenticateExceptionTranslator extends AbstractExceptionTranslator {
    @Override
    protected ResolvedResult doTranslate(Throwable throwable) {
        return ResolvedResultBuilder.build(GlobalErrorConstants.BAD_REQUEST.code(), "用户认证失败", HttpStatusCode.BAD_REQUEST);
    }

    @Override
    public boolean support(Throwable throwable) {
        return throwable instanceof AuthenticateException;
    }
}
