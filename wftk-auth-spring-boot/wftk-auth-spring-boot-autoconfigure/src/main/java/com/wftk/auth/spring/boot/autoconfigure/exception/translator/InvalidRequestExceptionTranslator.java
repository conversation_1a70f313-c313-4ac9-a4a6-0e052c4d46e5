package com.wftk.auth.spring.boot.autoconfigure.exception.translator;

import com.wftk.auth.spring.boot.autoconfigure.exception.auth.InvalidRequestException;
import com.wftk.exception.common.GlobalErrorConstants;
import com.wftk.exception.common.HttpStatusCode;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.translator.base.AbstractExceptionTranslator;

/**
 * <AUTHOR>
 * @create 2024/11/30 15:25
 */
public class InvalidRequestExceptionTranslator extends AbstractExceptionTranslator {
    @Override
    protected ResolvedResult doTranslate(Throwable throwable) {
        return ResolvedResultBuilder.build(GlobalErrorConstants.FORBIDDEN.code(), "非法请求", HttpStatusCode.FORBIDDEN);
    }

    @Override
    public boolean support(Throwable throwable) {
        return throwable instanceof InvalidRequestException;
    }
}
