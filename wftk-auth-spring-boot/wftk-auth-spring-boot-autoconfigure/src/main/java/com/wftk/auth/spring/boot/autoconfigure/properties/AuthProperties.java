package com.wftk.auth.spring.boot.autoconfigure.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Set;

/**
 * <AUTHOR>
 * @create 2024/11/21 11:02
 */
@ConfigurationProperties(prefix = "config.auth")
public class AuthProperties {


    /**
     * client通用配置
     */
    private ClientProperties client = new ClientProperties();

    /**
     * 认证filter配置
     */
    private FilterProperties filter = new FilterProperties();


    /**
     * 租户 / 账号校验
     */
    private ValidationProperties validation = new ValidationProperties();


    public ClientProperties getClient() {
        return client;
    }

    public void setClient(ClientProperties client) {
        this.client = client;
    }

    public FilterProperties getFilter() {
        return filter;
    }

    public void setFilter(FilterProperties filter) {
        this.filter = filter;
    }

    public ValidationProperties getValidation() {
        return validation;
    }

    public void setValidation(ValidationProperties validation) {
        this.validation = validation;
    }

    /**
     * 通用配置信息
     */
    public static class ClientProperties {

        /**
         * 预授权类型
         * @return
         */
        private Set<String> preGrantType;


        /**
         * 授权类型
         * @return
         */
        private Set<String> grantType;


        /**
         * 预认证信息留存时间
         */
        private Long preAuthExpireInSeconds = 300L;

        /**
         * 预认证间隔时间（例如60S内只能发送1次短信）
         */
        private Long preAuthDurationInSeconds = 60L;

        /**
         * 令牌过期时间
         */
        private Long accessTokenExpireInSeconds = 7200L;

        /**
         * 当accessToken尚未过期，又重新生成新accessToken后，老accessToken过渡时间（过渡时间内仍可使用）
         */
        private Long accessTokenTransitionInSeconds = 0L;

        /**
         * 最大并发会话数
         */
        private Integer maxConcurrentSessions = 1;


        /**
         * accessToken强制重置时间(即此时间后强制清空用户所有accessToken)
         */
        private Long accessTokenForceResetInSeconds = 3600L * 24 * 90;


        public Set<String> getPreGrantType() {
            return preGrantType;
        }

        public void setPreGrantType(Set<String> preGrantType) {
            this.preGrantType = preGrantType;
        }

        public Set<String> getGrantType() {
            return grantType;
        }

        public void setGrantType(Set<String> grantType) {
            this.grantType = grantType;
        }

        public Long getPreAuthExpireInSeconds() {
            return preAuthExpireInSeconds;
        }

        public void setPreAuthExpireInSeconds(Long preAuthExpireInSeconds) {
            this.preAuthExpireInSeconds = preAuthExpireInSeconds;
        }

        public Long getPreAuthDurationInSeconds() {
            return preAuthDurationInSeconds;
        }

        public void setPreAuthDurationInSeconds(Long preAuthDurationInSeconds) {
            this.preAuthDurationInSeconds = preAuthDurationInSeconds;
        }

        public Long getAccessTokenExpireInSeconds() {
            return accessTokenExpireInSeconds;
        }

        public void setAccessTokenExpireInSeconds(Long accessTokenExpireInSeconds) {
            this.accessTokenExpireInSeconds = accessTokenExpireInSeconds;
        }

        public Long getAccessTokenTransitionInSeconds() {
            return accessTokenTransitionInSeconds;
        }

        public void setAccessTokenTransitionInSeconds(Long accessTokenTransitionInSeconds) {
            this.accessTokenTransitionInSeconds = accessTokenTransitionInSeconds;
        }

        public Integer getMaxConcurrentSessions() {
            return maxConcurrentSessions;
        }

        public void setMaxConcurrentSessions(Integer maxConcurrentSessions) {
            this.maxConcurrentSessions = maxConcurrentSessions;
        }

        public Long getAccessTokenForceResetInSeconds() {
            return accessTokenForceResetInSeconds;
        }

        public void setAccessTokenForceResetInSeconds(Long accessTokenForceResetInSeconds) {
            this.accessTokenForceResetInSeconds = accessTokenForceResetInSeconds;
        }
    }


    /**
     * filter配置
     */
    public static class FilterProperties {

        /**
         * 是否开启认证filter
         */
        private boolean enable = true;

        /**
         * filter顺序
         */
        private Integer order = Integer.MAX_VALUE - 10000;

        /**
         * filter名
         */
        private String name = "authenticationFilter";


        /**
         * filter拦截的patterns
         */
        private Set<String> patterns = Set.of("/*");

        /**
         * 忽略认证的url
         */
        private Set<String> ignorePatterns = Set.of();


        public boolean isEnable() {
            return enable;
        }

        public void setEnable(boolean enable) {
            this.enable = enable;
        }

        public Set<String> getIgnorePatterns() {
            return ignorePatterns;
        }

        public void setIgnorePatterns(Set<String> ignorePatterns) {
            this.ignorePatterns = ignorePatterns;
        }

        public Integer getOrder() {
            return order;
        }

        public void setOrder(Integer order) {
            this.order = order;
        }

        public Set<String> getPatterns() {
            return patterns;
        }

        public void setPatterns(Set<String> patterns) {
            this.patterns = patterns;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }


    /**
     * 租户/账号校验配置
     */
    public static class ValidationProperties {

        private TenantProperties tenant = new TenantProperties();
        private ClientProperties client = new ClientProperties();
        private AccountProperties account = new AccountProperties();

        public TenantProperties getTenant() {
            return tenant;
        }

        public void setTenant(TenantProperties tenant) {
            this.tenant = tenant;
        }

        public ClientProperties getClient() {
            return client;
        }

        public void setClient(ClientProperties client) {
            this.client = client;
        }

        public AccountProperties getAccount() {
            return account;
        }

        public void setAccount(AccountProperties account) {
            this.account = account;
        }







        /**
         * 租户校验相关
         */
        public static class TenantProperties {
            private String tenantParamName = "tenantId";
            private Boolean enable = true;

            public String getTenantParamName() {
                return tenantParamName;
            }

            public void setTenantParamName(String tenantParamName) {
                this.tenantParamName = tenantParamName;
            }

            public Boolean getEnable() {
                return enable;
            }

            public void setEnable(Boolean enable) {
                this.enable = enable;
            }
        }


        /**
         * 租户下client相关校验
         */
        public static class ClientProperties {
            private String clientParamName = "clientId";
            private Boolean enable = true;

            public String getClientParamName() {
                return clientParamName;
            }

            public void setClientParamName(String clientParamName) {
                this.clientParamName = clientParamName;
            }

            public Boolean getEnable() {
                return enable;
            }

            public void setEnable(Boolean enable) {
                this.enable = enable;
            }
        }


        /**
         * 账号相关校验
         */
        public static class AccountProperties {
            private String accountParamName = "account";
            private Boolean enable = true;

            public String getAccountParamName() {
                return accountParamName;
            }

            public void setAccountParamName(String accountParamName) {
                this.accountParamName = accountParamName;
            }

            public Boolean getEnable() {
                return enable;
            }

            public void setEnable(Boolean enable) {
                this.enable = enable;
            }
        }
    }

}
