package com.wftk.auth.spring.boot.autoconfigure.core.loader;

import com.wftk.auth.spring.boot.autoconfigure.core.auth.AuthenticationInfo;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.user.AuthUser;

/**
 * <AUTHOR>
 * @create 2024/11/20 14:25
 */
public interface UserLoader<U> {

    /**
     * 根据认证信息获取用户信息
     * @param authenticationInfo
     * @return
     */
    default AuthUser<U> load(AuthenticationInfo authenticationInfo) {
        return load(authenticationInfo.getAccount());
    }

    /**
     * 根据账号获取用户信息
     * @param account
     * @return
     */
    AuthUser<U> load(String account);
}
