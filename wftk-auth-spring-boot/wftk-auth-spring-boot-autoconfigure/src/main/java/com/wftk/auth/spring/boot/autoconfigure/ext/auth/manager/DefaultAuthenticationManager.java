package com.wftk.auth.spring.boot.autoconfigure.ext.auth.manager;

import com.wftk.auth.spring.boot.autoconfigure.core.auth.manager.BaseAuthenticationManager;
import com.wftk.auth.spring.boot.autoconfigure.core.limitation.factory.RateLimiterFactory;
import com.wftk.auth.spring.boot.autoconfigure.core.loader.ClientLoader;
import com.wftk.auth.spring.boot.autoconfigure.core.loader.UserLoader;
import com.wftk.auth.spring.boot.autoconfigure.core.password.PasswordEncoder;
import com.wftk.auth.spring.boot.autoconfigure.core.persistent.ObjectPersistent;
import com.wftk.auth.spring.boot.autoconfigure.core.validator.PreAuthAccountValidator;
import com.wftk.auth.spring.boot.autoconfigure.properties.AuthProperties;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @create 2024/11/20 17:52
 */
public class DefaultAuthenticationManager extends BaseAuthenticationManager {

    private final PasswordEncoder passwordEncoder;

    public DefaultAuthenticationManager(ClientLoader clientLoader, UserLoader<?> userLoader, ObjectPersistent objectPersistent,
                                        AuthProperties authProperties, PasswordEncoder passwordEncoder) {
        super(clientLoader, userLoader, objectPersistent, authProperties, null);
        this.passwordEncoder = passwordEncoder;
    }

    public DefaultAuthenticationManager(ClientLoader clientLoader, UserLoader<?> userLoader, ObjectPersistent objectPersistent,
                                        AuthProperties authProperties, PreAuthAccountValidator preAuthAccountValidator, PasswordEncoder passwordEncoder,
                                        RateLimiterFactory rateLimiterFactory) {
        super(clientLoader, userLoader, objectPersistent, authProperties, preAuthAccountValidator, rateLimiterFactory);
        this.passwordEncoder = passwordEncoder;
    }


    @Override
    protected boolean passwordValidate(String rawPassword, String encodedPassword) {
        if (StrUtil.isBlank(rawPassword)) {
            return true;
        }
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }
}
