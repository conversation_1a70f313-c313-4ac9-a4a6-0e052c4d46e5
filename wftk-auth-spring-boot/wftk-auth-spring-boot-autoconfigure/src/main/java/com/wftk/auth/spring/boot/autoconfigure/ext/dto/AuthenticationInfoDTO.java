package com.wftk.auth.spring.boot.autoconfigure.ext.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wftk.auth.spring.boot.autoconfigure.ext.auth.DefaultAuthenticationInfo;
import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @create 2024/11/26 11:40
 */
public class AuthenticationInfoDTO {

    @JsonProperty("client_id")
    @NotBlank
    private String clientId;

    @JsonProperty("client_secret")
    @NotBlank
    private String clientSecret;

    /**
     * 授权类型
     */
    @JsonProperty("grant_type")
    @NotBlank
    private String grantType;

    /**
     * 预授权类型（例如：短信验证码、图形验证码）
     */
    @JsonProperty("pre_grant_type")
    private String preGrantType;

    /**
     * 预授权账号（例如：短信验证码场景下的手机号，图形验证码场景下的请求ID）
     */
    @JsonProperty("pre_auth_account")
    private String preAuthAccount;

    /**
     * 例如：短信验证码场景下的短信验证码，图形验证码场景下的验证码
     */
    @JsonProperty("pre_auth_code")
    private String preAuthCode;

    @NotBlank
    private String account;

    private String password;

    /**
     * 转entity
     * @return
     */
    public DefaultAuthenticationInfo toEntity() {
        DefaultAuthenticationInfo authenticationInfo = new DefaultAuthenticationInfo();
        authenticationInfo.setClientId(clientId);
        authenticationInfo.setClientSecret(clientSecret);
        authenticationInfo.setGrantType(grantType);
        authenticationInfo.setPreGrantType(preGrantType);
        authenticationInfo.setPreAuthAccount(preAuthAccount);
        authenticationInfo.setPreAuthCode(preAuthCode);
        authenticationInfo.setAccount(account);
        authenticationInfo.setPassword(password);
        return authenticationInfo;
    }




    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getGrantType() {
        return grantType;
    }

    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }

    public String getPreGrantType() {
        return preGrantType;
    }

    public void setPreGrantType(String preGrantType) {
        this.preGrantType = preGrantType;
    }

    public String getPreAuthAccount() {
        return preAuthAccount;
    }

    public void setPreAuthAccount(String preAuthAccount) {
        this.preAuthAccount = preAuthAccount;
    }

    public String getPreAuthCode() {
        return preAuthCode;
    }

    public void setPreAuthCode(String preAuthCode) {
        this.preAuthCode = preAuthCode;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Override
    public String toString() {
        return "AuthenticationInfoDTO{" +
                "clientId='" + clientId + '\'' +
                ", clientSecret='" + clientSecret + '\'' +
                ", grantType='" + grantType + '\'' +
                ", preGrantType='" + preGrantType + '\'' +
                ", preAuthAccount='" + preAuthAccount + '\'' +
                ", preAuthCode='" + preAuthCode + '\'' +
                ", account='" + account + '\'' +
                ", password='" + password + '\'' +
                '}';
    }
}
