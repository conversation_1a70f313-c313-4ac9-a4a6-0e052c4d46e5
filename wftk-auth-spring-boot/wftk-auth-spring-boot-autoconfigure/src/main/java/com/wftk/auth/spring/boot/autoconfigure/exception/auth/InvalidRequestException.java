package com.wftk.auth.spring.boot.autoconfigure.exception.auth;

import java.io.Serial;

/**
 * <AUTHOR>
 * @create 2024/11/30 15:12
 */
public class InvalidRequestException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = -280112993390387903L;

    public InvalidRequestException() {
    }

    public InvalidRequestException(String message) {
        super(message);
    }

    public InvalidRequestException(String message, Throwable cause) {
        super(message, cause);
    }

    public InvalidRequestException(Throwable cause) {
        super(cause);
    }

    public InvalidRequestException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
