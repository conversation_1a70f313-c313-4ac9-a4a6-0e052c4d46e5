package com.wftk.auth.spring.boot.autoconfigure.core.auth.manager;

import com.wftk.auth.spring.boot.autoconfigure.core.auth.Authentication;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.AuthenticationInfo;
import com.wftk.auth.spring.boot.autoconfigure.exception.auth.AuthenticateException;

/**
 * <AUTHOR>
 * @create 2024/11/20 10:33
 */
public interface AuthenticationManager {

    /**
     * 认证
     * @param authenticationInfo
     * @return
     * @throws AuthenticateException
     */
    Authentication authenticate(AuthenticationInfo authenticationInfo) throws AuthenticateException;

}
