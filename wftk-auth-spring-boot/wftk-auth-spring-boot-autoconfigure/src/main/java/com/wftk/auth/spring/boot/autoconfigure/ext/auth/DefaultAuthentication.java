package com.wftk.auth.spring.boot.autoconfigure.ext.auth;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.Authentication;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.client.ClientInfo;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.user.AuthUser;

import java.io.Serial;

/**
 * <AUTHOR>
 * @create 2024/11/20 18:10
 */
public class DefaultAuthentication implements Authentication {

    @Serial
    private static final long serialVersionUID = 872512451987199125L;
    private final ClientInfo clientInfo;
    private final AuthUser<?> authUser;
    private boolean isAuthenticated;


    @JsonCreator
    public DefaultAuthentication(@JsonProperty("clientInfo") ClientInfo clientInfo, @JsonProperty("authUser") AuthUser<?> authUser,
                                 @JsonProperty("isAuthenticated") boolean isAuthenticated) {
        this.clientInfo = clientInfo;
        this.authUser = authUser;
        this.isAuthenticated = isAuthenticated;
    }

    @Override
    public boolean isAuthenticated() {
        return isAuthenticated;
    }

    @Override
    public void setAuthenticated(boolean isAuthenticated) {
        this.isAuthenticated = isAuthenticated;
    }

    @Override
    public ClientInfo getClientInfo() {
        return clientInfo;
    }

    @Override
    public AuthUser<?> getAuthUser() {
        return authUser;
    }
}
