package com.wftk.auth.spring.boot.autoconfigure.core.limitation;

import org.redisson.api.RRateLimiter;

public class RedissonRateLimiter implements RateLimiter {

    private final RRateLimiter rateLimiter;

    public RedissonRateLimiter(RRateLimiter rateLimiter) {
        this.rateLimiter = rateLimiter;
    }

    @Override
    public void close() {
        rateLimiter.delete();
    }

    @Override
    public boolean tryAcquire() {
        return rateLimiter.tryAcquire();
    }

    @Override
    public boolean tryAcquire(int permits) {
        return rateLimiter.tryAcquire(permits);
    }

}
