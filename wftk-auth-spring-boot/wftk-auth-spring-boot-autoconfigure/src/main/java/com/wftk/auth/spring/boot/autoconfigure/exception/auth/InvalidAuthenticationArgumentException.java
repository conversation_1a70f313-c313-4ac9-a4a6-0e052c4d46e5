package com.wftk.auth.spring.boot.autoconfigure.exception.auth;

/**
 * <AUTHOR>
 * @create 2024/11/22 14:36
 */
public class InvalidAuthenticationArgumentException extends AuthenticateException {

    public InvalidAuthenticationArgumentException() {
    }

    public InvalidAuthenticationArgumentException(String message) {
        super(message);
    }

    public InvalidAuthenticationArgumentException(String message, Throwable cause) {
        super(message, cause);
    }

    public InvalidAuthenticationArgumentException(Throwable cause) {
        super(cause);
    }

    public InvalidAuthenticationArgumentException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
