package com.wftk.auth.spring.boot.autoconfigure.ext.auth.client;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.client.ClientInfo;
import org.springframework.lang.NonNull;

import java.io.Serial;

/**
 * <AUTHOR>
 * @create 2024/11/21 14:24
 */
public class DefaultClientInfo implements ClientInfo {

    @Serial
    private static final long serialVersionUID = 2314651401184921675L;
    private final String tenantId;
    private final String clientId;
    private final String clientSecret;
    private final ClientConfig clientConfig;

    @JsonCreator
    public DefaultClientInfo(@JsonProperty("tenantId") String tenantId, @JsonProperty("clientId") String clientId,
                             @JsonProperty("clientSecret") String clientSecret,
                             @JsonProperty("clientConfig") ClientConfig clientConfig) {
        this.tenantId = tenantId;
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.clientConfig = clientConfig == null ? new DefaultClientConfig() : clientConfig;
    }

    @Override
    public String getTenantId() {
        return tenantId;
    }

    @NonNull
    @Override
    public String getClientId() {
        return clientId;
    }

    @Override
    public String getClientSecret() {
        return clientSecret;
    }

    @NonNull
    @Override
    public ClientConfig getClientConfig() {
        return clientConfig;
    }
}
