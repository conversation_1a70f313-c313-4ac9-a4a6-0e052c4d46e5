package com.wftk.auth.spring.boot.autoconfigure.exception.auth;

/**
 * <AUTHOR>
 * @create 2024/11/22 14:43
 */
public class InvalidPasswordException extends AuthenticateException {

    public InvalidPasswordException() {
    }

    public InvalidPasswordException(String message) {
        super(message);
    }

    public InvalidPasswordException(String message, Throwable cause) {
        super(message, cause);
    }

    public InvalidPasswordException(Throwable cause) {
        super(cause);
    }

    public InvalidPasswordException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
