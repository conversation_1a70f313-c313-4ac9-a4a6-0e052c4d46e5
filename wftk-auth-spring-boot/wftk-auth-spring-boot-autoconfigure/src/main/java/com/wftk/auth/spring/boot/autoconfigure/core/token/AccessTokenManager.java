package com.wftk.auth.spring.boot.autoconfigure.core.token;

import com.wftk.auth.spring.boot.autoconfigure.core.auth.Authentication;

/**
 * <AUTHOR>
 * @create 2024/11/20 17:58
 */
public interface AccessTokenManager {

    /**
     * 创建令牌
     * @param authentication
     * @return
     */
    AccessTokenInfo grant(Authentication authentication);



    /**
     * 根据令牌移除
     * @param accessToken
     */
    void remove(String accessToken);

    /**
     * 根据用户账号移除
     * @param account
     */
    void removeWithAccount(String account);


    /**
     * 根据令牌获取认证信息
     * @param accessToken
     * @return
     */
    Authentication extract(String accessToken);

}
