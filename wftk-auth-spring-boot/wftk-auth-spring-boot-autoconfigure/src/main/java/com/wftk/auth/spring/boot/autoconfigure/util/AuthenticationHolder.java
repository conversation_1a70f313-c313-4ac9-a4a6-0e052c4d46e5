package com.wftk.auth.spring.boot.autoconfigure.util;

import com.wftk.auth.spring.boot.autoconfigure.core.auth.Authentication;

/**
 * <AUTHOR>
 * @create 2024/11/21 18:18
 */
public class AuthenticationHolder {

    private static final ThreadLocal<Authentication> context = new ThreadLocal<>();

    public static void setAuthentication(Authentication authentication) {
        context.set(authentication);
    }

    public static Authentication getAuthentication() {
        return context.get();
    }

    public static void clear() {
        context.remove();
    }

}
