package com.wftk.auth.spring.boot.autoconfigure;

import cn.hutool.core.util.StrUtil;
import com.wftk.auth.spring.boot.autoconfigure.core.persistent.ObjectPersistent;
import com.wftk.auth.spring.boot.autoconfigure.core.persistent.serializer.PersistentSerializer;
import com.wftk.auth.spring.boot.autoconfigure.ext.persistent.RedissonObjectPersistent;
import com.wftk.auth.spring.boot.autoconfigure.ext.persistent.serializer.JsonPersistentSerializer;
import com.wftk.cache.spring.boot.autoconfigure.RedissonObjectStoreAutoConfiguration;
import com.wftk.cache.spring.boot.autoconfigure.store.redisson.ObjectRedissonObjectStore;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

/**
 * <AUTHOR>
 * @create 2024/11/22 14:08
 */
@ConditionalOnClass(ObjectRedissonObjectStore.class)
@AutoConfigureAfter(RedissonObjectStoreAutoConfiguration.class)
@Configuration
public class AuthPersistentAutoConfiguration {

    @ConditionalOnMissingBean
    @Bean
    PersistentSerializer persistentSerializer() {
        return new JsonPersistentSerializer();
    }


    @ConditionalOnBean(ObjectRedissonObjectStore.class)
    @ConditionalOnMissingBean
    @Bean
    ObjectPersistent objectPersistent(ObjectRedissonObjectStore objectRedissonObjectStore, PersistentSerializer persistentSerializer, Environment environment) {
        String applicationName = environment.getProperty("spring.application.name");
        if (StrUtil.isBlank(applicationName)) {
            return new RedissonObjectPersistent(objectRedissonObjectStore, persistentSerializer);
        }
        return new RedissonObjectPersistent(objectRedissonObjectStore, persistentSerializer, applicationName);
    }
}
