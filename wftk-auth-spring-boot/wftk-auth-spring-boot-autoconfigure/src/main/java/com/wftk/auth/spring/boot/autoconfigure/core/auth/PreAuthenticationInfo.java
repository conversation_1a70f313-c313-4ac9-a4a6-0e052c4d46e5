package com.wftk.auth.spring.boot.autoconfigure.core.auth;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/11/20 11:05
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, property = "class")
public interface PreAuthenticationInfo extends Serializable {

    /**
     * 预授权方式
     * @return
     */
    @NonNull
    String getPreGrantType();

    /**
     * 授权 / 登录方式
     * @return
     */
    @NonNull
    String getGrantType();

    @NonNull
    String getClientId();

    @Nullable
    String getClientSecret();

    /**
     * 短信验证码场景时为手机号 / 图形验证码场景时为requestId / 其它场景根据实际情况决定
     * @return
     */
    @NonNull
    String getPreAuthAccount();

    /**
     * 短信验证码 / 图形验证码 / 其它场景根据实际情况决定
     * @return
     */
    @NonNull
    String getPreAuthCode();
}
