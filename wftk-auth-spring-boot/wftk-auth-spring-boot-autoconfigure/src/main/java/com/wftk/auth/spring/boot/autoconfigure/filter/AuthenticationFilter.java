package com.wftk.auth.spring.boot.autoconfigure.filter;

import cn.hutool.core.util.StrUtil;
import com.wftk.auth.spring.boot.autoconfigure.core.auth.Authentication;
import com.wftk.auth.spring.boot.autoconfigure.exception.token.InvalidAccessTokenException;
import com.wftk.auth.spring.boot.autoconfigure.core.token.AccessTokenManager;
import com.wftk.auth.spring.boot.autoconfigure.util.AuthenticationHolder;
import com.wftk.common.core.request.matcher.AntIgnoredRequestMatcher;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.HandlerExceptionResolver;

import java.io.IOException;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2024/11/21 17:04
 */
public class AuthenticationFilter extends OncePerRequestFilter {

    private final AntIgnoredRequestMatcher tokenIgnoreRequestMatcher;

    private final HandlerExceptionResolver handlerExceptionResolver;

    private final AccessTokenManager accessTokenManager;

    public AuthenticationFilter(HandlerExceptionResolver handlerExceptionResolver, AccessTokenManager accessTokenManager) {
        this(handlerExceptionResolver, accessTokenManager, Set.of());
    }


    public AuthenticationFilter(HandlerExceptionResolver handlerExceptionResolver, AccessTokenManager accessTokenManager, Set<String> ignorePatterns) {
        this.tokenIgnoreRequestMatcher = new AntIgnoredRequestMatcher(ignorePatterns);
        this.handlerExceptionResolver = handlerExceptionResolver;
        this.accessTokenManager = accessTokenManager;
    }


    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        return tokenIgnoreRequestMatcher.ignore(request);
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String token = request.getHeader("Authorization");
        token = StrUtil.isBlank(token) ? request.getParameter("Authorization") : token;
        if (StrUtil.isBlank(token)) {
            handlerExceptionResolver.resolveException(request,response,null,new InvalidAccessTokenException("path: " + request.getRequestURI()));
            return;
        }
        token = token.replace("bearer ", "").trim();
        try {
            Authentication authentication = accessTokenManager.extract(token);
            AuthenticationHolder.setAuthentication(authentication);
            doFilter(request, response, filterChain);
        } catch (InvalidAccessTokenException e) {
            handlerExceptionResolver.resolveException(request, response, null, e);
            return;
        } finally {
            AuthenticationHolder.clear();
        }
    }
}

