package com.wftk.auth.spring.boot.autoconfigure.core.auth;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/11/20 10:28
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, property = "class")
public interface AuthenticationInfo extends Serializable {

    /**
     * 预授权方式
     * @return
     */
    String getPreGrantType();

    /**
     * 授权 / 登录方式
     * @return
     */
    @NonNull
    String getGrantType();

    @NonNull
    String getClientId();

    @NonNull
    String getClientSecret();

    /**
     * 短信验证码场景时为手机号 / 图形验证码场景时为requestId / 其它场景根据实际情况决定
     * @return
     */
    @Nullable
    String preAuthAccount();

    /**
     * 短信验证码 / 图形验证码 / 其它场景根据实际情况决定
     * @return
     */
    @Nullable
    String preAuthCode();


    /**
     * 账号
     * @return
     */
    String getAccount();

    /**
     * 密码登录
     * @return
     */
    @Nullable
    String getPassword();


    /**
     * 转换为预授权信息
     * @return
     */
    PreAuthenticationInfo toPreAuthenticationInfo();

}
