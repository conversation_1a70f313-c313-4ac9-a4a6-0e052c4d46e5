package com.wftk.stateless.fsm.context;

/**
 * <AUTHOR>
 * @create 2022/10/24 09:52
 */
public interface ExecutionContext<S, E> {

    /**
     * 设置当前原始状态
     * @param currentRawState
     */
    void setCurrentRawState(S currentRawState);

    /**
     * 设置转换后原始状态
     * @param targetRawState
     */
    void setTargetRawState(S targetRawState);


    /**
     * 设置是否转换成功
     * @param isSuccess
     * @return
     */
    boolean setSuccess(boolean isSuccess);
}
