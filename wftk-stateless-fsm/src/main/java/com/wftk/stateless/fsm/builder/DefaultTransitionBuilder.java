package com.wftk.stateless.fsm.builder;


import com.wftk.stateless.fsm.action.Action;
import com.wftk.stateless.fsm.condition.Condition;
import com.wftk.stateless.fsm.machine.StateMachine;
import com.wftk.stateless.fsm.registry.StateRegistry;
import com.wftk.stateless.fsm.state.DefaultState;
import com.wftk.stateless.fsm.state.State;
import com.wftk.stateless.fsm.transition.ExternalTransition;
import com.wftk.stateless.fsm.transition.InternalTransition;
import com.wftk.stateless.fsm.transition.Transition;

/**
 * <AUTHOR>
 * @create 2022/10/20 18:17
 */
public class DefaultTransitionBuilder<S, E> implements InternalTransitionBuilder<S, E>, ExternalTransitionBuilder<S, E>, From<S, E>, To<S, E>, On<S, E>, When<S, E>, And<S, E> {

    private final TransitionsBuilder<S, E> transitionsBuilder;
    private final StateRegistry<S, E, State<S, E>> stateRegistry;

    private State<S, E> sourceState;
    private State<S, E> targetState;

    private Transition<S, E> transition;

    /**
     * 是否自旋状态
     */
    private boolean spin = false;

    public DefaultTransitionBuilder(TransitionsBuilder<S, E> transitionsBuilder, StateMachine<S, E> stateMachine) {
        this.transitionsBuilder = transitionsBuilder;
        this.stateRegistry = stateMachine.getStateRegistry();
    }

    @Override
    public From<S, E> from(S rawState) {
        sourceState = getStateWithRaw(rawState);
        return this;
    }

    @Override
    public To<S, E> to(S rawState) {
        targetState = getStateWithRaw(rawState);
        return this;
    }

    @Override
    public To<S, E> onEntry(Action<S, E> entryAction) {
        sourceState.addEntryAction(entryAction);
        return this;
    }

    @Override
    public To<S, E> onExit(Action<S, E> exitAction) {
        sourceState.addExitAction(exitAction);
        return this;
    }

    @Override
    public To<S, E> spin(S rawState) {
        targetState = sourceState = getStateWithRaw(rawState);
        spin = true;
        return this;
    }

    @Override
    public When<S, E> when(Condition<S, E> condition) {
        transition.setCondition(condition);
        return this;
    }

    @Override
    public On<S, E> onEvent(E event) {
        if (!spin) {
            transition = new ExternalTransition<>(sourceState, targetState, event);
        } else {
            transition = new InternalTransition<>(sourceState, event);
        }
        sourceState.addTransition(transition);
        return this;
    }

    @Override
    public And<S, E> doAction(Action<S, E> action) {
        transition.addAction(action);
        return this;
    }

    @Override
    public TransitionsBuilder<S, E> and() {
        return transitionsBuilder;
    }

    /**
     * 根据原始状态获取状态
     * @param rawState
     * @return
     */
    private State<S, E> getStateWithRaw(S rawState) {
        return stateRegistry.get(rawState)
                .orElseGet(() -> {
                    State<S, E> state = new DefaultState<>(rawState);
                    stateRegistry.register(rawState, state);
                    return state;
                });
    }
}
