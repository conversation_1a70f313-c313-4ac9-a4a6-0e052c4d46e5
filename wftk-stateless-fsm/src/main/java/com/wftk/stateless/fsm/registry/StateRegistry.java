package com.wftk.stateless.fsm.registry;


import com.wftk.stateless.fsm.exception.NoSuchStateException;
import com.wftk.stateless.fsm.state.State;

import java.util.Collection;
import java.util.Optional;

/**
 * <AUTHOR>
 * @create 2022/10/8 15:31
 */
public interface StateRegistry<S, E, T extends State<S, E>> {

    /**
     * 注册状态
     * @param rawState
     * @param state
     */
    void register(S rawState, T state);

    default boolean contains(S rawState) {
        return get(rawState).isPresent();
    }

    /**
     * 获取状态
     * @param rawState
     * @return
     */
    Optional<T> get(S rawState);

    /**
     * 获取状态
     * @param rawState
     * @return
     * @throws NoSuchStateException
     */
    default T getNonNull(S rawState) throws NoSuchStateException {
        return get(rawState).orElseThrow(() -> new NoSuchStateException(rawState));
    }

    /**
     * 获取所有原始状态
     */
    Collection<S> getAllRaw();

    /**
     * 获取所有状态
     * @return
     */
    Collection<T> getAll();
}
