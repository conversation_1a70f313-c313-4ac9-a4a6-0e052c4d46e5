package com.wftk.stateless.fsm.executor.listener;


import com.wftk.stateless.fsm.context.Context;
import com.wftk.stateless.fsm.event.FSMEvent;
import com.wftk.stateless.fsm.event.listener.FSMEventListener;

/**
 * <AUTHOR>
 * @create 2022/10/8 17:54
 */
public interface FSMListenerExecutor<S, E, L extends FSMEventListener<S, E, FE>, FE extends FSMEvent> {

    <P> void execute(L listener, FE event, Context<S, E, P> context);
}
