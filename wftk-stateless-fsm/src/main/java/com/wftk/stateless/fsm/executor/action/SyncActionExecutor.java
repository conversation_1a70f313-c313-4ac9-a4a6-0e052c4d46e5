package com.wftk.stateless.fsm.executor.action;


import com.wftk.stateless.fsm.action.Action;
import com.wftk.stateless.fsm.context.Context;
import com.wftk.stateless.fsm.executor.Synchronous;

import java.util.Collection;

/**
 * <AUTHOR>
 * @create 2022/10/8 17:49
 */
public class SyncActionExecutor<S, E, T extends Action<S, E>> implements ActionExecutor<S, E, T>, Synchronous {

    @Override
    public <P> void execute(T action, Context<S, E, P> context) {
        action.doAction(context);
    }

    @Override
    public <P> void execute(Collection<T> actions, Context<S, E, P> context) {
        if (actions == null || actions.isEmpty()) {
            return;
        }
        for (T action : actions) {
            action.doAction(context);
        }
    }
}
