package com.wftk.stateless.fsm.event.listener;


import com.wftk.stateless.fsm.context.Context;
import com.wftk.stateless.fsm.event.FSMEvent;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @create 2022/10/8 11:40
 */
public interface FSMEventListener<S, E, FE extends FSMEvent> {

    /**
     * 状态机相关事件处理
     * @param event
     * @param context
     * @param <P>
     */
    <P> void onEvent(FE event, Context<S, E, P> context);

    default Class<FE> getSupportedEventType() {
        Type actualTypeArgument = ((ParameterizedType) getClass().getGenericInterfaces()[0]).getActualTypeArguments()[2];
        if (actualTypeArgument instanceof ParameterizedType) {
            actualTypeArgument = ((ParameterizedType) actualTypeArgument).getRawType();
        }

        return (Class<FE>) actualTypeArgument;
    }
}
