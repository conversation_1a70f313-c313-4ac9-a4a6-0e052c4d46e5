package com.wftk.stateless.fsm.executor.listener;


import com.wftk.stateless.fsm.context.Context;
import com.wftk.stateless.fsm.event.FSMEvent;
import com.wftk.stateless.fsm.event.listener.FSMEventListener;
import com.wftk.stateless.fsm.executor.Synchronous;

/**
 * <AUTHOR>
 * @create 2022/10/8 17:57
 */
public class SyncFSMListenerExecutor<S, E, L extends FSMEventListener<S, E, FE>, FE extends FSMEvent> implements FSMListenerExecutor<S, E, L, FE>, Synchronous {

    @Override
    public <P> void execute(L listener, FE event, Context<S, E, P> context) {
        listener.onEvent(event, context);
    }
}
