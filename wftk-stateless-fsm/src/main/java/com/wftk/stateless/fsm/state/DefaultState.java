package com.wftk.stateless.fsm.state;


import com.wftk.stateless.fsm.action.Action;
import com.wftk.stateless.fsm.context.Context;
import com.wftk.stateless.fsm.event.StateEntryEvent;
import com.wftk.stateless.fsm.event.StateExitEvent;
import com.wftk.stateless.fsm.transition.Transition;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR>
 * @create 2022/10/9 17:07
 */
public class DefaultState<S, E> implements State<S, E> {

    /**
     * 本身状态
     */
    private final S rawState;

    /**
     * 进入状态相关action
     */
    private final List<Action<S, E>> entryActions;

    /**
     * 离开状态相关action
     */
    private final List<Action<S, E>> exitActions;

    /**
     * 绑定的状态迁移对象
     */
    private final Map<E, List<Transition<S, E>>> transitions;

    public DefaultState(S rawState) {
        this.rawState = rawState;
        this.entryActions = new CopyOnWriteArrayList<>();
        this.exitActions = new CopyOnWriteArrayList<>();
        this.transitions = new HashMap<>();
    }

    @Override
    public void addEntryAction(Action<S, E> action) {
        entryActions.add(action);
    }

    @Override
    public Collection<Action<S, E>> getEntryActions() {
        return Collections.unmodifiableCollection(entryActions);
    }

    @Override
    public void addExitAction(Action<S, E> action) {
        exitActions.add(action);
    }

    @Override
    public Collection<Action<S, E>> getExitActions() {
        return Collections.unmodifiableCollection(exitActions);
    }

    @Override
    public synchronized void addTransition(Transition<S, E> transition) {
        List<Transition<S, E>> transitionItems = transitions.get(transition.getEvent());
        if (transitionItems == null || transitionItems.isEmpty()) {
            transitionItems = new ArrayList<>();
        }
        transitionItems.add(transition);
        transitions.put(transition.getEvent(), transitionItems);
    }

    @Override
    public Collection<Transition<S, E>> getTransitions() {
        List<Transition<S, E>> resultList = new ArrayList<>();
        for (List<Transition<S, E>> list : transitions.values()) {
            resultList.addAll(list);
        }
        return Collections.unmodifiableCollection(resultList);
    }

    @Override
    public Collection<Transition<S, E>> getTransitions(E event) {
        return Collections.unmodifiableCollection(transitions.get(event));
    }

    @Override
    public S getRawState() {
        return rawState;
    }

    @Override
    public <P> void entry(Context<S, E, P> context) {
        context.getMachine().getFSMEventMulticaster().multicastEvent(new StateEntryEvent<>(rawState), context);
        context.getMachine().getActionExecutor().execute(getEntryActions(), context);
    }

    @Override
    public <P> void exit(Context<S, E, P> context) {
        context.getMachine().getFSMEventMulticaster().multicastEvent(new StateExitEvent<>(rawState), context);
        context.getMachine().getActionExecutor().execute(getExitActions(), context);
    }
}
