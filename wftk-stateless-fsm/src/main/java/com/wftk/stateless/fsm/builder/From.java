package com.wftk.stateless.fsm.builder;

import com.wftk.stateless.fsm.action.Action;

/**
 * <AUTHOR>
 * @create 2022/10/20 17:59
 */
public interface From<S, E> {

    To<S, E> to(S rawState);

    /**
     * 添加进入状态所触发的动作
     * @param entryAction
     * @return
     */
    To<S, E> onEntry(Action<S, E> entryAction);

    /**
     * 添加离开状态所触发的动作
     * @param exitAction
     * @return
     */
    To<S, E> onExit(Action<S, E> exitAction);
}
