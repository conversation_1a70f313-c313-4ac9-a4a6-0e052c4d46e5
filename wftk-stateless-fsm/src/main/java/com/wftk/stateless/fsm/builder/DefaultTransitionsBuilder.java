package com.wftk.stateless.fsm.builder;


import com.wftk.stateless.fsm.machine.StateMachine;

/**
 * <AUTHOR>
 * @create 2022/10/21 09:28
 */
public class DefaultTransitionsBuilder<S, E> implements TransitionsBuilder<S, E> {

    private final StateMachineBuilder<S, E> stateMachineBuilder;
    private final StateMachine<S, E> stateMachine;

    public DefaultTransitionsBuilder(StateMachineBuilder<S, E> stateMachineBuilder, StateMachine<S, E> stateMachine) {
        this.stateMachineBuilder = stateMachineBuilder;
        this.stateMachine = stateMachine;
    }

    @Override
    public TransitionsBuilder<S, E> and() {
        return this;
    }

    @Override
    public ExternalTransitionBuilder<S, E> withExternal() {
        return new DefaultTransitionBuilder<>(this, stateMachine);
    }

    @Override
    public InternalTransitionBuilder<S, E> withInternal() {
        return new DefaultTransitionBuilder<>(this, stateMachine);
    }

    @Override
    public StateMachineBuilder<S, E> machine() {
        return stateMachineBuilder;
    }

}
