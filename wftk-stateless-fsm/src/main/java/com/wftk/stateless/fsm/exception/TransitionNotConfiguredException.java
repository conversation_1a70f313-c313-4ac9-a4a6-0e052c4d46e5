package com.wftk.stateless.fsm.exception;

/**
 * <AUTHOR>
 * @create 2022/10/9 09:59
 */
public class TransitionNotConfiguredException extends FSMException {
    private static final long serialVersionUID = 3000709498990757850L;

    private final Object rawState;

    public TransitionNotConfiguredException(Object rawState) {
        this.rawState = rawState;
    }

    @Override
    public String getMessage() {
        return "No transition found for rawState: " + rawState;
    }
}
