package com.wftk.stateless.fsm.context;

/**
 * <AUTHOR>
 * @create 2022/10/21 16:19
 */
public class ParamContext <S, E, P> extends BaseContext<S, E, P> {

    public ParamContext(S rawState) {
        super(rawState);
    }

    public ParamContext(S rawState, P parameter) {
        super(rawState, false, parameter);
    }

    public ParamContext(S rawState, boolean fireEntryWhileInitial) {
        super(rawState, fireEntryWhileInitial);
    }

    public ParamContext(S rawState, boolean fireEntryWhileInitial, P parameter) {
        super(rawState, fireEntryWhileInitial, parameter);
    }
}
