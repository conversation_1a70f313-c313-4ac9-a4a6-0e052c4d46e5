package com.wftk.stateless.fsm.event;

/**
 * <AUTHOR>
 * @create 2022/10/11 17:00
 */
public abstract class BaseTransitionEvent<S> implements FSMEvent {

    /**
     * 转换类型
     */
    private final TransitionType transitionType;

    /**
     * 转换前原始状态
     */
    private final S from;

    /**
     * 转换后原始状态
     */
    private final S to;

    protected BaseTransitionEvent(TransitionType transitionType, S from, S to) {
        this.transitionType = transitionType;
        this.from = from;
        this.to = to;
    }

    public S getFrom() {
        return from;
    }

    public S getTo() {
        return to;
    }

    public TransitionType getTransitionType() {
        return transitionType;
    }

    /**
     * 转换类型
     */
    public enum TransitionType {
        INTERNAL, EXTERNAL;
    }
}
