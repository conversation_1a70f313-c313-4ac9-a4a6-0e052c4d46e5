package com.wftk.stateless.fsm.machine;


import com.wftk.stateless.fsm.action.Action;
import com.wftk.stateless.fsm.context.Context;
import com.wftk.stateless.fsm.event.FSMEvent;
import com.wftk.stateless.fsm.event.listener.FSMEventListener;
import com.wftk.stateless.fsm.event.multicaster.FSMEventMulticaster;
import com.wftk.stateless.fsm.exception.handler.FSMExceptionHandler;
import com.wftk.stateless.fsm.executor.action.ActionExecutor;
import com.wftk.stateless.fsm.executor.listener.FSMListenerExecutor;
import com.wftk.stateless.fsm.registry.StateRegistry;
import com.wftk.stateless.fsm.state.State;

/**
 * 状态机
 * <AUTHOR>
 * @create 2022/10/8 13:56
 */
public interface StateMachine<S, E> {

    /**
     * 获取状态机ID
     * @return
     */
    String getMachineId();

    /**
     * 根据事件触发
     * @param event
     */
    <P> void fire(E event, Context<S, E, P> context);

    /**
     * 获取状态注册
     * @return
     */
    StateRegistry<S, E, State<S, E>> getStateRegistry();

    /**
     * 设置异常处理器
     */
    void setFSMExceptionHandler(FSMExceptionHandler fsmExceptionHandler);

    /**
     * 获取异常处理器
     * @return
     */
    FSMExceptionHandler getFSMExceptionHandler();


    /**
     * 设置Action执行器
     * @param actionExecutor
     */
    void setActionExecutor(ActionExecutor<S, E, Action<S, E>> actionExecutor);

    /**
     * 获取Action执行器
     * @return
     */
    ActionExecutor<S, E, Action<S, E>> getActionExecutor();

    /**
     * 设置事件监听执行器
     * @param fsmListenerExecutor
     */
    void setFSMListenerExecutor(FSMListenerExecutor<S, E, FSMEventListener<S, E, FSMEvent>, FSMEvent> fsmListenerExecutor);

    /**
     * 获取事件监听执行器
     * @return
     */
    FSMListenerExecutor<S, E, FSMEventListener<S, E, FSMEvent>, FSMEvent> getFSMListenerExecutor();

    /**
     * 设置事件广播器
     * @param fsmEventMulticaster
     */
    void setFSMEventMulticaster(FSMEventMulticaster<S, E> fsmEventMulticaster);

    /**
     * 获取事件广播器
     * @return
     */
    FSMEventMulticaster<S, E> getFSMEventMulticaster();

    /**
     * 添加事件监听器
     * @param fsmEventListener
     * @param <FE>
     */
    <FE extends FSMEvent> void addFSMEventListener(FSMEventListener<S, E, FE> fsmEventListener);
}
