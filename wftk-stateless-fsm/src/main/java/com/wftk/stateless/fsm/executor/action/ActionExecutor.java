package com.wftk.stateless.fsm.executor.action;


import com.wftk.stateless.fsm.action.Action;
import com.wftk.stateless.fsm.context.Context;

import java.util.Collection;

/**
 * <AUTHOR>
 * @create 2022/10/8 17:28
 */
public interface ActionExecutor<S, E, T extends Action<S, E>> {

    /**
     * 执行任务
     * @param action
     * @param context
     * @param <P>
     */
    <P> void execute(T action, Context<S, E, P> context);

    /***
     * 执行任务
     * @param actions
     * @param context
     * @param <P>
     */
    <P> void execute(Collection<T> actions, Context<S, E, P> context);
}
