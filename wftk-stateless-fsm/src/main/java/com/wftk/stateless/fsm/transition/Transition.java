package com.wftk.stateless.fsm.transition;


import com.wftk.stateless.fsm.action.Action;
import com.wftk.stateless.fsm.condition.Condition;
import com.wftk.stateless.fsm.context.Context;
import com.wftk.stateless.fsm.state.State;

/**
 *
 * <AUTHOR>
 * @create 2022/10/8 14:29
 */
public interface Transition<S, E> {

    /**
     * 添加转换动作
     * @param action
     */
    void addAction(Action<S, E> action);

    /**
     * 添加条件
     * @param condition
     */
    void setCondition(Condition<S, E> condition);

    /**
     * 状态转换
     * @param context
     * @param <P>
     */
    <P> boolean transit(Context<S, E, P> context);

    /**
     * 获取源状态
     * @return
     */
    State<S, E> getSourceState();

    /**
     * 获取目标状态
     * @return
     */
    State<S, E> getTargetState();

    /**
     * 获取绑定的事件
     * @return
     */
    E getEvent();
}
