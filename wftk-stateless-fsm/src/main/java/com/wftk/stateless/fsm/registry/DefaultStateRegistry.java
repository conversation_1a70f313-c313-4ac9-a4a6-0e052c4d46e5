package com.wftk.stateless.fsm.registry;


import com.wftk.stateless.fsm.state.State;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @create 2022/10/8 15:53
 */
public class DefaultStateRegistry<S, E, T extends State<S, E>> implements StateRegistry<S, E, T> {

    private final Map<S, T> statesMap;

    public DefaultStateRegistry() {
        this.statesMap = new ConcurrentHashMap<>();
    }

    @Override
    public void register(S rawState, T state) {
        statesMap.put(rawState, state);
    }

    @Override
    public Optional<T> get(S stateId) {
        return Optional.ofNullable(statesMap.get(stateId));
    }

    @Override
    public Collection<S> getAllRaw() {
        return statesMap.keySet();
    }

    @Override
    public Collection<T> getAll() {
        return statesMap.values();
    }
}
