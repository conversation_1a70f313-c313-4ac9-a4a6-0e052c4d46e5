package com.wftk.stateless.fsm.transition;


import com.wftk.stateless.fsm.action.Action;
import com.wftk.stateless.fsm.condition.Condition;
import com.wftk.stateless.fsm.context.Context;
import com.wftk.stateless.fsm.context.ExecutionContext;
import com.wftk.stateless.fsm.event.multicaster.FSMEventMulticaster;
import com.wftk.stateless.fsm.state.State;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR>
 * @create 2022/10/9 11:36
 */
public abstract class BaseTransition<S, E> implements Transition<S, E> {


    /**
     * 原状态
     */
    private final State<S, E> sourceState;

    /**
     * 目标状态
     */
    private final State<S, E> targetState;

    /**
     * 绑定的触发事件
     */
    private final E event;

    private Condition<S, E> condition;

    private final List<Action<S, E>> actions;

    public BaseTransition(State<S, E> sourceState, State<S, E> targetState, E event) {
        this.sourceState = sourceState;
        this.targetState = targetState;
        this.event = event;
        this.actions = new CopyOnWriteArrayList<>();
    }

    @Override
    public void addAction(Action<S, E> action) {
        actions.add(action);
    }

    @Override
    public void setCondition(Condition<S, E> condition) {
        this.condition = condition;
    }

    @Override
    public <P> boolean transit(Context<S, E, P> context) {
        if (condition != null && !condition.matches(context)) {
            return false;
        }
        FSMEventMulticaster<S, E> multicaster = context.getMachine().getFSMEventMulticaster();
        try {
            beforeTransit(context);
            beforeTransitEvent(multicaster, context);
            if (context instanceof ExecutionContext) {
                ExecutionContext<S, E> executionContext = (ExecutionContext<S, E>) context;
                executionContext.setTargetRawState(targetState.getRawState());
            }
            boolean isSuccess = doTransit(context);
            if (context instanceof ExecutionContext) {
                ExecutionContext<S, E> executionContext = (ExecutionContext<S, E>) context;
                executionContext.setSuccess(isSuccess);
                executionContext.setCurrentRawState(isSuccess ? targetState.getRawState() : sourceState.getRawState());
            }
            return isSuccess;
        } finally {
            afterTransit(context);
            afterTransitEvent(multicaster, context);
        }
    }

    @Override
    public State<S, E> getSourceState() {
        return sourceState;
    }

    @Override
    public State<S, E> getTargetState() {
        return targetState;
    }

    @Override
    public E getEvent() {
        return event;
    }

    /**
     * 执行相关Action
     * @param context
     * @param <P>
     */
    protected <P> void doActions(Context<S, E, P> context) {
        context.getMachine().getActionExecutor().execute(actions, context);
    }

    protected abstract <P> boolean doTransit(Context<S, E, P> context);

    protected abstract <P> void beforeTransitEvent(FSMEventMulticaster<S, E> multicaster, Context<S, E, P> context);

    protected abstract <P> void afterTransitEvent(FSMEventMulticaster<S, E> multicaster, Context<S, E, P> context);

    protected <P> void beforeTransit(Context<S, E, P> context) {

    }

    protected <P> void afterTransit(Context<S, E, P> context) {

    }
}
