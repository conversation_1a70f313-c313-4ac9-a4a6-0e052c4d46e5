package com.wftk.stateless.fsm.context;


import com.wftk.stateless.fsm.aware.StateMachineAware;
import com.wftk.stateless.fsm.machine.StateMachine;

/**
 * <AUTHOR>
 * @create 2022/10/8 16:28
 */
public abstract class BaseContext<S, E, P> implements Context<S, E, P>, StateMachineAware<S, E>, ExecutionContext<S, E> {

    /**
     * 参数
     */
    private final P parameter;

    /**
     * 状态
     */
    private final S rawState;

    /**
     * 当前所处原始状态
     */
    private S currentRawState;

    /**
     * 目标原始状态
     */
    private S targetRawState;

    /**
     * 每次设置了初始值后是否触发entry方法
     */
    private final boolean fireEntryWhileInitial;

    private StateMachine<S, E> stateMachine;

    /**
     * 是否成功
     */
    private boolean isSuccess = false;

    public BaseContext(S rawState) {
        this(rawState, false, null);
    }

    public BaseContext(S rawState, boolean fireEntryWhileInitial) {
        this(rawState, fireEntryWhileInitial, null);
    }

    public BaseContext(S rawState, boolean fireEntryWhileInitial, P parameter) {
        this.rawState = rawState;
        this.currentRawState = rawState;
        this.fireEntryWhileInitial = fireEntryWhileInitial;
        this.parameter = parameter;
    }

    @Override
    public P getParameter() {
        return parameter;
    }

    @Override
    public final StateMachine<S, E> getMachine() {
        return stateMachine;
    }

    @Override
    public S getInitialState() {
        return rawState;
    }

    @Override
    public void setStateMachine(StateMachine<S, E> stateMachine) {
        this.stateMachine = stateMachine;
    }

    @Override
    public boolean fireEntryWhileInitial() {
        return fireEntryWhileInitial;
    }

    @Override
    public S getCurrentRawState() {
        return currentRawState;
    }

    @Override
    public S getTargetRawState() {
        return targetRawState;
    }

    @Override
    public boolean isSuccess() {
        return isSuccess;
    }

    @Override
    public void setCurrentRawState(S currentRawState) {
        this.currentRawState = currentRawState;
    }

    @Override
    public void setTargetRawState(S targetRawState) {
        this.targetRawState = targetRawState;
    }

    @Override
    public boolean setSuccess(boolean isSuccess) {
        return this.isSuccess = isSuccess;
    }
}
