package com.wftk.stateless.fsm.state;


import com.wftk.stateless.fsm.action.Action;
import com.wftk.stateless.fsm.context.Context;
import com.wftk.stateless.fsm.exception.TransitionNotConfiguredException;
import com.wftk.stateless.fsm.transition.Transition;

import java.util.Collection;

/**
 * 状态
 * <AUTHOR>
 * @create 2022/10/8 14:15
 */
public interface State<S, E> {

    /**
     * 添加进入状态动作
     * @param action
     */
    void addEntryAction(Action<S, E> action);

    /**
     * 获取进入状态动作
     * @return
     */
    Collection<Action<S, E>> getEntryActions();

    /**
     * 添加离开状态动作
     * @param action
     */
    void addExitAction(Action<S, E> action);

    /**
     * 获取离开状态动作
     * @return
     */
    Collection<Action<S, E>> getExitActions();

    /**
     * @param transition
     */
    void addTransition(Transition<S, E> transition);

    /**
     *
     * @return
     */
    Collection<Transition<S, E>> getTransitions();

    /**
     * 根据绑定的事件查询
     * @param event
     * @return
     */
    Collection<Transition<S, E>> getTransitions(E event);

    /**
     * 获取transitions
     * @return
     * @throws TransitionNotConfiguredException
     */
    default Collection<Transition<S, E>> getNonEmptyTransitions() throws TransitionNotConfiguredException {
        Collection<Transition<S, E>> transitions = getTransitions();
        if (transitions == null || transitions.isEmpty()) {
            throw new TransitionNotConfiguredException(getRawState());
        }
        return transitions;
    }

    /**
     * 获取transitions
     * @param event
     * @return
     * @throws TransitionNotConfiguredException
     */
    default Collection<Transition<S, E>> getNonEmptyTransitions(E event) throws TransitionNotConfiguredException {
        Collection<Transition<S, E>> transitions = getTransitions(event);
        if (transitions == null || transitions.isEmpty()) {
            throw new TransitionNotConfiguredException(getRawState());
        }
        return transitions;
    }

    /**
     * 获取原始状态
     * @return
     */
    S getRawState();

    /**
     * 进入状态
     * @param context
     * @param <P>
     */
    <P> void entry(Context<S, E, P> context);

    /**
     * 离开状态
     * @param context
     * @param <P>
     */
    <P> void exit(Context<S, E, P> context);
}
