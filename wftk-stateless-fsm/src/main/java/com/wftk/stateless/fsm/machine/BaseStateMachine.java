package com.wftk.stateless.fsm.machine;



import com.wftk.stateless.fsm.action.Action;
import com.wftk.stateless.fsm.aware.StateMachineAware;
import com.wftk.stateless.fsm.context.Context;
import com.wftk.stateless.fsm.event.FSMEvent;
import com.wftk.stateless.fsm.event.listener.FSMEventListener;
import com.wftk.stateless.fsm.event.multicaster.DefaultFSMEventMulticaster;
import com.wftk.stateless.fsm.event.multicaster.FSMEventMulticaster;
import com.wftk.stateless.fsm.exception.FSMException;
import com.wftk.stateless.fsm.exception.handler.FSMExceptionHandler;
import com.wftk.stateless.fsm.executor.action.ActionExecutor;
import com.wftk.stateless.fsm.executor.action.SyncActionExecutor;
import com.wftk.stateless.fsm.executor.listener.FSMListenerExecutor;
import com.wftk.stateless.fsm.executor.listener.SyncFSMListenerExecutor;
import com.wftk.stateless.fsm.registry.DefaultStateRegistry;
import com.wftk.stateless.fsm.registry.StateRegistry;
import com.wftk.stateless.fsm.state.State;
import com.wftk.stateless.fsm.transition.Transition;

import java.util.Collection;
import java.util.UUID;

/**
 * <AUTHOR>
 * @create 2022/10/8 15:21
 */
public abstract class BaseStateMachine<S, E> implements StateMachine<S, E> {

    /**
     * 状态机ID
     */
    private final String machineId;

    /**
     * 状态注册器
     */
    private final StateRegistry<S, E, State<S, E>> stateRegistry;

    /**
     * 异常处理(默认为空)
     */
    private FSMExceptionHandler fsmExceptionHandler;

    /**
     * action执行器
     */
    private ActionExecutor<S, E, Action<S, E>> actionExecutor = new SyncActionExecutor<>();

    /**
     * listener执行器
     */
    private FSMListenerExecutor<S, E, FSMEventListener<S, E, FSMEvent>, FSMEvent> fsmListenerExecutor = new SyncFSMListenerExecutor<>();

    /**
     * 事件广播器
     */
    private FSMEventMulticaster<S, E> fsmEventMulticaster = new DefaultFSMEventMulticaster<>();

    protected BaseStateMachine() {
        this(UUID.randomUUID().toString(), new DefaultStateRegistry<>());
    }

    protected BaseStateMachine(StateRegistry<S, E, State<S, E>> stateRegistry) {
        this(UUID.randomUUID().toString(), stateRegistry);
    }

    protected BaseStateMachine(String machineId, StateRegistry<S, E, State<S, E>> stateRegistry) {
        this.machineId = machineId;
        this.stateRegistry = stateRegistry;
    }

    @Override
    public String getMachineId() {
        return machineId;
    }


    @Override
    public final <P> void fire(E event, Context<S, E, P> context) {
        try {
            if (context instanceof StateMachineAware) {
                ((StateMachineAware<S, E>) context).setStateMachine(this);
            }
            S currentRawState = context.getInitialState();
            State<S, E> currentState = getStateRegistry().getNonNull(currentRawState);
            //设置项(初始化状态是否执行entry方法)
            if (context.fireEntryWhileInitial()) {
                currentState.entry(context);
            }
            Collection<Transition<S, E>> transitions = currentState.getNonEmptyTransitions(event);
            for (Transition<S, E> transition : transitions) {
                transition.transit(context);
            }
        } catch (FSMException fsmException) {
            if (fsmExceptionHandler == null) {
                throw fsmException;
            }
            //如果异常处理器不为空则执行处理异常逻辑
            fsmExceptionHandler.handle(fsmException);
        }

    }

    @Override
    public StateRegistry<S, E, State<S, E>> getStateRegistry() {
        return stateRegistry;
    }

    @Override
    public void setFSMExceptionHandler(FSMExceptionHandler fsmExceptionHandler) {
        this.fsmExceptionHandler = fsmExceptionHandler;
    }

    @Override
    public FSMExceptionHandler getFSMExceptionHandler() {
        return fsmExceptionHandler;
    }

    @Override
    public void setActionExecutor(ActionExecutor<S, E, Action<S, E>> actionExecutor) {
        if (actionExecutor == null) {
            throw new FSMException("action executor must not be null.");
        }
        this.actionExecutor = actionExecutor;
    }

    @Override
    public ActionExecutor<S, E, Action<S, E>> getActionExecutor() {
        return actionExecutor;
    }

    @Override
    public void setFSMListenerExecutor(FSMListenerExecutor<S, E, FSMEventListener<S, E, FSMEvent>, FSMEvent> fsmListenerExecutor) {
        if (fsmListenerExecutor == null) {
            throw new FSMException("listener executor must not be null.");
        }
        this.fsmListenerExecutor = fsmListenerExecutor;
    }

    @Override
    public FSMListenerExecutor<S, E, FSMEventListener<S, E, FSMEvent>, FSMEvent> getFSMListenerExecutor() {
        return fsmListenerExecutor;
    }

    @Override
    public void setFSMEventMulticaster(FSMEventMulticaster<S, E> fsmEventMulticaster) {
        if (fsmEventMulticaster == null) {
            throw new FSMException("event multicaster must not be null.");
        }
        this.fsmEventMulticaster = fsmEventMulticaster;
    }

    @Override
    public FSMEventMulticaster<S, E> getFSMEventMulticaster() {
        return fsmEventMulticaster;
    }

    @Override
    public <FE extends FSMEvent> void addFSMEventListener(FSMEventListener<S, E, FE> fsmEventListener) {
        fsmEventMulticaster.addEventListener(fsmEventListener);
    }
}
