package com.wftk.stateless.fsm.exception;

/**
 * <AUTHOR>
 * @create 2022/10/8 15:39
 */
public class NoSuchStateException extends FSMException {
    private static final long serialVersionUID = -4408526304290506695L;

    private final Object rawState;

    public NoSuchStateException(Object rawState) {
        this.rawState = rawState;
    }

    @Override
    public String getMessage() {
        return "No such state: " + rawState;
    }
}
