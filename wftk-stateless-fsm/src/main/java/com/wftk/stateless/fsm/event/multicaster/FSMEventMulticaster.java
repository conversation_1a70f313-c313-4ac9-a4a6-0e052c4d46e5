package com.wftk.stateless.fsm.event.multicaster;


import com.wftk.stateless.fsm.context.Context;
import com.wftk.stateless.fsm.event.FSMEvent;
import com.wftk.stateless.fsm.event.listener.FSMEventListener;

import java.util.Collection;

/**
 * <AUTHOR>
 * @create 2022/10/10 09:46
 */
public interface FSMEventMulticaster<S, E> {

    /**
     * 广播事件
     * @param event
     * @param context
     * @param <FE>
     * @param <P>
     */
    <FE extends FSMEvent, P> void multicastEvent(FE event, Context<S, E, P> context);

    /**
     * 添加监听器
     * @param fsmEventListener
     * @param <FE>
     */
    <FE extends FSMEvent> void addEventListener(FSMEventListener<S, E, FE> fsmEventListener);

    /**
     * 移除监听器
     * @param fsmEventListener
     * @param <FE>
     */
    <FE extends FSMEvent> void removeEventListener(FSMEventListener<S, E, FE> fsmEventListener);


    /**
     * 根据事件获取可以广播的监听器
     * @param event
     * @return
     */
    Collection<FSMEventListener<S, E, FSMEvent>> getEventListeners(FSMEvent event);
}
