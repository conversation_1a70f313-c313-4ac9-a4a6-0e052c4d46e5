package com.wftk.stateless.fsm.transition;


import com.wftk.stateless.fsm.context.Context;
import com.wftk.stateless.fsm.event.BaseTransitionEvent;
import com.wftk.stateless.fsm.event.TransitionEndEvent;
import com.wftk.stateless.fsm.event.TransitionStartEvent;
import com.wftk.stateless.fsm.event.multicaster.FSMEventMulticaster;
import com.wftk.stateless.fsm.state.State;

/**
 * 涉及到状态转换
 * <AUTHOR>
 * @create 2022/10/9 16:54
 */
public class ExternalTransition<S, E> extends BaseTransition<S, E> {

    public ExternalTransition(State<S, E> sourceState, State<S, E> targetState, E event) {
        super(sourceState, targetState, event);
    }

    @Override
    protected <P> boolean doTransit(Context<S, E, P> context) {
        getSourceState().exit(context);
        doActions(context);
        getTargetState().entry(context);
        return true;
    }

    @Override
    protected <P> void beforeTransitEvent(FSMEventMulticaster<S, E> multicaster, Context<S, E, P> context) {
        TransitionStartEvent<S> transitionStartEvent = new TransitionStartEvent<>(BaseTransitionEvent.TransitionType.EXTERNAL, getSourceState().getRawState(), getTargetState().getRawState());
        multicaster.multicastEvent(transitionStartEvent, context);
    }

    @Override
    protected <P> void afterTransitEvent(FSMEventMulticaster<S, E> multicaster, Context<S, E, P> context) {
        TransitionEndEvent<S> transitionStartEvent = new TransitionEndEvent<>(BaseTransitionEvent.TransitionType.EXTERNAL, getSourceState().getRawState(), getTargetState().getRawState());
        multicaster.multicastEvent(transitionStartEvent, context);
    }


}
