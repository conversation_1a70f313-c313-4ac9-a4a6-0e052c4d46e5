package com.wftk.stateless.fsm.context;


import com.wftk.stateless.fsm.machine.StateMachine;

/**
 * <AUTHOR>
 * @create 2022/10/8 13:47
 */
public interface Context<S, E, P> {

    /**
     * 获取参数
     * @return
     */
    P getParameter();

    /**
     * 获取状态机
     * @return
     */
    StateMachine<S, E> getMachine();

    /**
     * 获取初始状态
     * @return
     */
    S getInitialState();

    /**
     * 每次设置了初始值后是否触发entry方法（默认不触发）
     * @return
     */
    boolean fireEntryWhileInitial();

    /**
     * 获取当前原始状态
     * @return
     */
    S getCurrentRawState();

    /**
     * 获取目标原始状态
     * @return
     */
    S getTargetRawState();

    /**
     * 是否转换成功
     * @return
     */
    boolean isSuccess();
}
