package com.wftk.stateless.fsm.builder;


import com.wftk.stateless.fsm.action.Action;
import com.wftk.stateless.fsm.event.FSMEvent;
import com.wftk.stateless.fsm.event.listener.FSMEventListener;
import com.wftk.stateless.fsm.event.multicaster.FSMEventMulticaster;
import com.wftk.stateless.fsm.executor.action.ActionExecutor;
import com.wftk.stateless.fsm.executor.listener.FSMListenerExecutor;

/**
 * <AUTHOR>
 * @create 2022/10/20 18:59
 */
public interface EventBuilder<S, E> {

    /**
     * 设置action执行器
     * @param actionExecutor
     * @return
     */
    EventBuilder<S, E> actionExecutor(ActionExecutor<S, E, Action<S, E>> actionExecutor);

    /**
     * 设置事件广播器
     * @param fsmEventMulticaster
     * @return
     */
    EventBuilder<S, E> multicaster(FSMEventMulticaster<S, E> fsmEventMulticaster);

    /**
     * 事件监听执行器
     * @param listenerExecutor
     * @return
     */
    EventBuilder<S, E> listenerExecutor(FSMListenerExecutor<S, E, FSMEventListener<S, E, FSMEvent>, FSMEvent> listenerExecutor);

    /**
     * 添加事件
     * @param fsmEventListener
     * @return
     * @param <FE>
     */
    <FE extends FSMEvent> EventBuilder<S, E> addListener(FSMEventListener<S, E, FE> fsmEventListener);


    StateMachineBuilder<S, E> machine();
}
