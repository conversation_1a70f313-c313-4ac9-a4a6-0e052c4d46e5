package com.wftk.stateless.fsm.builder;


import com.wftk.stateless.fsm.exception.handler.FSMExceptionHandler;
import com.wftk.stateless.fsm.machine.StateMachine;

/**
 * <AUTHOR>
 * @create 2022/10/20 18:25
 */
public interface StateMachineBuilder<S, E> {

    TransitionsBuilder<S, E> transitions();


    /**
     * 设置异常处理器
     * @param fsmExceptionHandler
     * @return
     */
    StateMachineBuilder<S, E> exceptionHandler(FSMExceptionHandler fsmExceptionHandler);

    /**
     * 事件相关配置
     * @return
     */
    EventBuilder<S, E> event();

    StateMachine<S, E> build();
}
