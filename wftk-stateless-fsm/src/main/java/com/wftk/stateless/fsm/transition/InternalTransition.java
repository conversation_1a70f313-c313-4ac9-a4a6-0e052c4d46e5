package com.wftk.stateless.fsm.transition;


import com.wftk.stateless.fsm.context.Context;
import com.wftk.stateless.fsm.event.BaseTransitionEvent;
import com.wftk.stateless.fsm.event.TransitionStartEvent;
import com.wftk.stateless.fsm.event.multicaster.FSMEventMulticaster;
import com.wftk.stateless.fsm.state.State;

/**
 * 不涉及到状态转换
 * <AUTHOR>
 * @create 2022/10/9 16:32
 */
public class InternalTransition<S, E> extends BaseTransition<S, E> {

    public InternalTransition(State<S, E> sourceState, E event) {
        super(sourceState, sourceState, event);
    }

    @Override
    protected <P> boolean doTransit(Context<S, E, P> context) {
        doActions(context);
        return true;
    }

    @Override
    protected <P> void beforeTransitEvent(FSMEventMulticaster<S, E> multicaster, Context<S, E, P> context) {
        TransitionStartEvent<S> transitionStartEvent = new TransitionStartEvent<>(BaseTransitionEvent.TransitionType.INTERNAL, getSourceState().getRawState(), getTargetState().getRawState());
        multicaster.multicastEvent(transitionStartEvent, context);
    }

    @Override
    protected <P> void afterTransitEvent(FSMEventMulticaster<S, E> multicaster, Context<S, E, P> context) {
        TransitionStartEvent<S> transitionStartEvent = new TransitionStartEvent<>(BaseTransitionEvent.TransitionType.INTERNAL, getSourceState().getRawState(), getTargetState().getRawState());
        multicaster.multicastEvent(transitionStartEvent, context);
    }
}
