package com.wftk.stateless.fsm.builder;


import com.wftk.stateless.fsm.action.Action;
import com.wftk.stateless.fsm.event.FSMEvent;
import com.wftk.stateless.fsm.event.listener.FSMEventListener;
import com.wftk.stateless.fsm.event.multicaster.FSMEventMulticaster;
import com.wftk.stateless.fsm.exception.handler.FSMExceptionHandler;
import com.wftk.stateless.fsm.executor.action.ActionExecutor;
import com.wftk.stateless.fsm.executor.listener.FSMListenerExecutor;
import com.wftk.stateless.fsm.machine.DefaultStateMachine;
import com.wftk.stateless.fsm.machine.StateMachine;

/**
 * <AUTHOR>
 * @create 2022/10/20 19:05
 */
public class DefaultStateMachineBuilder<S, E> implements StateMachineBuilder<S, E>, EventBuilder<S, E> {

    private final StateMachine<S, E> stateMachine;

    public DefaultStateMachineBuilder() {
        this.stateMachine = new DefaultStateMachine<>();
    }

    public DefaultStateMachineBuilder(StateMachine<S, E> stateMachine) {
        this.stateMachine = stateMachine;
    }


    @Override
    public EventBuilder<S, E> actionExecutor(ActionExecutor<S, E, Action<S, E>> actionExecutor) {
        stateMachine.setActionExecutor(actionExecutor);
        return this;
    }

    @Override
    public EventBuilder<S, E> multicaster(FSMEventMulticaster<S, E> fsmEventMulticaster) {
        stateMachine.setFSMEventMulticaster(fsmEventMulticaster);
        return this;
    }

    @Override
    public EventBuilder<S, E> listenerExecutor(FSMListenerExecutor<S, E, FSMEventListener<S, E, FSMEvent>, FSMEvent> listenerExecutor) {
        stateMachine.setFSMListenerExecutor(listenerExecutor);
        return this;
    }

    @Override
    public <FE extends FSMEvent> EventBuilder<S, E> addListener(FSMEventListener<S, E, FE> fsmEventListener) {
        stateMachine.addFSMEventListener(fsmEventListener);
        return this;
    }

    @Override
    public StateMachineBuilder<S, E> machine() {
        return this;
    }

    @Override
    public TransitionsBuilder<S, E> transitions() {
        return new DefaultTransitionsBuilder<>(this, stateMachine);
    }


    @Override
    public StateMachineBuilder<S, E> exceptionHandler(FSMExceptionHandler fsmExceptionHandler) {
        return this;
    }

    @Override
    public EventBuilder<S, E> event() {
        return this;
    }

    @Override
    public StateMachine<S, E> build() {
        return stateMachine;
    }
}
