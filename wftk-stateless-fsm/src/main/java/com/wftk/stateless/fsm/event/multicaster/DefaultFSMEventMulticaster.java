package com.wftk.stateless.fsm.event.multicaster;


import com.wftk.stateless.fsm.context.Context;
import com.wftk.stateless.fsm.event.FSMEvent;
import com.wftk.stateless.fsm.event.listener.FSMEventListener;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @create 2022/10/10 09:57
 */
public class DefaultFSMEventMulticaster<S, E> implements FSMEventMulticaster<S, E> {

    private final Map<Class<? extends FSMEvent>, Set<FSMEventListener<S, E, FSMEvent>>> listeners;

    public DefaultFSMEventMulticaster() {
        this.listeners = new ConcurrentHashMap<>();
    }

    @Override
    public <FE extends FSMEvent, P> void multicastEvent(FE event, Context<S, E, P> context) {
        for (FSMEventListener<S, E, FSMEvent> eventListener : getEventListeners(event)) {
            eventListener.onEvent(event, context);
        }
    }

    @Override
    public synchronized <FE extends FSMEvent> void addEventListener(FSMEventListener<S, E, FE> fsmEventListener) {
        Class<FE> supportedEventType = fsmEventListener.getSupportedEventType();
        Set<FSMEventListener<S, E, FSMEvent>> currentEventListeners = listeners.get(supportedEventType);
        if (currentEventListeners == null) {
            currentEventListeners = new LinkedHashSet<>();
        }
        currentEventListeners.add((FSMEventListener<S, E, FSMEvent>) fsmEventListener);
        listeners.put(supportedEventType, currentEventListeners);
    }

    @Override
    public synchronized  <FE extends FSMEvent> void removeEventListener(FSMEventListener<S, E, FE> fsmEventListener) {
        listeners.get(fsmEventListener.getSupportedEventType()).remove(fsmEventListener);
    }

    @Override
    public Collection<FSMEventListener<S, E, FSMEvent>> getEventListeners(FSMEvent event) {
        //自身
        Set<FSMEventListener<S, E, FSMEvent>> currentEventListeners = listeners.get(event.getClass());
        if (currentEventListeners == null) {
            currentEventListeners = new LinkedHashSet<>();
        }

        //父类
        for (Map.Entry<Class<? extends FSMEvent>, Set<FSMEventListener<S, E, FSMEvent>>> entry : listeners.entrySet()) {
            if (!entry.getKey().isAssignableFrom(event.getClass())) {
                continue;
            }
            currentEventListeners.addAll(entry.getValue());
        }
        return Collections.unmodifiableCollection(currentEventListeners);
    }
}
