package com.wftk.file.biz.spring.boot.autoconfigure.common;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @create 2023/6/6 19:17
 */
public class FileItem {

    @NotBlank(message = "编码不能为空")
    private String code;

    @NotBlank(message = "文件名不能为空")
    private String fileName;

    @NotBlank(message = "文件MD5不能为空")
    private String md5;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }
}
