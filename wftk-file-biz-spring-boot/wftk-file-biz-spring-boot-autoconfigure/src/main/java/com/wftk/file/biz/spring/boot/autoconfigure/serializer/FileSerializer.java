package com.wftk.file.biz.spring.boot.autoconfigure.serializer;


import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.fasterxml.jackson.databind.ser.std.StringSerializer;
import com.wftk.file.biz.spring.boot.autoconfigure.common.ApplicationContextHolder;
import com.wftk.file.biz.spring.boot.autoconfigure.common.FileResult;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.request.DefaultFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.request.FileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.response.UploadedFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.manager.ResourceManager;

import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR>
 * @create 2023/6/7 14:18
 */
public class FileSerializer extends JsonSerializer<String> implements ContextualSerializer {

    /**
     * OSS角色
     */
    private final String role;

    /**
     * ossManager名称(通过名称从spring容器中获取)
     */
    private final String resourceManager;


    public FileSerializer() {
        this(null, null);
    }

    public FileSerializer(String role, String resourceManager) {
        this.role = role;
        this.resourceManager = resourceManager;
    }

    @Override
    public void serialize(String value, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (value == null) {
            jsonGenerator.writeNull();
            return;
        }
        if (StrUtil.isBlank(value)) {
            jsonGenerator.writeString(value);
            return;
        }
        ResourceManager manager = (ResourceManager) ApplicationContextHolder.applicationContext.getBean(resourceManager);
        if (value.startsWith("http") || value.startsWith("https")) {
            FileResult fileResult = new FileResult();
            fileResult.setUrl(URLUtil.url(value));
            jsonGenerator.writeObject(fileResult);
            return;
        }
        FileMeta fileMeta = new DefaultFileMeta(new File(value), null);
        UploadedFileMeta uploadedFileMeta = manager.get(role, fileMeta);
        FileResult fileResult = new FileResult();
        fileResult.setFileName(value);
        fileResult.setUrl(uploadedFileMeta.getUrl());
        jsonGenerator.writeObject(fileResult);
    }


    @Override
    public JsonSerializer<?> createContextual(SerializerProvider serializerProvider, BeanProperty beanProperty) throws JsonMappingException {
        RFile annotation = beanProperty.getAnnotation(RFile.class);
        if (annotation == null) {
            return new StringSerializer();
        }
        return new FileSerializer(annotation.role(), annotation.resourceManager());
    }
}
