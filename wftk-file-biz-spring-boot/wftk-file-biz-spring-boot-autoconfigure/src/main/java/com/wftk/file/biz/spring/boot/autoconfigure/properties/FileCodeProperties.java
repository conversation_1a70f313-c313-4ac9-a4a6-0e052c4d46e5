package com.wftk.file.biz.spring.boot.autoconfigure.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.Map;


/**
 * <AUTHOR>
 * @create 2023/6/6 18:55
 */
@ConfigurationProperties("config.file")
public class FileCodeProperties {

    @NestedConfigurationProperty
    private Map<String, FileCodeItem> item;


    public Map<String, FileCodeItem> getItem() {
        return item;
    }

    public void setItem(Map<String, FileCodeItem> item) {
        this.item = item;
    }


    /**
     *
     */
    public static class FileCodeItem {

        private String role;

        /**
         * 业务指定的路径
         */
        private String dir;

        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }

        public String getDir() {
            return dir;
        }

        public void setDir(String dir) {
            this.dir = dir;
        }
    }
}
