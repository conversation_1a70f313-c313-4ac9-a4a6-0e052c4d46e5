package com.wftk.file.biz.spring.boot.autoconfigure.common;


import java.net.URL;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/6/6 19:00
 */
public class FileResult {

    private String fileName;
    private URL url;
    private Map<String, String> headers;


    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public URL getUrl() {
        return url;
    }

    public void setUrl(URL url) {
        this.url = url;
    }

    public Map<String, String> getHeaders() {
        return headers;
    }

    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }
}
