package com.wftk.file.biz.spring.boot.autoconfigure.uploader;


import com.wftk.file.biz.spring.boot.autoconfigure.common.FileItem;
import com.wftk.file.biz.spring.boot.autoconfigure.common.FileResult;
import com.wftk.file.biz.spring.boot.autoconfigure.exception.FileCodeNotExistException;

/**
 * <AUTHOR>
 * @create 2023/6/6 18:59
 */
public interface FileUploader {

    /**
     * 上传文件
     * @param fileItem
     * @param replaceFileName
     * @return
     * @throws FileCodeNotExistException
     */
    FileResult upload(FileItem fileItem, boolean replaceFileName) throws FileCodeNotExistException;
}
