package com.wftk.file.biz.spring.boot.autoconfigure.serializer;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @create 2023/6/7 14:22
 */
@Target({ElementType.FIELD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@JsonSerialize(using = FileSerializer.class)
public @interface RFile {

    /**
     * 配置的角色
     * @return
     */
    String role();

    /**
     * 资源管理器
     * @return
     */
    String resourceManager();

}
