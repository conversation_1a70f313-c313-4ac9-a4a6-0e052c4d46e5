package com.wftk.file.biz.spring.boot.autoconfigure.registry;

import cn.hutool.core.collection.CollectionUtil;
import com.wftk.common.core.registry.ObjectRegistry;
import com.wftk.file.biz.spring.boot.autoconfigure.properties.FileCodeProperties;
import org.springframework.beans.factory.InitializingBean;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @create 2023/6/6 18:42
 */
public class FileCodeRegistry implements ObjectRegistry<String, FileCodeRegistry.UploadFileMeta>, InitializingBean {

    private final Map<String, UploadFileMeta> registry = new ConcurrentHashMap<>();

    private final FileCodeProperties fileCodeProperties;

    public FileCodeRegistry(FileCodeProperties fileCodeProperties) {
        this.fileCodeProperties = fileCodeProperties;
    }

    @Override
    public boolean register(String s, UploadFileMeta uploadFileMeta) {
        registry.put(s, uploadFileMeta);
        return true;
    }

    @Override
    public boolean register(Map<String, UploadFileMeta> objects) {
        registry.putAll(objects);
        return true;
    }

    @Override
    public boolean register(ObjectRegistry<String, UploadFileMeta> objectRegistry) {
        registry.putAll(objectRegistry.getAll());
        return true;
    }

    @Override
    public Optional<UploadFileMeta> get(String s) {
        return Optional.ofNullable(registry.get(s));
    }

    @Override
    public Map<String, UploadFileMeta> getAll() {
        return registry;
    }

    @Override
    public boolean remove(String s) {
        registry.remove(s);
        return true;
    }

    @Override
    public boolean removeAll() {
        registry.clear();
        return true;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (CollectionUtil.isNotEmpty(fileCodeProperties.getItem())) {
            fileCodeProperties.getItem().forEach((k, v) -> {
                UploadFileMeta uploadFileMeta = new UploadFileMeta(v.getRole(), v.getDir());
                register(k, uploadFileMeta);
            });
        }
    }


    /**
     *
     */
    public static class UploadFileMeta {
        private final String role;
        private final String dir;

        public UploadFileMeta(String role, String dir) {
            this.role = role;
            this.dir = dir;
        }

        public String getRole() {
            return role;
        }

        public String getDir() {
            return dir;
        }
    }

}
