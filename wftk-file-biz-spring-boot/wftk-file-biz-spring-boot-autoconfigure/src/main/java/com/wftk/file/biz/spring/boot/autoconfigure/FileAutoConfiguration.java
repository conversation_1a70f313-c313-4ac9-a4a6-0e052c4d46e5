package com.wftk.file.biz.spring.boot.autoconfigure;

import com.wftk.file.biz.spring.boot.autoconfigure.common.ApplicationContextHolder;
import com.wftk.file.biz.spring.boot.autoconfigure.properties.FileCodeProperties;
import com.wftk.file.biz.spring.boot.autoconfigure.registry.FileCodeRegistry;
import com.wftk.file.biz.spring.boot.autoconfigure.uploader.DefaultFileUploader;
import com.wftk.file.biz.spring.boot.autoconfigure.uploader.FileUploader;
import com.wftk.file.manager.spring.boot.autoconfigure.core.manager.ResourceManager;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2023/6/7 13:54
 */
@Configuration
@EnableConfigurationProperties(FileCodeProperties.class)
public class FileAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    FileCodeRegistry fileCodeRegistry(FileCodeProperties fileCodeProperties) {
        return new FileCodeRegistry(fileCodeProperties);
    }

    @Bean
    @ConditionalOnMissingBean
    FileUploader fileUploader(FileCodeRegistry fileCodeRegistry, ResourceManager resourceManager) {
        return new DefaultFileUploader(fileCodeRegistry, resourceManager);
    }

    @Bean
    ApplicationContextHolder applicationContextHolder() {
        return new ApplicationContextHolder();
    }
}
