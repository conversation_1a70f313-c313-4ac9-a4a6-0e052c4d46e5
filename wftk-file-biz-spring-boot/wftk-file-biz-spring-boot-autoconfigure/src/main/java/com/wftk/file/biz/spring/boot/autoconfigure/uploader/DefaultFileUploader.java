package com.wftk.file.biz.spring.boot.autoconfigure.uploader;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.file.biz.spring.boot.autoconfigure.common.FileItem;
import com.wftk.file.biz.spring.boot.autoconfigure.common.FileResult;
import com.wftk.file.biz.spring.boot.autoconfigure.exception.FileCodeNotExistException;
import com.wftk.file.biz.spring.boot.autoconfigure.registry.FileCodeRegistry;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.request.DefaultFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.request.FileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.response.CloudUploadedFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.file.response.UploadedFileMeta;
import com.wftk.file.manager.spring.boot.autoconfigure.core.manager.ResourceManager;

import java.io.File;

/**
 * <AUTHOR>
 * @create 2023/6/6 19:01
 */
public class DefaultFileUploader implements FileUploader {

    private final FileCodeRegistry fileCodeRegistry;
    private final ResourceManager resourceManager;

    public DefaultFileUploader(FileCodeRegistry fileCodeRegistry, ResourceManager resourceManager) {
        this.fileCodeRegistry = fileCodeRegistry;
        this.resourceManager = resourceManager;
    }

    @Override
    public FileResult upload(FileItem fileItem, boolean replaceFileName) throws FileCodeNotExistException {
        String code = fileItem.getCode();
        FileCodeRegistry.UploadFileMeta uploadFileMeta = fileCodeRegistry.get(code)
                .orElseThrow(() -> new FileCodeNotExistException(code));
        String fileName = fileItem.getFileName();
        if (replaceFileName) {
            fileName = IdUtil.getSnowflakeNextIdStr() + "." + FileNameUtil.extName(fileName);
        }
        File file;
        if (StrUtil.isNotBlank(uploadFileMeta.getDir())) {
            file = FileUtil.file(uploadFileMeta.getDir(), fileName);
        } else {
            file = FileUtil.file(fileName);
        }

        FileMeta fileMeta = new DefaultFileMeta(file, fileItem.getMd5());
        UploadedFileMeta uploadedFileMeta = resourceManager.save(uploadFileMeta.getRole(), fileMeta, false);
        FileResult fileResult = new FileResult();
        fileResult.setFileName(uploadedFileMeta.getFileName());
        fileResult.setUrl(uploadedFileMeta.getUrl());
        if (uploadedFileMeta instanceof CloudUploadedFileMeta cloudUploadedFileMeta) {
            fileResult.setHeaders(cloudUploadedFileMeta.getHeaders());
        }
        return fileResult;
    }
}
