package com.wftk.cache.spring.boot.autoconfigure.store.caffeine;

import com.github.benmanes.caffeine.cache.CacheLoader;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2023/8/31 18:48
 */
public class ObjectCaffeineObjectStore extends CaffeineObjectStore<Object, Object> {
    public ObjectCaffeineObjectStore(long expireIn, TimeUnit timeUnit, long maxCacheSize) {
        super(expireIn, timeUnit, maxCacheSize);
    }

    public ObjectCaffeineObjectStore(long expireIn, TimeUnit timeUnit, long maxCacheSize, CacheLoader<Object, Object> cacheLoader) {
        super(expireIn, timeUnit, maxCacheSize, cacheLoader);
    }
}
