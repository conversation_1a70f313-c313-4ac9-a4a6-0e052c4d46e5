package com.wftk.cache.spring.boot.autoconfigure;

import com.wftk.cache.spring.boot.autoconfigure.properties.CacheProperties;
import com.wftk.common.core.cache.ObjectCachingObjectManager;
import com.wftk.common.core.cache.StringCachingObjectManager;
import com.wftk.common.core.store.ObjectStore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2023/8/30 15:52
 */
@Configuration
@EnableConfigurationProperties(CacheProperties.class)
public class CacheAutoConfiguration {

    @ConditionalOnMissingBean
    @Bean
    ObjectCachingObjectManager objectCachingObjectManager(ObjectStore<Object, Object> objectStore) {
        return new ObjectCachingObjectManager(objectStore);
    }

    @ConditionalOnMissingBean
    @Bean
    StringCachingObjectManager stringCachingObjectManager(ObjectStore<String, String> objectStore) {
        return new StringCachingObjectManager(objectStore);
    }

}
