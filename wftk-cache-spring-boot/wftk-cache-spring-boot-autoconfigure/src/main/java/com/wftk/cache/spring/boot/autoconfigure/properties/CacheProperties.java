package com.wftk.cache.spring.boot.autoconfigure.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @create 2023/8/31 10:49
 */
@ConfigurationProperties("config.cache")
public class CacheProperties {

    /**
     * redisson配置
     */
    private RedissonProperties redisson = new RedissonProperties();

    /**
     * caffeine配置
     */
    private CaffeineProperties caffeine = new CaffeineProperties();

    public RedissonProperties getRedisson() {
        return redisson;
    }

    public void setRedisson(RedissonProperties redisson) {
        this.redisson = redisson;
    }

    public CaffeineProperties getCaffeine() {
        return caffeine;
    }

    public void setCaffeine(CaffeineProperties caffeine) {
        this.caffeine = caffeine;
    }





    /**
     * redisson配置
     */
    public static class RedissonProperties {
        private Boolean enable = true;

        public Boolean getEnable() {
            return enable;
        }

        public void setEnable(Boolean enable) {
            this.enable = enable;
        }
    }



    /**
     * caffeine配置
     */
    public static class CaffeineProperties {

        /**
         * 是否开启
         */
        private Boolean enable = true;

        /**
         * 过期时间（毫秒）
         */
        private Long expireInTimeMills = 60 * 60 * 1000L;

        /**
         * 最大缓存数量
         */
        private Long maxCacheSize = 50000L;


        public Boolean getEnable() {
            return enable;
        }

        public void setEnable(Boolean enable) {
            this.enable = enable;
        }

        public Long getExpireInTimeMills() {
            return expireInTimeMills;
        }

        public void setExpireInTimeMills(Long expireInTimeMills) {
            this.expireInTimeMills = expireInTimeMills;
        }

        public Long getMaxCacheSize() {
            return maxCacheSize;
        }

        public void setMaxCacheSize(Long maxCacheSize) {
            this.maxCacheSize = maxCacheSize;
        }
    }
}
