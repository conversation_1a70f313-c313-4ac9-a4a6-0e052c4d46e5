package com.wftk.cache.spring.boot.autoconfigure;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.wftk.cache.spring.boot.autoconfigure.properties.CacheProperties;
import com.wftk.cache.spring.boot.autoconfigure.store.caffeine.CaffeineObjectStore;
import com.wftk.cache.spring.boot.autoconfigure.store.caffeine.ObjectCaffeineObjectStore;
import com.wftk.cache.spring.boot.autoconfigure.store.caffeine.StringCaffeineObjectStore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2023/8/31 10:03
 */
@ConditionalOnProperty(name = "config.cache.caffeine.enable", havingValue = "true", matchIfMissing = true)
@ConditionalOnClass(Caffeine.class)
@Configuration
public class CaffeineObjectStoreAutoConfiguration {

    @ConditionalOnMissingBean
    @Bean
    ObjectCaffeineObjectStore objectCaffeineObjectStore(CacheProperties properties) {
        return new ObjectCaffeineObjectStore(properties.getCaffeine().getExpireInTimeMills(), TimeUnit.MILLISECONDS, properties.getCaffeine().getMaxCacheSize());
    }

    @ConditionalOnMissingBean
    @Bean
    StringCaffeineObjectStore stringCaffeineObjectStore(CacheProperties properties) {
        return new StringCaffeineObjectStore(properties.getCaffeine().getExpireInTimeMills(), TimeUnit.MILLISECONDS, properties.getCaffeine().getMaxCacheSize());
    }
}
