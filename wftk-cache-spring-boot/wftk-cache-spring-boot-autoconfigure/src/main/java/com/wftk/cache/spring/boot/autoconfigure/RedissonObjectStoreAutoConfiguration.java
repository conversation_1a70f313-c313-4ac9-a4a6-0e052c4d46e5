package com.wftk.cache.spring.boot.autoconfigure;

import com.wftk.cache.spring.boot.autoconfigure.store.redisson.ObjectRedissonObjectStore;
import com.wftk.cache.spring.boot.autoconfigure.store.redisson.RedissonObjectStore;
import com.wftk.cache.spring.boot.autoconfigure.store.redisson.StringRedissonObjectStore;
import org.redisson.api.RedissonClient;
import org.redisson.spring.starter.RedissonAutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2023/8/30 17:53
 */
@Configuration
@ConditionalOnProperty(name = "config.cache.redisson.enable", havingValue = "true", matchIfMissing = true)
@ConditionalOnClass(RedissonClient.class)
@AutoConfigureAfter(RedissonAutoConfiguration.class)
public class RedissonObjectStoreAutoConfiguration {


    @ConditionalOnMissingBean
    @Bean
    ObjectRedissonObjectStore objectRedissonObjectStore(RedissonClient redissonClient) {
        return new ObjectRedissonObjectStore(redissonClient);
    }

    @ConditionalOnMissingBean
    @Bean
    StringRedissonObjectStore stringRedissonObjectStore(RedissonClient redissonClient) {
        return new StringRedissonObjectStore(redissonClient);
    }

}
