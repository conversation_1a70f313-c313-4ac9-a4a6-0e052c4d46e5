package com.wftk.cache.spring.boot.autoconfigure.store.caffeine;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.wftk.common.core.store.ExpirationObjectStore;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2023/5/4 14:47
 */
public class CaffeineObjectStore<K, V> implements ExpirationObjectStore<K, V> {

    private final Cache<K, V> cache;

    public CaffeineObjectStore(long expireIn, TimeUnit timeUnit, long maxCacheSize) {
        this(expireIn, timeUnit, maxCacheSize, null);
    }

    public CaffeineObjectStore(long expireIn, TimeUnit timeUnit, long maxCacheSize, CacheLoader<K, V> cacheLoader) {
        this.cache = init(expireIn, timeUnit, maxCacheSize, cacheLoader);
    }

    private Cache<K, V> init(long expireIn, TimeUnit timeUnit, long maxCacheSize, CacheLoader<K, V> cacheLoader) {
        if (cacheLoader == null) {
            return Caffeine.newBuilder()
                    .expireAfterWrite(expireIn, timeUnit)
                    .maximumSize(maxCacheSize)
                    .build();
        } else {
            return Caffeine.newBuilder()
                .expireAfterWrite(expireIn, timeUnit)
                .maximumSize(maxCacheSize)
                .build(cacheLoader);
        }
    }


    @Override
    public boolean save(K k, V v, Long expiredInSeconds) {
        throw new UnsupportedOperationException();
    }

    @Override
    public boolean save(K k, V v) {
        cache.put(k, v);
        return true;
    }

    @Override
    public V getObject(K k) {
        return cache.getIfPresent(k);
    }

    @Override
    public Map<K, V> getAll() {
        return cache.asMap();
    }

    @Override
    public boolean remove(K k) {
        cache.invalidate(k);
        return true;
    }

    @Override
    public boolean clear() {
        cache.invalidateAll();
        return true;
    }
}
