package com.wftk.cache.spring.boot.autoconfigure.store.redisson;

import com.wftk.common.core.store.ExpirationObjectStore;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/5/4 17:49
 */
public class RedissonObjectStore<K, V> implements ExpirationObjectStore<K, V> {

    private final RedissonClient redissonClient;

    public RedissonObjectStore(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    @Override
    public boolean save(K k, V v, Long expiredInSeconds) {
        RBucket<V> bucket = getBucket(k);
        Duration duration = Duration.of(expiredInSeconds, ChronoUnit.SECONDS);
        bucket.set(v, duration);
        return true;
    }

    @Override
    public boolean save(K k, V v) {
        RBucket<V> bucket = getBucket(k);
        bucket.set(v);
        return true;
    }

    @Override
    public V getObject(K k) {
        RBucket<V> bucket = getBucket(k);
        return bucket.get();
    }

    @Override
    public Map<K, V> getAll() {
        throw new UnsupportedOperationException();
    }

    @Override
    public boolean remove(K k) {
        RBucket<V> bucket = getBucket(k);
        return bucket.delete();
    }

    @Override
    public boolean clear() {
        throw new UnsupportedOperationException();
    }

    @Override
    public Long ttl(K k) {
        return getBucket(k).remainTimeToLive();
    }

    private RBucket<V> getBucket(K k) {
        return redissonClient.getBucket(k.toString());
    }
}
