package com.wftk.opt.log.spring.boot.autoconfigure.builder;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ClassUtil;
import com.wftk.jackson.core.JSONObject;
import com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.log.SysOptLog;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2023/5/6 13:38
 */
public abstract class MethodOptLogBuilder<T extends SysOptLog> extends BaseOptLogBuilder<T> {

    private final JoinPoint joinPoint;
    private final MethodSignature methodSignature;

    private final UserInfoHolder userInfoHolder;

    protected MethodOptLogBuilder(JoinPoint joinPoint, UserInfoHolder userInfoHolder) {
        this.joinPoint = joinPoint;
        this.methodSignature = (MethodSignature) joinPoint.getSignature();
        this.userInfoHolder = userInfoHolder;
        initial();
    }


    /**
     * 初始化
     */
    protected void initial() {
        Method method = methodSignature.getMethod();
        OptLog optLog = method.getAnnotation(OptLog.class);
        this.optType = optLog.optType();
        this.module = optLog.module();
        this.description = optLog.description();

        initialRequestParams();
        initialUserInfo();
    }

    /**
     * 初始化请求参数
     */
    protected void initialRequestParams() {
        String[] parameterNames = methodSignature.getParameterNames();
        if (parameterNames == null || parameterNames.length < 1) {
            return;
        }
        Object[] args = joinPoint.getArgs();
        if (args == null || args.length < 1 || args.length != parameterNames.length) {
            return;
        }
        Map<String, Object> paramsMap = new HashMap<>();
        for (int i = 0; i < parameterNames.length; i++) {
            if (args[i] instanceof HttpServletRequest) {
                continue;
            }
            if (args[i] instanceof HttpServletResponse) {
                continue;
            }
            if (args[i] instanceof MultipartFile) {
                continue;
            }
            if (hasTypeOfFiled(args[i], MultipartFile.class)) {
                continue;
            }
            paramsMap.put(parameterNames[i], args[i]);
        }
        this.params = JSONObject.getInstance().toJSONString(paramsMap);
    }


    /**
     * 判断某个对象是否包含指定类型的字段
     * @param obj
     * @param clazz
     * @return
     */
    private boolean hasTypeOfFiled(Object obj, Class<?> clazz) {
        if (obj == null) {
            return false;
        }
        if (ClassUtil.isJdkClass(obj.getClass())) {
            return false;
        }
        return BeanUtil.beanToMap(obj).values().stream()
                .filter(Objects::nonNull)
                .anyMatch(it -> it.getClass().isAssignableFrom(clazz));
    }



    /**
     * 初始化用户信息（用户ID，用户名）
     */
    protected void initialUserInfo() {
        if (userInfoHolder == null) {
            return;
        }
        this.userId = userInfoHolder.getUserId();
        this.userName = userInfoHolder.getUserName();
        this.userType = userInfoHolder.getUserType();
    }


    /**
     * 用户信息(获取当前用户信息)
     */
    public interface UserInfoHolder {

        /**
         * 获取用户ID
         * @return
         */
        String getUserId();


        /**
         * 获取用户名
         * @return
         */
        String getUserName();

        /**
         * 获取用户类型
         * @return
         */
        Integer getUserType();

    }

}
