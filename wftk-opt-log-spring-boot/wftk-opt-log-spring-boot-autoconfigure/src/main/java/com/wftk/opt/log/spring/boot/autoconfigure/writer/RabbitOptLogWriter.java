package com.wftk.opt.log.spring.boot.autoconfigure.writer;

import com.wftk.opt.log.spring.boot.autoconfigure.log.SysOptLog;
import com.wftk.rabbitmq.spring.boot.autoconfigure.consumer.DynamicMessageListener;
import com.wftk.rabbitmq.spring.boot.autoconfigure.consumer.SimpleDynamicMessageListener;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.DirectMessageMetadata;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.MessageMetadata;
import com.wftk.rabbitmq.spring.boot.autoconfigure.metadata.resolver.MessageMetadataResolver;
import com.wftk.rabbitmq.spring.boot.autoconfigure.producer.MessageDelivery;

/**
 * <AUTHOR>
 * @create 2023/5/6 15:16
 */
public class RabbitOptLogWriter<T extends SysOptLog> implements OptLogWriter<T> {

    private final MessageDelivery messageDelivery;
    private final OptLogMessageMetadata optLogMessageMetadata;

    public RabbitOptLogWriter(MessageDelivery messageDelivery, OptLogMessageMetadata optLogMessageMetadata) {
        this.messageDelivery = messageDelivery;
        this.optLogMessageMetadata = optLogMessageMetadata;
    }

    @Override
    public void write(T log) {
        messageDelivery.deliver(optLogMessageMetadata, log);
    }


    /**
     * 操作日志MQ定义
     */
    public abstract static class OptLogMessageMetadata extends DirectMessageMetadata {

    }


    /**
     * 操作日志监听器
     */
    public abstract static class OptLogListener<T extends SysOptLog> extends SimpleDynamicMessageListener<T> {

        public OptLogListener(MessageMetadata messageMetadata, MessageMetadataResolver messageMetadataResolver) {
            super(messageMetadata, messageMetadataResolver);
        }
    }
}
