package com.wftk.opt.log.spring.boot.autoconfigure.factory;

import com.wftk.opt.log.spring.boot.autoconfigure.builder.OptLogBuilder;
import com.wftk.opt.log.spring.boot.autoconfigure.log.SysOptLog;
import org.aspectj.lang.JoinPoint;

/**
 * <AUTHOR>
 * @create 2023/5/6 11:11
 */
public interface OptLogBuilderFactory<B extends OptLogBuilder<? extends SysOptLog>> {

    /**
     * 获取操作日志构造器
     * @param joinPoint
     * @return
     */
    B get(JoinPoint joinPoint);
}
