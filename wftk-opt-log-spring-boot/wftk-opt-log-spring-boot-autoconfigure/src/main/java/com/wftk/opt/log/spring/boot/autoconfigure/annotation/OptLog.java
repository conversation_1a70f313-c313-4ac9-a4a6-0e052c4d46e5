package com.wftk.opt.log.spring.boot.autoconfigure.annotation;

import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @create 2023/5/6 10:39
 */
@Target({ElementType.METHOD, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OptLog {

    /**
     * 模块
     * @return
     */
    String module() default "";


    /**
     * 操作类型
     * @return
     */
    OptType optType();


    /**
     * 描述
     * @return
     */
    String description() default "";
}
