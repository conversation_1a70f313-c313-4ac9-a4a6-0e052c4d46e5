package com.wftk.opt.log.spring.boot.autoconfigure.builder;

import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.RequestMethod;
import com.wftk.opt.log.spring.boot.autoconfigure.log.SysOptLog;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @create 2023/5/6 11:16
 */
public abstract class BaseOptLogBuilder<T extends SysOptLog> implements OptLogBuilder<T> {

    /**
     * 用户ID
     */
    protected String userId;

    /**
     * 用户名
     */
    protected String userName;


    /**
     * 用户类型
     */
    protected Integer userType;


    /**
     * 模块
     */
    protected String module;

    /**
     * 操作类型
     */
    protected OptType optType;


    /**
     * 请求参数
     */
    protected String params;

    /**
     * 描述
     */
    protected String description;

    /**
     * 操作时间
     */
    protected LocalDateTime optTime = LocalDateTime.now();


    @Override
    public OptLogBuilder<T> userId(String userId) {
        this.userId = userId;
        return this;
    }

    @Override
    public OptLogBuilder<T> userName(String userName) {
        this.userName = userName;
        return this;
    }


    @Override
    public OptLogBuilder<T> module(String module) {
        this.module = module;
        return this;
    }

    @Override
    public OptLogBuilder<T> optType(OptType optType) {
        this.optType = optType;
        return this;
    }


    @Override
    public OptLogBuilder<T> params(String params) {
        this.params = params;
        return this;
    }

    @Override
    public OptLogBuilder<T> description(String description) {
        this.description = description;
        return this;
    }

    @Override
    public OptLogBuilder<T> optTime(LocalDateTime optTime) {
        if (optTime == null) {
            return this;
        }
        this.optTime = optTime;
        return this;
    }

    @Override
    public OptLogBuilder<T> userType(Integer userType) {
        this.userType = userType;
        return this;
    }

    @Override
    public T build() {
        //校验
        doValidate();
        T log = doCreate();
        log.setUserId(userId);
        log.setUserName(userName);
        log.setUserType(userType);
        log.setModule(module);
        log.setDescription(description);
        log.setOptTime(optTime);
        log.setParams(params);
        log.setOptType(optType);
        return log;
    }


    /**
     * 校验
     */
    protected void doValidate() {
        checkNotNull("userId", userId);
        checkNotNull("userName", userName);
        checkNotNull("userType", userType);
        checkNotNull("module", module);
    }

    protected abstract T doCreate();


    /**
     *
     * @param fieldName
     * @param obj
     */
    private void checkNotNull(String fieldName, Object obj) {
        if (obj == null) {
            throw new IllegalArgumentException("field [" + fieldName + "] must not be null.");
        }
        if (obj instanceof String s && !StringUtils.hasText(s)) {
            throw new IllegalArgumentException("field [" + fieldName + "] must not be null.");
        }
    }
}
