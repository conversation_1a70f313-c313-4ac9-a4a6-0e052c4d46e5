package com.wftk.opt.log.spring.boot.autoconfigure;

import com.wftk.opt.log.spring.boot.autoconfigure.aspect.OptLogAspect;
import com.wftk.opt.log.spring.boot.autoconfigure.builder.MethodOptLogBuilder;
import com.wftk.opt.log.spring.boot.autoconfigure.builder.OptLogBuilder;
import com.wftk.opt.log.spring.boot.autoconfigure.factory.DefaultOptLogBuilderFactory;
import com.wftk.opt.log.spring.boot.autoconfigure.factory.OptLogBuilderFactory;
import com.wftk.opt.log.spring.boot.autoconfigure.log.SysOptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.writer.OptLogWriter;
import com.wftk.opt.log.spring.boot.autoconfigure.writer.RabbitOptLogWriter;
import com.wftk.rabbitmq.spring.boot.autoconfigure.producer.MessageDelivery;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * <AUTHOR>
 * @create 2023/5/6 15:46
 */
@EnableAspectJAutoProxy
@Configuration
public class OptLogConfiguration {

    @Configuration
    @ConditionalOnClass(MessageDelivery.class)
    static class OptLogWriterConfiguration {

        @ConditionalOnMissingBean(OptLogWriter.class)
        @Bean
        <T extends SysOptLog> OptLogWriter<T> optLogWriter(MessageDelivery messageDelivery, RabbitOptLogWriter.OptLogMessageMetadata optLogMessageMetadata) {
            return new RabbitOptLogWriter<>(messageDelivery, optLogMessageMetadata);
        }
    }


    @ConditionalOnMissingBean(OptLogBuilderFactory.class)
    @Bean
    <T extends SysOptLog> OptLogBuilderFactory<? extends OptLogBuilder<T>> optLogBuilderFactory(MethodOptLogBuilder.UserInfoHolder userInfoHolder) {
        return (OptLogBuilderFactory<? extends OptLogBuilder<T>>) new DefaultOptLogBuilderFactory(userInfoHolder);
    }

    @Bean
    <T extends SysOptLog> OptLogAspect<T> optLogAspect(OptLogBuilderFactory<? extends OptLogBuilder<T>> optLogBuilderFactory, OptLogWriter<T> optLogWriter) {
        return new OptLogAspect<>(optLogBuilderFactory, optLogWriter);
    }
}
