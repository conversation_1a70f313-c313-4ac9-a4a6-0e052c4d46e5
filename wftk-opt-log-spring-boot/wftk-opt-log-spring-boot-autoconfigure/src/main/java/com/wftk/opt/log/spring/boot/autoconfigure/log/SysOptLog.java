package com.wftk.opt.log.spring.boot.autoconfigure.log;

import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.RequestMethod;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 操作日志
 * <AUTHOR>
 * @create 2023/5/6 10:14
 */
public interface SysOptLog extends Serializable {

    /**
     * 用户ID
     * @return
     */
    String getUserId();

    void setUserId(String userId);

    /**
     * 获取用户名
     * @return
     */
    String getUserName();

    void setUserName(String userName);

    /**
     * 获取用户类型
     * @return
     */
    Integer getUserType();

    void setUserType(Integer userType);

    /**
     * 获取IP
     * @return
     */
    String getIp();

    void setIp(String ip);


    /**
     * 获取模块
     * @return
     */
    String getModule();

    void setModule(String module);


    /**
     * 获取操作类型
     * @return
     */
    OptType getOptType();

    void setOptType(OptType optType);

    /**
     * 获取请求类型
     * @return
     */
    RequestMethod getRequestMethod();

    void setRequestMethod(RequestMethod requestMethod);


    /**
     * 获取请求参数（除非基本数据类型及其包装类和String外，均为json字符串）
     * @return
     */
    String getParams();

    void setParams(String params);


    /**
     * 获取URI
     * @return
     */
    String getUri();

    void setUri(String uri);


    /**
     * 获取描述信息
     * @return
     */
    String getDescription();

    void setDescription(String description);


    /**
     * 获取操作时间
     * @return
     */
    LocalDateTime getOptTime();

    void setOptTime(LocalDateTime optTime);


    /**
     * 设置请求头
     * @param headers
     */
    void setHeaders(Map<String, String> headers);

    /**
     * 获取请求头
     * @return
     */
    Map<String, String> getHeaders();
}
