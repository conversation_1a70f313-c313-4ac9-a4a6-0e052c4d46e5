/*
 * @Author: dy
 * @Date: 2024-09-30 11:32:47
 * @LastEditors: dy
 * @LastEditTime: 2025-06-12 17:03:34
 * @Description: 
 */
package com.wftk.opt.log.spring.boot.autoconfigure.aware;

import cn.hutool.core.util.StrUtil;

import com.wftk.common.core.util.IPUtil;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.RequestMethod;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/8/21 21:08
 */
public interface HttpServletAware {


    /**
     * 获取请求
     * @return
     */
    default HttpServletRequest getHttpServletRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
    }

    /**
     * 获取IP地址
     * @return
     */
    default String getIp() {
        return IPUtil.getRequestIp(getHttpServletRequest());
    }


    /**
     * 获取URI
     * @return
     */
    default String getUri() {
        return getHttpServletRequest().getRequestURI();
    }


    /**
     * 获取请求方式
     * @return
     */
    default RequestMethod getRequestMethod() {
        return RequestMethod.valueOf(getHttpServletRequest().getMethod().toUpperCase());
    }


    /**
     * 获取请求头
     * @return
     */
    default Map<String, String> getHeaders() {
        HttpServletRequest request = getHttpServletRequest();
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            if (StrUtil.isNotBlank(headerValue)) {
                headers.put(headerName.toLowerCase(), headerValue);
            }
        }
        return headers;
    }

}
