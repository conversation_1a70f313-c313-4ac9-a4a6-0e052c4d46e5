package com.wftk.opt.log.spring.boot.autoconfigure.factory;

import com.wftk.opt.log.spring.boot.autoconfigure.builder.DefaultOptBuilder;
import com.wftk.opt.log.spring.boot.autoconfigure.builder.MethodOptLogBuilder;
import org.aspectj.lang.JoinPoint;

/**
 * <AUTHOR>
 * @create 2023/5/6 11:56
 */
public class DefaultOptLogBuilderFactory implements OptLogBuilderFactory<DefaultOptBuilder> {

    private final MethodOptLogBuilder.UserInfoHolder userInfoHolder;

    public DefaultOptLogBuilderFactory(MethodOptLogBuilder.UserInfoHolder userInfoHolder) {
        this.userInfoHolder = userInfoHolder;
    }

    @Override
    public DefaultOptBuilder get(JoinPoint joinPoint) {
        return new DefaultOptBuilder(joinPoint, userInfoHolder);
    }
}
