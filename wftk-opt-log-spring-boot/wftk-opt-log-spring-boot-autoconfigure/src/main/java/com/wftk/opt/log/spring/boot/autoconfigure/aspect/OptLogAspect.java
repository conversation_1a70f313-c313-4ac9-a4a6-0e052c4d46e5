package com.wftk.opt.log.spring.boot.autoconfigure.aspect;

import com.wftk.opt.log.spring.boot.autoconfigure.builder.OptLogBuilder;
import com.wftk.opt.log.spring.boot.autoconfigure.factory.OptLogBuilderFactory;
import com.wftk.opt.log.spring.boot.autoconfigure.log.SysOptLog;
import com.wftk.opt.log.spring.boot.autoconfigure.writer.OptLogWriter;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @create 2023/5/6 14:47
 */
@Aspect
public class OptLogAspect<T extends SysOptLog> {

    private static final Logger LOGGER = LoggerFactory.getLogger(OptLogAspect.class);

    private final OptLogBuilderFactory<? extends OptLogBuilder<T>> optLogBuilderFactory;
    private final OptLogWriter<T> optLogWriter;

    public OptLogAspect(OptLogBuilderFactory<? extends OptLogBuilder<T>> optLogBuilderFactory, OptLogWriter<T> optLogWriter) {
        this.optLogBuilderFactory = optLogBuilderFactory;
        this.optLogWriter = optLogWriter;
    }

    @Pointcut("@annotation(com.wftk.opt.log.spring.boot.autoconfigure.annotation.OptLog)")
    public void optLogPointCut() {

    }

    @Before(value = "optLogPointCut()")
    public void handleOptLog(JoinPoint joinPoint) {
        try {
            OptLogBuilder<T> optLogBuilder = optLogBuilderFactory.get(joinPoint);
            T sysOptLog = optLogBuilder.build();
            if (optLogWriter != null) {
                optLogWriter.write(sysOptLog);
            }
        } catch (Exception e) {
            LOGGER.error("write opt log error.", e);
        }
    }
}
