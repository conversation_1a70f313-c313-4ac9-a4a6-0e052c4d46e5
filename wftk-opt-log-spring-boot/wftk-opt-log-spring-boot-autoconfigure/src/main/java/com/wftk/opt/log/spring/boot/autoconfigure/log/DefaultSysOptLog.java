package com.wftk.opt.log.spring.boot.autoconfigure.log;

import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.RequestMethod;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/5/6 10:51
 */
public class DefaultSysOptLog implements SysOptLog {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户类型
     */
    private Integer userType;

    /**
     * IP
     */
    private String ip;

    /**
     * 模块
     */
    private String module;

    /**
     * 操作类型
     */
    private OptType optType;

    /**
     * HTTP请求方法
     */
    private RequestMethod requestMethod;

    /**
     * 请求参数
     */
    private String params;

    /**
     * 请求头
     */
    private Map<String, String> headers;

    /**
     * 请求URI
     */
    private String uri;


    /**
     * 描述
     */
    private String description;


    /**
     * 操作时间
     */
    private LocalDateTime optTime;


    @Override
    public String getUserId() {
        return userId;
    }

    @Override
    public String getUserName() {
        return userName;
    }

    @Override
    public Integer getUserType() {
        return userType;
    }

    @Override
    public String getIp() {
        return ip;
    }

    @Override
    public String getModule() {
        return module;
    }

    @Override
    public OptType getOptType() {
        return optType;
    }

    @Override
    public void setOptType(OptType optType) {
        this.optType = optType;
    }

    @Override
    public RequestMethod getRequestMethod() {
        return requestMethod;
    }

    @Override
    public String getParams() {
        return params;
    }

    @Override
    public String getUri() {
        return uri;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public LocalDateTime getOptTime() {
        return optTime;
    }

    @Override
    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Override
    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Override
    public void setIp(String ip) {
        this.ip = ip;
    }

    @Override
    public void setModule(String module) {
        this.module = module;
    }

    @Override
    public void setRequestMethod(RequestMethod requestMethod) {
        this.requestMethod = requestMethod;
    }

    @Override
    public void setParams(String params) {
        this.params = params;
    }

    @Override
    public void setUri(String uri) {
        this.uri = uri;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public void setOptTime(LocalDateTime optTime) {
        this.optTime = optTime;
    }

    @Override
    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }

    @Override
    public Map<String, String> getHeaders() {
        return headers;
    }

    @Override
    public void setUserType(Integer userType) {
        this.userType = userType;
    }
}
