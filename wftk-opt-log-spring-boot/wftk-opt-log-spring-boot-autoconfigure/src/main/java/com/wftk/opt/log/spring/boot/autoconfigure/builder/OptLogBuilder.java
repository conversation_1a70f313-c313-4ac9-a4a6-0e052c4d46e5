package com.wftk.opt.log.spring.boot.autoconfigure.builder;

import com.wftk.opt.log.spring.boot.autoconfigure.enums.OptType;
import com.wftk.opt.log.spring.boot.autoconfigure.enums.RequestMethod;
import com.wftk.opt.log.spring.boot.autoconfigure.log.SysOptLog;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @create 2023/5/6 11:10
 */
public interface OptLogBuilder<T extends SysOptLog> {

    /**
     * 用户ID
     * @param userId
     * @return
     */
    OptLogBuilder<T> userId(String userId);

    /**
     * 用户名
     * @param userName
     * @return
     */
    OptLogBuilder<T> userName(String userName);

    /**
     * 用户类型
     * @param userType
     * @return
     */
    OptLogBuilder<T> userType(Integer userType);



    /**
     * 模块
     * @param module
     * @return
     */
    OptLogBuilder<T> module(String module);


    /**
     * 操作类型
     * @param optType
     * @return
     */
    OptLogBuilder<T> optType(OptType optType);


    /**
     * 请求参数（除非基本数据类型及其包装类和String外，均为json字符串）
     * @param params
     * @return
     */
    OptLogBuilder<T> params(String params);


    /**
     * 描述信息
     * @param description
     * @return
     */
    OptLogBuilder<T> description(String description);


    /**
     * 操作时间
     * @param optTime
     * @return
     */
    OptLogBuilder<T> optTime(LocalDateTime optTime);


    /**
     * 构造操作日志
     * @return
     */
    T build();
}
