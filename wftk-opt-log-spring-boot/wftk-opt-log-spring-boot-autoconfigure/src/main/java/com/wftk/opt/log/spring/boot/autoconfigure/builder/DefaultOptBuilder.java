package com.wftk.opt.log.spring.boot.autoconfigure.builder;

import com.wftk.opt.log.spring.boot.autoconfigure.aware.HttpServletAware;
import com.wftk.opt.log.spring.boot.autoconfigure.log.DefaultSysOptLog;
import org.aspectj.lang.JoinPoint;

/**
 * <AUTHOR>
 * @create 2023/5/6 11:27
 */
public class DefaultOptBuilder extends MethodOptLogBuilder<DefaultSysOptLog> implements HttpServletAware {

    public DefaultOptBuilder(JoinPoint joinPoint, UserInfoHolder userInfoHolder) {
        super(joinPoint, userInfoHolder);
    }

    @Override
    protected DefaultSysOptLog doCreate() {
        DefaultSysOptLog sysOptLog = new DefaultSysOptLog();
        sysOptLog.setHeaders(getHeaders());
        sysOptLog.setUri(getUri());
        sysOptLog.setRequestMethod(getRequestMethod());
        sysOptLog.setIp(getIp());
        return sysOptLog;
    }
}
