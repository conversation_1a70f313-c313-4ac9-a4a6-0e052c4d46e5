package com.wftk.exception.ext.translators;


import com.wftk.exception.common.GlobalErrorConstants;
import com.wftk.exception.common.HttpStatusCode;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.exception.BaseBusinessException;
import com.wftk.exception.core.translator.base.AbstractExceptionTranslator;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 16:24
 */
public class UnsupportedExceptionTranslator extends AbstractExceptionTranslator {

    @Override
    protected ResolvedResult doTranslate(Throwable throwable) {
        if (throwable instanceof BaseBusinessException baseBusinessException) {
            return ResolvedResultBuilder.build(baseBusinessException.getCode(), baseBusinessException.getMessage(), HttpStatusCode.INTERNAL_SERVER_ERROR);
        }
        return ResolvedResultBuilder.build(GlobalErrorConstants.INTERNAL_SERVER_ERROR.code(), GlobalErrorConstants.INTERNAL_SERVER_ERROR.message(), HttpStatusCode.INTERNAL_SERVER_ERROR);
    }

    @Override
    public int getOrder() {
        return LOWEST_PRECEDENCE;
    }

    @Override
    public boolean support(Throwable throwable) {
        return true;
    }
}
