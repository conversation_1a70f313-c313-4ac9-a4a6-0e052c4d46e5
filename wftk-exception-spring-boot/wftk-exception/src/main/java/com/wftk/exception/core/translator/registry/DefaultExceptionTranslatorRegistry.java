package com.wftk.exception.core.translator.registry;

import com.wftk.exception.core.translator.base.ExceptionTranslator;
import com.wftk.exception.order.OrderComparator;

import java.io.Serial;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022/7/1 16:52
 */
public class DefaultExceptionTranslatorRegistry implements ExceptionTranslatorRegistry {

    private final List<ExceptionTranslator> translators;
    private final LRUCache cache;
    private final Comparator<ExceptionTranslator> comparator;

    public DefaultExceptionTranslatorRegistry(int capacity) {
        this(new CopyOnWriteArrayList<>(), new LRUCache(capacity), new OrderComparator());
    }

    protected DefaultExceptionTranslatorRegistry(List<ExceptionTranslator> translators, LRUCache lruCache,
                                                 Comparator<ExceptionTranslator> comparator) {
        this.translators = translators;
        this.cache = lruCache;
        this.comparator = comparator;
    }

    @Override
    public boolean register(ExceptionTranslator exceptionTranslator) {
        translators.add(exceptionTranslator);
        //根据order排序
        translators.sort(comparator);
        return true;
    }

    @Override
    public boolean register(Collection<ExceptionTranslator> exceptionTranslators) {
        translators.addAll(exceptionTranslators);
        translators.sort(comparator);
        return true;
    }

    @Override
    public Optional<ExceptionTranslator> get(Throwable throwable) {
        String cacheKey = getCacheKey(throwable);

        //先找缓存
        List<ExceptionTranslator> exceptionTranslators = this.cache.get(cacheKey);
        if (exceptionTranslators != null && !exceptionTranslators.isEmpty()) {
            return Optional.of(exceptionTranslators.get(0));
        }
        //获取适配处理器
        List<ExceptionTranslator> matchedTranslators = getAll().stream()
                .filter(it -> it.support(throwable))
                .sorted(comparator)
                .collect(Collectors.toList());
        //构建缓存
        this.cache.put(cacheKey, matchedTranslators);

        if (!matchedTranslators.isEmpty()) {
            return Optional.of(matchedTranslators.get(0));
        }
        return Optional.empty();
    }

    @Override
    public Collection<ExceptionTranslator> getAllByThrowable(Throwable throwable) {
        return this.cache.get(getCacheKey(throwable));
    }

    @Override
    public Collection<ExceptionTranslator> getAll() {
        return translators;
    }

    /**
     * 获取缓存key
     * @param throwable
     * @return
     */
    private String getCacheKey(Throwable throwable) {
        return throwable.getClass().getName();
    }



    /**
     * 基于LinkedHashMap实现LRU算法
     */
    static class LRUCache extends LinkedHashMap<String, List<ExceptionTranslator>> {

        @Serial
        private static final long serialVersionUID = -526443537518372233L;

        /** 容量，超过此容量自动删除末尾元素 */
        private final int capacity;

        /**
         * @param capacity
         */
        public LRUCache(int capacity) {
            super(capacity + 1, 1.0f, true);
            this.capacity = capacity;
        }

        @Override
        protected boolean removeEldestEntry(Map.Entry<String, List<ExceptionTranslator>> eldest) {
            return size() > this.capacity;
        }

    }
}
