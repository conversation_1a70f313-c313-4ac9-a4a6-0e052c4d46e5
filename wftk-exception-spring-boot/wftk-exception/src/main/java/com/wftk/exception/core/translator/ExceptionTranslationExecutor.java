package com.wftk.exception.core.translator;

import com.wftk.exception.common.GlobalErrorConstants;
import com.wftk.exception.common.HttpStatusCode;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.translator.base.ExceptionTranslator;
import com.wftk.exception.core.translator.registry.ExceptionTranslatorRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 16:24
 */
public class ExceptionTranslationExecutor implements ExceptionTranslator {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    protected final ExceptionTranslatorRegistry exceptionTranslatorRegistry;

    public ExceptionTranslationExecutor(ExceptionTranslatorRegistry exceptionTranslatorRegistry) {
        this.exceptionTranslatorRegistry = exceptionTranslatorRegistry;
    }

    @Override
    public ResolvedResult translate(Throwable throwable) {
        logger.error("error: ", throwable);
        return exceptionTranslatorRegistry.get(throwable)
                .map(it -> it.translate(throwable))
                .orElseGet(() -> {
                    logger.warn("none exception translator matched. default result returned.");
                    return ResolvedResultBuilder.build(GlobalErrorConstants.INTERNAL_SERVER_ERROR.code(), GlobalErrorConstants.INTERNAL_SERVER_ERROR.message(), HttpStatusCode.INTERNAL_SERVER_ERROR);
                });
    }

    @Override
    public boolean support(Throwable throwable) {
        return true;
    }

    @Override
    public int getOrder() {
        return 0;
    }
}
