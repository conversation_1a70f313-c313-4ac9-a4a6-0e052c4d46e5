package com.wftk.exception.core.exception.alarm;


import com.wftk.exception.common.ErrorCode;
import com.wftk.exception.common.GlobalErrorConstants;
import com.wftk.exception.core.exception.BaseBusinessException;

import java.io.Serial;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 16:24
 */
public class AlarmBusinessException extends BaseBusinessException implements AlarmException {

    @Serial
    private static final long serialVersionUID = -1184455227190561873L;

    private final int code;

    public AlarmBusinessException() {
        this(GlobalErrorConstants.INTERNAL_SERVER_ERROR);
    }

    public AlarmBusinessException(ErrorCode errorCode) {
        this(errorCode.code(), errorCode.message());
    }

    public AlarmBusinessException(int code, String message) {
        super(message);
        this.code = code;
    }

    public AlarmBusinessException(String message) {
        super(message);
        this.code = GlobalErrorConstants.INTERNAL_SERVER_ERROR.code();
    }

    public AlarmBusinessException(Throwable throwable) {
        super(throwable);
        this.code = GlobalErrorConstants.INTERNAL_SERVER_ERROR.code();
    }

    @Override
    public int getCode() {
        return code;
    }
}
