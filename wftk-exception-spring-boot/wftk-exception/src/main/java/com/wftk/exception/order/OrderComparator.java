package com.wftk.exception.order;


import com.wftk.exception.core.translator.base.ExceptionTranslator;

import java.util.Comparator;

/**
 * <AUTHOR>
 * @create 2022/7/1 16:25
 */
public class OrderComparator implements Comparator<ExceptionTranslator> {

    @Override
    public int compare(ExceptionTranslator o1, ExceptionTranslator o2) {
        return Integer.compare(o1.getOrder(), o2.getOrder());
    }
}
