package com.wftk.exception.ext.exception;

import com.wftk.exception.common.GlobalErrorConstants;
import com.wftk.exception.core.exception.BusinessException;

import java.io.Serial;

/**
 * @Author: ying.dong
 * @Date: 2021/9/16 09:17
 */
public class UnAuthorizedException extends BusinessException {

    @Serial
    private static final long serialVersionUID = 4424635801245763198L;

    public UnAuthorizedException() {
        this(GlobalErrorConstants.UNAUTHORIZED.code(), GlobalErrorConstants.UNAUTHORIZED.message());
    }

    public UnAuthorizedException(int code, String message) {
        super(code, message);
    }

}
