package com.wftk.exception.common.result;

import com.wftk.exception.common.ErrorCode;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 16:24
 */
public class ResolvedResultBuilder {

    public static ResolvedResult build(int code, String msg, int httpStatus) {
        return new ResolvedResult(new LocaleResult(code, msg), httpStatus);
    }

    public static ResolvedResult build(ErrorCode errorCode, int httpStatus) {
        return build(errorCode.code(), errorCode.message(), httpStatus);
    }
}
