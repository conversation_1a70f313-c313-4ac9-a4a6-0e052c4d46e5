package com.wftk.exception.ext.translators;

import com.wftk.exception.common.HttpStatusCode;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.translator.base.AbstractExceptionTranslator;
import com.wftk.exception.ext.exception.UnAuthorizedException;

/**
 * @Author: ying.dong
 * @Date: 2021/9/16 09:18
 */
public class UnAuthorizedExceptionTranslator extends AbstractExceptionTranslator {

    @Override
    protected ResolvedResult doTranslate(Throwable throwable) {
        UnAuthorizedException unAuthorizedException = (UnAuthorizedException) throwable;
        return ResolvedResultBuilder.build(unAuthorizedException.getCode(), unAuthorizedException.getMessage(), HttpStatusCode.UNAUTHORIZED);
    }

    @Override
    public boolean support(Throwable throwable) {
        return throwable instanceof UnAuthorizedException;
    }
}
