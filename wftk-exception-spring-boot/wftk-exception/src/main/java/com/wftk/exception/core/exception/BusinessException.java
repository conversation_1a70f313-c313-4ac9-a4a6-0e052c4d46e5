package com.wftk.exception.core.exception;


import com.wftk.exception.common.ErrorCode;
import com.wftk.exception.common.GlobalErrorConstants;

import java.io.Serial;

/**
 * @Author: ying.dong
 * @Date: 2021/4/20 18:22
 */
public class BusinessException extends BaseBusinessException {

    @Serial
    private static final long serialVersionUID = -9040723087491516359L;
    private final int code;

    public BusinessException() {
        this(GlobalErrorConstants.INTERNAL_SERVER_ERROR);
    }

    public BusinessException(ErrorCode errorCode) {
        this(errorCode.code(), errorCode.message());
    }

    public BusinessException(int code, String message) {
        super(message);
        this.code = code;
    }

    public BusinessException(String message) {
        super(message);
        this.code = GlobalErrorConstants.INTERNAL_SERVER_ERROR.code();
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.code = GlobalErrorConstants.INTERNAL_SERVER_ERROR.code();
    }

    public BusinessException(Throwable throwable) {
        super(throwable);
        this.code = GlobalErrorConstants.INTERNAL_SERVER_ERROR.code();
    }

    @Override
    public int getCode() {
        return code;
    }
}
