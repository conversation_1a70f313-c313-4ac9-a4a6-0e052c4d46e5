package com.wftk.exception.core.exception;

import java.io.Serial;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 16:24
 */
public abstract class BaseBusinessException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = -5808211807429111010L;

    protected BaseBusinessException() {
        super();
    }

    protected BaseBusinessException(String message) {
        super(message);
    }

    protected BaseBusinessException(Throwable cause) {
        super(cause);
    }

    protected BaseBusinessException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 获取错误码
     * @return
     */
    public abstract int getCode();

}
