package com.wftk.exception.core.translator.registry;

import com.wftk.exception.core.translator.base.ExceptionTranslator;

import java.util.Collection;
import java.util.Optional;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 16:24
 */
public interface ExceptionTranslatorRegistry {

    /**
     * 注册
     * @param exceptionTranslator
     * @return
     */
    boolean register(ExceptionTranslator exceptionTranslator);

    /**
     * 注册
     * @param exceptionTranslators
     * @return
     */
    boolean register(Collection<ExceptionTranslator> exceptionTranslators);

    /**
     * 根据异常获取一个处理器（优先级最高的）
     * @param throwable
     * @return
     */
    Optional<ExceptionTranslator> get(Throwable throwable);

    /**
     * 根据异常获取所有能处理该异常的处理器
     * @param throwable
     * @return
     */
    Collection<ExceptionTranslator> getAllByThrowable(Throwable throwable);

    /**
     * 获取所有异常处理器
     * @return
     */
    Collection<ExceptionTranslator> getAll();
}
