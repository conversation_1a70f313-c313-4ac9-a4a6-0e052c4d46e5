package com.wftk.exception.ext.translators;

import com.wftk.exception.common.HttpStatusCode;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.exception.BaseBusinessException;
import com.wftk.exception.core.translator.base.AbstractExceptionTranslator;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 16:24
 */
public class CommonBusinessExceptionTranslator extends AbstractExceptionTranslator {


    @Override
    public int getOrder() {
        return LOWEST_PRECEDENCE - 10;
    }

    @Override
    protected ResolvedResult doTranslate(Throwable throwable) {
        BaseBusinessException baseBusinessException = (BaseBusinessException) throwable;
        return ResolvedResultBuilder.build(baseBusinessException.getCode(), throwable.getMessage(), HttpStatusCode.INTERNAL_SERVER_ERROR);
    }

    @Override
    public boolean support(Throwable throwable) {
        return throwable instanceof BaseBusinessException;
    }

}
