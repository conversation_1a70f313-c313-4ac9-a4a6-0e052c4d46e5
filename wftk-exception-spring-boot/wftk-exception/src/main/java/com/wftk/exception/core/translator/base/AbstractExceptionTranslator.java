package com.wftk.exception.core.translator.base;

import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.order.Ordered;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 16:24
 */
public abstract class AbstractExceptionTranslator implements ExceptionTranslator {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public ResolvedResult translate(Throwable throwable) {
        if (!support(throwable)) {
            logger.info("{} not support for {}", this.getClass().getName(), throwable.toString());
            return null;
        }
        logger.info("{} will be handled by {}.", throwable.toString(), this.getClass().getSimpleName());
        return doTranslate(throwable);
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }


    protected abstract ResolvedResult doTranslate(Throwable throwable);
}
