package com.wftk.exception.core.translator.base;


import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.order.Ordered;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 16:24
 */
public interface ExceptionTranslator extends Supportable<Throwable>, Ordered {

    /**
     * 翻译异常
     * @param throwable
     * @return
     */
    ResolvedResult translate(Throwable throwable);
}
