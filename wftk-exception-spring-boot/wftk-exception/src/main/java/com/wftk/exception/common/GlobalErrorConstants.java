package com.wftk.exception.common;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 17:37
 */
public interface GlobalErrorConstants {

    // ========== 客户端错误段 ==========

    ErrorCode BAD_REQUEST = new ErrorCode(400, "请求参数错误");
    ErrorCode UNAUTHORIZED = new ErrorCode(401, "未授权");
    ErrorCode FORBIDDEN = new ErrorCode(403, "没有该操作权限");
    ErrorCode NOT_FOUND = new ErrorCode(404, "请求未找到");
    ErrorCode METHOD_NOT_ALLOWED = new ErrorCode(405, "不支持的请求方法");
    ErrorCode MEDIA_TYPE_NOT_SUPPORTED = new ErrorCode(415, "不支持的MediaType");

    // ========== 服务端错误段 ==========

    ErrorCode INTERNAL_SERVER_ERROR = new ErrorCode(500, "服务器忙，请稍后再试");

}
