package com.wftk.exception.spring.boot.autoconfigure.translators.webmvc;

import com.wftk.exception.common.GlobalErrorConstants;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.translator.base.AbstractExceptionTranslator;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.ServletRequestBindingException;

/**
 * @Author: ying.dong
 * @Date: 2021/4/20 09:30
 */
public class ServletRequestBindingExceptionTranslator  extends AbstractExceptionTranslator {

    @Override
    protected ResolvedResult doTranslate(Throwable throwable) {
        return ResolvedResultBuilder.build(GlobalErrorConstants.BAD_REQUEST.code(), throwable.getMessage(), HttpStatus.BAD_REQUEST.value());
    }

    @Override
    public boolean support(Throwable throwable) {
        return throwable instanceof ServletRequestBindingException;
    }
}
