package com.wftk.exception.spring.boot.autoconfigure.translators;

import com.wftk.exception.common.HttpStatusCode;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.translator.base.AbstractExceptionTranslator;
import com.wftk.exception.spring.boot.autoconfigure.exception.LocaleBusinessException;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 16:24
 */
public class LocalBusinessExceptionTranslator extends AbstractExceptionTranslator {

    @Override
    protected ResolvedResult doTranslate(Throwable throwable) {
        LocaleBusinessException e = (LocaleBusinessException) throwable;
        return ResolvedResultBuilder.build(e.getLocaleCode(), e.getMessage(), HttpStatusCode.INTERNAL_SERVER_ERROR);
    }

    @Override
    public int getOrder() {
        return LOWEST_PRECEDENCE - 20;
    }

    @Override
    public boolean support(Throwable throwable) {
        return throwable instanceof LocaleBusinessException;
    }
}
