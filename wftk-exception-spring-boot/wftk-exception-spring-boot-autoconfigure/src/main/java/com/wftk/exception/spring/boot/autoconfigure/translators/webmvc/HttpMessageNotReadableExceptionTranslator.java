package com.wftk.exception.spring.boot.autoconfigure.translators.webmvc;

import com.wftk.exception.common.GlobalErrorConstants;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.translator.base.AbstractExceptionTranslator;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;

/**
 * <AUTHOR>
 * @create 2023/12/25 16:38
 */
public class HttpMessageNotReadableExceptionTranslator extends AbstractExceptionTranslator {
    @Override
    protected ResolvedResult doTranslate(Throwable throwable) {
        return ResolvedResultBuilder.build(GlobalErrorConstants.BAD_REQUEST.code(), "参数错误", HttpStatus.BAD_REQUEST.value());
    }

    @Override
    public boolean support(Throwable throwable) {
        return throwable instanceof HttpMessageNotReadableException;
    }
}
