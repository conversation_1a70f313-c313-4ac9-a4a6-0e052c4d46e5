package com.wftk.exception.spring.boot.autoconfigure.alarm.user;

/**
 * <AUTHOR>
 * @create 2024/5/27 18:45
 */
public class FeishuRobotAlarmUserInfo extends AlarmUserInfo {

    private String webhook;
    private String secret;

    public FeishuRobotAlarmUserInfo(String group, AlarmUserType type) {
        super(group, type);
    }

    public String getWebhook() {
        return webhook;
    }

    public void setWebhook(String webhook) {
        this.webhook = webhook;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }
}
