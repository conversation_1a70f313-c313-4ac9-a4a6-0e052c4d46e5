package com.wftk.exception.spring.boot.autoconfigure.config;

import com.wftk.exception.ext.translators.CommonBusinessExceptionTranslator;
import com.wftk.exception.spring.boot.autoconfigure.translators.LocalBusinessExceptionTranslator;
import com.wftk.exception.ext.translators.UnAuthorizedExceptionTranslator;
import com.wftk.exception.ext.translators.UnsupportedExceptionTranslator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2022/7/4 11:14
 */
@Configuration
public class DefaultTranslatorConfiguration {

    @ConditionalOnMissingBean
    @Bean
    UnsupportedExceptionTranslator unsupportedExceptionTranslator() {
        return new UnsupportedExceptionTranslator();
    }

    @ConditionalOnMissingBean
    @Bean
    CommonBusinessExceptionTranslator commonBusinessExceptionTranslator() {
        return new CommonBusinessExceptionTranslator();
    }

    @ConditionalOnMissingBean
    @Bean
    LocalBusinessExceptionTranslator localBusinessExceptionTranslator() {
        return new LocalBusinessExceptionTranslator();
    }


    @ConditionalOnMissingBean
    @Bean
    UnAuthorizedExceptionTranslator unAuthorizedExceptionTranslator() {
        return new UnAuthorizedExceptionTranslator();
    }

}
