package com.wftk.exception.spring.boot.autoconfigure.alarm.user;

/**
 * <AUTHOR>
 * @create 2024/5/27 10:18
 */
public abstract class AlarmUserInfo {

    /**
     * 用户分组
     */
    private String group;

    /**
     * 用户类型
     */
    private AlarmUserType type;

    public AlarmUserInfo(String group, AlarmUserType type) {
        this.group = group;
        this.type = type;
    }


    public String getGroup() {
        return group;
    }

    public AlarmUserType getType() {
        return type;
    }
}
