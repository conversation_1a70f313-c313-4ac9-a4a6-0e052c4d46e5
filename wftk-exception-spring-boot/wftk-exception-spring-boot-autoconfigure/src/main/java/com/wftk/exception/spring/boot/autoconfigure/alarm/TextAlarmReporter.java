package com.wftk.exception.spring.boot.autoconfigure.alarm;

import org.springframework.lang.Nullable;

/**
 * 普通文本告警(不适用模板)
 * <AUTHOR>
 * @create 2024/5/24 15:57
 */
public interface TextAlarmReporter {

    default void report(String subject, String content) {
        report(subject, content, null, null);
    }

    default void report(String subject, String content, String userGroup) {
        report(subject, content, userGroup, null);
    }


    /**
     *
     * @param subject 标题
     * @param content 内容
     * @param userGroup 用户分组（可自定义不同分组）, null则发送给所有分组
     * @param alarmChannel 告警渠道，null则默认所有渠道
     */
    void report(String subject, String content, @Nullable String userGroup, @Nullable AlarmChannel alarmChannel);
}
