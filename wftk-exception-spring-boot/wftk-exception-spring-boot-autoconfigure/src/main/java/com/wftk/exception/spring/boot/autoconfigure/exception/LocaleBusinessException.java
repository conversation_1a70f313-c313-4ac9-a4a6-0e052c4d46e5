package com.wftk.exception.spring.boot.autoconfigure.exception;

import com.wftk.exception.core.exception.BaseBusinessException;
import com.wftk.exception.spring.boot.autoconfigure.locale.Localizable;

import java.io.Serial;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 16:24
 */
public class LocaleBusinessException extends BaseBusinessException implements Localizable {

    @Serial
    private static final long serialVersionUID = 4815319569073257711L;

    private final int localeCode;

    public LocaleBusinessException(int localeCode) {
        super();
        this.localeCode = localeCode;
    }

    public LocaleBusinessException(int localeCode, Throwable throwable) {
        super(throwable);
        this.localeCode = localeCode;
    }

    @Override
    public String getMessage() {
        String msg = getLocalizedMessage();
        if (msg == null || msg.length() == 0) {
            return super.getMessage();
        }
        return msg;
    }

    @Override
    public String getLocalizedMessage() {
        return localize(localeCode);
    }

    public int getLocaleCode() {
        return localeCode;
    }

    @Override
    public int getCode() {
        return localeCode;
    }
}
