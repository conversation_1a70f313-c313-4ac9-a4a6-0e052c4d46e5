package com.wftk.exception.spring.boot.autoconfigure.translators.webmvc;

import com.wftk.exception.common.GlobalErrorConstants;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.translator.base.AbstractExceptionTranslator;
import org.springframework.http.HttpStatus;
import org.springframework.web.HttpRequestMethodNotSupportedException;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 16:24
 */
public class HttpMethodNotSupportedExceptionTranslator extends AbstractExceptionTranslator {

    @Override
    protected ResolvedResult doTranslate(Throwable throwable) {
        return ResolvedResultBuilder.build(GlobalErrorConstants.METHOD_NOT_ALLOWED.code(), GlobalErrorConstants.METHOD_NOT_ALLOWED.message(),
                HttpStatus.METHOD_NOT_ALLOWED.value());
    }

    @Override
    public boolean support(Throwable throwable) {
        return throwable instanceof HttpRequestMethodNotSupportedException;
    }
}
