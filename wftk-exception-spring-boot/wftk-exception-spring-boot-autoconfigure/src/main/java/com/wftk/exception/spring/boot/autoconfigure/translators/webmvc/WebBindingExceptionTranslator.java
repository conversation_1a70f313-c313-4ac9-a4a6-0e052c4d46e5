package com.wftk.exception.spring.boot.autoconfigure.translators.webmvc;

import com.wftk.exception.common.GlobalErrorConstants;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.translator.base.AbstractExceptionTranslator;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;

import java.util.Optional;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 16:24
 */
public class WebBindingExceptionTranslator extends AbstractExceptionTranslator {

    @Override
    protected ResolvedResult doTranslate(Throwable throwable) {
        BindException e = (BindException) throwable;
        String bindingResult = Optional.ofNullable(e.getBindingResult().getFieldError())
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .orElse(e.getLocalizedMessage());
        return ResolvedResultBuilder.build(GlobalErrorConstants.BAD_REQUEST.code(), bindingResult, HttpStatus.BAD_REQUEST.value());
    }

    @Override
    public boolean support(Throwable throwable) {
        return throwable instanceof BindException;
    }
}
