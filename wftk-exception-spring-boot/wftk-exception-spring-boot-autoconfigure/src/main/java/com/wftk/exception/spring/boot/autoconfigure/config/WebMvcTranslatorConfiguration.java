package com.wftk.exception.spring.boot.autoconfigure.config;

import com.wftk.exception.spring.boot.autoconfigure.translators.webmvc.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2023/4/12 14:29
 */
@Configuration
public class WebMvcTranslatorConfiguration {

    @ConditionalOnMissingBean
    @Bean
    HttpMethodNotSupportedExceptionTranslator httpMethodNotSupportedExceptionTranslator() {
        return new HttpMethodNotSupportedExceptionTranslator();
    }

    @ConditionalOnMissingBean
    @Bean
    WebBindingExceptionTranslator webBindingExceptionTranslator() {
        return new WebBindingExceptionTranslator();
    }

    @ConditionalOnMissingBean
    @Bean
    ServletRequestBindingExceptionTranslator servletRequestBindingExceptionTranslator() {
        return new ServletRequestBindingExceptionTranslator();
    }

    @ConditionalOnMissingBean
    @Bean
    NoHandlerFoundExceptionTranslator noHandlerFoundExceptionTranslator() {
        return new NoHandlerFoundExceptionTranslator();
    }

    @ConditionalOnMissingBean
    @Bean
    NoResourceFoundExceptionTranslator noResourceFoundExceptionTranslator() {
        return new NoResourceFoundExceptionTranslator();
    }

    @ConditionalOnMissingBean
    @Bean
    ConstraintViolationExceptionTranslator constraintViolationExceptionTranslator() {
        return new ConstraintViolationExceptionTranslator();
    }

    @ConditionalOnMissingBean
    @Bean
    HttpMessageNotReadableExceptionTranslator httpMessageNotReadableExceptionTranslator() {
        return new HttpMessageNotReadableExceptionTranslator();
    }
}
