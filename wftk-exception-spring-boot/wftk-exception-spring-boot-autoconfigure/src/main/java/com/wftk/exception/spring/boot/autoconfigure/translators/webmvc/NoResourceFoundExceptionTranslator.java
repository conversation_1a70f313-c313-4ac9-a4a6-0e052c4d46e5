package com.wftk.exception.spring.boot.autoconfigure.translators.webmvc;

import com.wftk.exception.common.GlobalErrorConstants;
import com.wftk.exception.common.HttpStatusCode;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.translator.base.AbstractExceptionTranslator;
import org.springframework.web.servlet.resource.NoResourceFoundException;

/**
 * <AUTHOR>
 * @create 2024/4/30 17:11
 */
public class NoResourceFoundExceptionTranslator extends AbstractExceptionTranslator {
    @Override
    protected ResolvedResult doTranslate(Throwable throwable) {
        return ResolvedResultBuilder.build(GlobalErrorConstants.NOT_FOUND, HttpStatusCode.NOT_FOUND);
    }

    @Override
    public boolean support(Throwable throwable) {
        return throwable instanceof NoResourceFoundException;
    }
}
