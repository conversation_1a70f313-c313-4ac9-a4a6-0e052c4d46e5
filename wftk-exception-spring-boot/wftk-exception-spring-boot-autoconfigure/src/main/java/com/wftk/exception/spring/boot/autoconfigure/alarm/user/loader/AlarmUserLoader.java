package com.wftk.exception.spring.boot.autoconfigure.alarm.user.loader;


import com.wftk.exception.spring.boot.autoconfigure.alarm.user.AlarmUserInfo;
import com.wftk.exception.spring.boot.autoconfigure.alarm.user.AlarmUserType;
import org.springframework.lang.NonNull;

import java.util.Collection;

/**
 * <AUTHOR>
 * @create 2024/5/27 10:18
 */
public interface AlarmUserLoader<U extends AlarmUserInfo> {

    /**
     * 根据分组加载
     * @param group
     * @return
     */
    default Collection<U> load(@NonNull String group) {
        return load(group, null);
    }


    /**
     * 根据用户类型加载
     * @param alarmUserType
     * @return
     */
    Collection<U> load(@NonNull AlarmUserType alarmUserType);


    /**
     *
     * @param group
     * @param alarmUserType
     * @return
     */
    Collection<U> load(@NonNull String group, AlarmUserType alarmUserType);


    /**
     * 加载所有用户
     * @return
     */
    Collection<U> loadAll();
}
