package com.wftk.exception.spring.boot.autoconfigure.translators.webmvc;

import com.wftk.exception.common.GlobalErrorConstants;
import com.wftk.exception.common.HttpStatusCode;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.translator.base.AbstractExceptionTranslator;
import org.springframework.web.servlet.NoHandlerFoundException;

/**
 * <AUTHOR>
 * @create 2023/6/14 15:37
 */
public class NoHandlerFoundExceptionTranslator extends AbstractExceptionTranslator {
    @Override
    protected ResolvedResult doTranslate(Throwable throwable) {
        return ResolvedResultBuilder.build(GlobalErrorConstants.NOT_FOUND, HttpStatusCode.NOT_FOUND);
    }

    @Override
    public boolean support(Throwable throwable) {
        return throwable instanceof NoHandlerFoundException;
    }
}
