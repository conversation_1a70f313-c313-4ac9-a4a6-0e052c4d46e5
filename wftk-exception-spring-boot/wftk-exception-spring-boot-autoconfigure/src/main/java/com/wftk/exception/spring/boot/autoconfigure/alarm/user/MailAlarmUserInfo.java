package com.wftk.exception.spring.boot.autoconfigure.alarm.user;

import java.util.Set;

/**
 * 邮箱用户相关信息
 * <AUTHOR>
 * @create 2024/5/27 18:39
 */
public class MailAlarmUserInfo extends AlarmUserInfo {

    private String from;
    private Set<String> to;
    private Set<String> cc;
    private Set<String> bcc;
    private String replyTo;

    public MailAlarmUserInfo(String group, AlarmUserType type) {
        super(group, type);
    }


    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public Set<String> getTo() {
        return to;
    }

    public void setTo(Set<String> to) {
        this.to = to;
    }

    public Set<String> getCc() {
        return cc;
    }

    public void setCc(Set<String> cc) {
        this.cc = cc;
    }

    public Set<String> getBcc() {
        return bcc;
    }

    public void setBcc(Set<String> bcc) {
        this.bcc = bcc;
    }

    public String getReplyTo() {
        return replyTo;
    }

    public void setReplyTo(String replyTo) {
        this.replyTo = replyTo;
    }
}
