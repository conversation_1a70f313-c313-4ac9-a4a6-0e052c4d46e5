//package com.wftk.exception.spring.boot.autoconfigure.alarm.user.loader.mail;
//
//import com.wftk.exception.spring.boot.autoconfigure.alarm.user.AlarmUserType;
//import com.wftk.exception.spring.boot.autoconfigure.alarm.user.MailAlarmUserInfo;
//import com.wftk.exception.spring.boot.autoconfigure.alarm.user.loader.AlarmUserLoader;
//
//import java.util.Collection;
//import java.util.List;
//
///**
// * 邮件告警用户加载器
// * <AUTHOR>
// * @create 2024/5/27 18:46
// */
//public class PropertiesMailAlarmUserLoader implements AlarmUserLoader<MailAlarmUserInfo> {
//
//}
