package com.wftk.exception.spring.boot.autoconfigure.translators.webmvc;

import com.wftk.exception.common.GlobalErrorConstants;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.translator.base.AbstractExceptionTranslator;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.springframework.http.HttpStatus;

import java.util.Set;

/**
 * <AUTHOR>
 * @create 2023/12/22 14:26
 */
public class ConstraintViolationExceptionTranslator extends AbstractExceptionTranslator {
    @Override
    protected ResolvedResult doTranslate(Throwable throwable) {
        ConstraintViolationException constraintViolationException = (ConstraintViolationException) throwable;
        Set<ConstraintViolation<?>> constraintViolations = constraintViolationException.getConstraintViolations();
        String msg = "参数异常";
        for (ConstraintViolation<?> constraintViolation : constraintViolations) {
            if (constraintViolation != null) {
                msg = constraintViolation.getMessage();
                break;
            }
        }
        return ResolvedResultBuilder.build(GlobalErrorConstants.BAD_REQUEST.code(), msg, HttpStatus.BAD_REQUEST.value());
    }

    @Override
    public boolean support(Throwable throwable) {
        return throwable instanceof ConstraintViolationException;
    }
}
