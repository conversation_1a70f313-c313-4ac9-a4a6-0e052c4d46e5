package com.wftk.exception.spring.boot.autoconfigure.exception;

import com.wftk.exception.core.exception.alarm.AlarmBusinessException;
import com.wftk.exception.spring.boot.autoconfigure.locale.Localizable;

import java.io.Serial;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 16:24
 */
public class LocalAlarmBusinessException extends AlarmBusinessException implements Localizable {

    @Serial
    private static final long serialVersionUID = -8423987086879710458L;

    private final int localeCode;


    public LocalAlarmBusinessException(int localeCode) {
        super();
        this.localeCode = localeCode;
    }

    public LocalAlarmBusinessException(int localeCode, Throwable cause) {
        super(cause);
        this.localeCode = localeCode;
    }

    public int getCode() {
        return localeCode;
    }

    @Override
    public String getMessage() {
        return getLocalizedMessage() != null ? getLocalizedMessage() : super.getMessage();
    }

    @Override
    public String getLocalizedMessage() {
        return localize(localeCode);
    }
}
