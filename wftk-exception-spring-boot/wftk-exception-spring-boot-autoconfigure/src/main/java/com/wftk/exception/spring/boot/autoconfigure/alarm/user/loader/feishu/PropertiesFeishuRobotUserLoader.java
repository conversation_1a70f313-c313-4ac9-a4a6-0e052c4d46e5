package com.wftk.exception.spring.boot.autoconfigure.alarm.user.loader.feishu;

import cn.hutool.core.collection.CollUtil;
import com.wftk.exception.spring.boot.autoconfigure.alarm.properties.AlarmProperties;
import com.wftk.exception.spring.boot.autoconfigure.alarm.properties.FeishuRobotProperties;
import com.wftk.exception.spring.boot.autoconfigure.alarm.user.AlarmUserType;
import com.wftk.exception.spring.boot.autoconfigure.alarm.user.FeishuRobotAlarmUserInfo;
import com.wftk.exception.spring.boot.autoconfigure.alarm.user.loader.AlarmUserLoader;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 飞书机器人用户信息加载器
 * <AUTHOR>
 * @create 2024/5/27 18:48
 */
public class PropertiesFeishuRobotUserLoader implements AlarmUserLoader<FeishuRobotAlarmUserInfo> {

    private final AlarmProperties alarmProperties;

    public PropertiesFeishuRobotUserLoader(AlarmProperties alarmProperties) {
        this.alarmProperties = alarmProperties;
    }

    @Override
    public Collection<FeishuRobotAlarmUserInfo> load(AlarmUserType alarmUserType) {
        return loadAll();
    }

    @Override
    public Collection<FeishuRobotAlarmUserInfo> load(String group, AlarmUserType alarmUserType) {
        return loadAll();
    }

    @Override
    public Collection<FeishuRobotAlarmUserInfo> loadAll() {
        if (alarmProperties == null) {
            return null;
        }
        if (CollUtil.isEmpty(alarmProperties.getFeishuRobot())) {
            return null;
        }
        alarmProperties.getFeishuRobot().stream().collect(Collectors.groupingBy(FeishuRobotProperties::getGroup));
        return List.of();
    }
}
