package com.wftk.exception.spring.boot.autoconfigure.handler;

import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.LocaleResult;
import com.wftk.exception.core.translator.ExceptionTranslationExecutor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 16:24
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private final ExceptionTranslationExecutor exceptionTranslator;

    public GlobalExceptionHandler(ExceptionTranslationExecutor exceptionTranslationExecutor) {
        this.exceptionTranslator = exceptionTranslationExecutor;
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<LocaleResult> handleException(Exception e) {
        ResolvedResult resolvedResult = exceptionTranslator.translate(e);
        HttpStatus httpStatus = resolvedResult.status() == null ? HttpStatus.INTERNAL_SERVER_ERROR : HttpStatus.resolve(resolvedResult.status());
        httpStatus = httpStatus != null ? httpStatus : HttpStatus.INTERNAL_SERVER_ERROR;
        return new ResponseEntity<>(resolvedResult.body(), httpStatus);
    }

}
