package com.wftk.exception.spring.boot.autoconfigure;

import com.wftk.exception.core.translator.ExceptionTranslationExecutor;
import com.wftk.exception.core.translator.base.ExceptionTranslator;
import com.wftk.exception.core.translator.registry.DefaultExceptionTranslatorRegistry;
import com.wftk.exception.core.translator.registry.ExceptionTranslatorRegistry;
import com.wftk.exception.spring.boot.autoconfigure.config.DefaultTranslatorConfiguration;
import com.wftk.exception.spring.boot.autoconfigure.config.WebMvcTranslatorConfiguration;
import com.wftk.exception.spring.boot.autoconfigure.handler.GlobalExceptionHandler;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.servlet.DispatcherServlet;

import java.util.List;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 16:36
 */
@Configuration
@Import({DefaultTranslatorConfiguration.class, WebMvcTranslatorConfiguration.class})
@EnableAsync
public class ExceptionHandlerAutoConfiguration {

    @ConditionalOnMissingBean
    @Bean
    public ExceptionTranslationExecutor exceptionTranslationExecutor(List<ExceptionTranslator> exceptionTranslatorList) {
        ExceptionTranslatorRegistry exceptionTranslatorRegistry = new DefaultExceptionTranslatorRegistry(500);
        exceptionTranslatorRegistry.register(exceptionTranslatorList);
        return new ExceptionTranslationExecutor(exceptionTranslatorRegistry);
    }


    @ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
    @ConditionalOnClass(DispatcherServlet.class)
    @Bean
    public GlobalExceptionHandler globalExceptionHandler(ExceptionTranslationExecutor exceptionTranslationExecutor) {
        return new GlobalExceptionHandler(exceptionTranslationExecutor);
    }
}
