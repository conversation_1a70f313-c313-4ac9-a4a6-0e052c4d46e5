package com.wftk.exception.spring.boot.autoconfigure.locale;


import org.springframework.context.i18n.LocaleContextHolder;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 16:24
 */
public interface Localizable {

    default String localize(int code) {
        return MessageSourceHolder.get()
                .map(it -> it.getMessage(String.valueOf(code), null, "", LocaleContextHolder.getLocale()))
                .orElse("");
    }
}
