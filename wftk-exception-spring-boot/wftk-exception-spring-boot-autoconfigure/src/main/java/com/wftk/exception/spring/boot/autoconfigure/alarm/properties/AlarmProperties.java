package com.wftk.exception.spring.boot.autoconfigure.alarm.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/5/28 14:21
 */
@ConfigurationProperties(prefix = "config.alarm")
public class AlarmProperties {

    /**
     * 邮件配置
     */
    private List<MailProperties> mail;

    /**
     * 飞书机器人配置
     */
    private List<FeishuRobotProperties> feishuRobot;

    public List<MailProperties> getMail() {
        return mail;
    }

    public void setMail(List<MailProperties> mail) {
        this.mail = mail;
    }

    public List<FeishuRobotProperties> getFeishuRobot() {
        return feishuRobot;
    }

    public void setFeishuRobot(List<FeishuRobotProperties> feishuRobot) {
        this.feishuRobot = feishuRobot;
    }
}
