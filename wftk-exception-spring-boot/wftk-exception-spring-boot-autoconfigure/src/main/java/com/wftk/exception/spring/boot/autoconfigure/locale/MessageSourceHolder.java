package com.wftk.exception.spring.boot.autoconfigure.locale;

import org.springframework.context.MessageSource;

import java.util.Optional;

/**
 * @Author: ying.dong
 * @Date: 2021/4/19 16:24
 */
public class MessageSourceHolder {

    private static MessageSource messageSource;

    public synchronized static void set(MessageSource source) {
        messageSource = source;
    }

    public static Optional<MessageSource> get() {
        return Optional.ofNullable(messageSource);
    }
}
