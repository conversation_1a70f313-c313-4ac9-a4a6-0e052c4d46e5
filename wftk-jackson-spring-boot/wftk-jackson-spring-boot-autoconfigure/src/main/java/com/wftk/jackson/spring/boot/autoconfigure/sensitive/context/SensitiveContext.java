package com.wftk.jackson.spring.boot.autoconfigure.sensitive.context;

/**
 * 脱敏Context，外部可以通过扩展实现自己的Context用于控制当前是否需要脱敏（例如： 权限控制）
 * <AUTHOR>
 * @create 2023/9/21 16:09
 */
public interface SensitiveContext {

    /**
     * 判断手机号是否需要脱敏
     * @return
     */
    boolean maskTel();

    /**
     * 判断邮箱是否需要脱敏
     * @return
     */
    boolean maskEmail();

    /**
     * 判断身份证是否需要脱敏
     * @return
     */
    boolean maskIdCard();

    /**
     * 判断姓名是否需要脱敏
     * @return
     */
    boolean maskName();

    /**
     * 判断银行卡号是否需要脱敏
     * @return
     */
    boolean maskBankCard();
}
