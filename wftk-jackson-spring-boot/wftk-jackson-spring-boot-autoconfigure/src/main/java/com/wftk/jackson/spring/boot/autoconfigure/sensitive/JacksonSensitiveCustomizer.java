package com.wftk.jackson.spring.boot.autoconfigure.sensitive;

import com.wftk.jackson.spring.boot.autoconfigure.introspector.HierarchicalAnnotationIntrospector;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

/**
 * <AUTHOR>
 * @create 2023/10/11 20:57
 */
public class JacksonSensitiveCustomizer implements Jackson2ObjectMapperBuilderCustomizer {
    @Override
    public void customize(Jackson2ObjectMapperBuilder jacksonObjectMapperBuilder) {
        jacksonObjectMapperBuilder.annotationIntrospector(new HierarchicalAnnotationIntrospector());
    }
}
