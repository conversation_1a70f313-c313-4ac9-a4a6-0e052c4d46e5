package com.wftk.jackson.spring.boot.autoconfigure.sensitive.annotation;

import cn.hutool.core.annotation.AliasFor;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.jackson.spring.boot.autoconfigure.sensitive.serializer.EmailSerializer;

import java.lang.annotation.*;

/**
 * 邮箱脱敏
 * <AUTHOR>
 * @create 2023/9/21 15:40
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@JsonSerialize(using = EmailSerializer.class)
@SensitiveData
public @interface Email {

    /**
     * 星号前保留明文位数
     * @return
     */
    @AliasFor(annotation = SensitiveData.class)
    int startInclude() default 1;


    /**
     * 在反序列化时，如果已经是带星号脱敏的，则置为null，避免写入到数据库
     * @return
     */
    @AliasFor(annotation = SensitiveData.class)
    boolean ignoreDeserialize() default true;
}
