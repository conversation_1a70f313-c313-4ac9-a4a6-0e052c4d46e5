package com.wftk.jackson.spring.boot.autoconfigure.properties;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.EnumMap;
import java.util.Map;

@ConfigurationProperties(JacksonProperties.PREFIX)
@Data
public class JacksonProperties {

    public static final String PREFIX = "config.jackson";

    /**
     * 序列化feature
     */
    @NestedConfigurationProperty
    private final Map<SerializationFeature, Boolean> serialization = new EnumMap<>(SerializationFeature.class);

    /**
     * 反序列化feature
     */
    @NestedConfigurationProperty
    private final Map<DeserializationFeature, Boolean> deserialization = new EnumMap<>(DeserializationFeature.class);

    /**
     * mapper配置
     */
    @NestedConfigurationProperty
    private final Map<MapperFeature, Boolean> mapper = new EnumMap<>(MapperFeature.class);
}
