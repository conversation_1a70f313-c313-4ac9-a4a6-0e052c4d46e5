package com.wftk.jackson.spring.boot.autoconfigure.introspector;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.introspect.Annotated;
import com.fasterxml.jackson.databind.introspect.AnnotatedField;
import com.fasterxml.jackson.databind.introspect.AnnotatedMethod;
import com.fasterxml.jackson.databind.introspect.JacksonAnnotationIntrospector;
import org.springframework.core.annotation.AnnotatedElementUtils;

import java.io.Serial;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * 支持对自定义注解的扫描
 * <AUTHOR>
 * @create 2023/9/21 18:24
 */
public class HierarchicalAnnotationIntrospector extends JacksonAnnotationIntrospector {

    @Serial
    private static final long serialVersionUID = 7459329712690729937L;

    @Override
    protected <A extends Annotation> A _findAnnotation(Annotated ann, Class<A> annoClass) {
        A annotation = super._findAnnotation(ann, annoClass);
        if (annotation != null) {
            return annotation;
        }
        if (ann instanceof AnnotatedField) {
            return AnnotatedElementUtils.getMergedAnnotation(ann.getAnnotated(), annoClass);
        }
        if (ann instanceof AnnotatedMethod anm) {
            Method method = anm.getAnnotated();
            annotation = AnnotatedElementUtils.getMergedAnnotation(method, annoClass);
            if (annotation != null) {
                return annotation;
            }
            //如果是getter方法且方法上没有注解，则看getter方法相应的field是否有注解
            boolean isGetterOrSetter = ReflectUtil.isGetterOrSetterIgnoreCase(method);
            String methodName = method.getName();
            if (isGetterOrSetter && (methodName.startsWith("get") || methodName.startsWith("is"))) {
                String fieldName = StrUtil.lowerFirst(methodName.substring(3));
                Field field = ReflectUtil.getField(method.getDeclaringClass(), fieldName);
                if (field != null) {
                    annotation = AnnotatedElementUtils.getMergedAnnotation(field, annoClass);
                    if (annotation != null) {
                        return annotation;
                    }
                }
            }
        }
        if (ann == null || ann.getAnnotated() == null || annoClass == null) {
            return null;
        }
        return AnnotatedElementUtils.getMergedAnnotation(ann.getAnnotated(), annoClass);
    }

}
