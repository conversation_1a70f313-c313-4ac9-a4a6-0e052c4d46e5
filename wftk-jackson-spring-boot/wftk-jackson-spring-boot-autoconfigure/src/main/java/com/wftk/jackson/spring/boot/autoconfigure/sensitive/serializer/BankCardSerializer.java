package com.wftk.jackson.spring.boot.autoconfigure.sensitive.serializer;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.wftk.jackson.spring.boot.autoconfigure.sensitive.context.SensitiveContext;

import java.io.IOException;

/**
 * 针对银行卡号进行脱敏
 * <AUTHOR>
 * @create 2023/9/21 15:49
 */
public class BankCardSerializer extends SensitiveSerializer {

    public BankCardSerializer() {
        this(0, 0, true, null);
    }

    public BankCardSerializer(int startInclude, int endExclude, boolean ignoreDeserialize, SensitiveContext sensitiveContext) {
        super(startInclude, endExclude, ignoreDeserialize, sensitiveContext);
    }

    @Override
    protected void doMask(String value, int maskEndIndex, JsonGenerator jsonGenerator, SensitiveContext sensitiveContext) throws IOException {
        if (sensitiveContext != null && !sensitiveContext.maskBankCard()) {
            jsonGenerator.writeString(value);
            return;
        }
        jsonGenerator.writeString(StrUtil.hide(value, startInclude, maskEndIndex));
    }

    @Override
    protected JsonSerializer<?> createSerializer(int startInclude, int endExclude, boolean ignoreDeserialize, SensitiveContext sensitiveContext) {
        return new BankCardSerializer(startInclude, endExclude, ignoreDeserialize, sensitiveContext);
    }
}
