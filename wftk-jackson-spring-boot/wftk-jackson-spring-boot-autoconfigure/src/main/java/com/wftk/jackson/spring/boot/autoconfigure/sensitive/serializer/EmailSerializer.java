package com.wftk.jackson.spring.boot.autoconfigure.sensitive.serializer;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.wftk.jackson.spring.boot.autoconfigure.sensitive.context.SensitiveContext;

import java.io.IOException;


/**
 * 针对邮箱脱敏
 * <AUTHOR>
 * @create 2023/9/21 15:48
 */
public class EmailSerializer extends SensitiveSerializer {

    public EmailSerializer() {
        this(0, 0, true, null);
    }

    public EmailSerializer(int startInclude, int endExclude, boolean ignoreDeserialize, SensitiveContext sensitiveContext) {
        super(startInclude, endExclude, ignoreDeserialize, sensitiveContext);
    }

    @Override
    protected void doMask(String value, int maskEndIndex, JsonGenerator jsonGenerator, SensitiveContext sensitiveContext) throws IOException {
        if (sensitiveContext != null && !sensitiveContext.maskEmail()) {
            jsonGenerator.writeString(value);
            return;
        }
        int index = StrUtil.indexOf(value, '@');
        if (index <= 1) {
            jsonGenerator.writeString(value);
            return;
        }
        jsonGenerator.writeString(StrUtil.hide(value, startInclude, index));
    }

    @Override
    protected JsonSerializer<?> createSerializer(int startInclude, int endExclude, boolean ignoreDeserialize, SensitiveContext sensitiveContext) {
        return new EmailSerializer(startInclude, endExclude, ignoreDeserialize, sensitiveContext);
    }
}
