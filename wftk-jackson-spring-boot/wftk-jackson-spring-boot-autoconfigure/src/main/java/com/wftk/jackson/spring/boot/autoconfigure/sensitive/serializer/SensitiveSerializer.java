package com.wftk.jackson.spring.boot.autoconfigure.sensitive.serializer;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.fasterxml.jackson.databind.ser.std.StringSerializer;
import com.wftk.jackson.spring.boot.autoconfigure.sensitive.annotation.SensitiveData;
import com.wftk.jackson.spring.boot.autoconfigure.sensitive.context.SensitiveContext;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.lang.Nullable;

import java.io.IOException;
import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @create 2023/9/21 16:05
 */
public abstract class SensitiveSerializer extends JsonSerializer<String> implements ContextualSerializer {

    /**
     * 星号前保留明文位数
     */
    protected final int startInclude;

    /**
     * 星号后保留明文位数
     */
    protected final int endExclude;

    /**
     * 在反序列化时，如果已经是带星号脱敏的，则置为null，避免写入到数据库
     */
    protected final boolean ignoreDeserialize;

    protected final SensitiveContext sensitiveContext;

    public SensitiveSerializer(int startInclude, int endExclude, boolean ignoreDeserialize, SensitiveContext sensitiveContext) {
        this.sensitiveContext = sensitiveContext;
        this.startInclude = Math.max(startInclude, 0);
        this.endExclude = Math.max(endExclude, 0);
        this.ignoreDeserialize = ignoreDeserialize;
    }


    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        if (StrUtil.isBlank(value)) {
            gen.writeString(value);
            return;
        }
        int endIndex = maskEndIndex(startInclude, endExclude, value);
        doMask(value, endIndex, gen, sensitiveContext);
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property) throws JsonMappingException {
        SensitiveData annotation = property.getAnnotation(SensitiveData.class);
        if (annotation == null) {
            Field field = ReflectUtil.getField(property.getMember().getDeclaringClass(), property.getName());
            if (field != null) {
                annotation = AnnotatedElementUtils.getMergedAnnotation(field, SensitiveData.class);
            }
        }
        if (annotation == null) {
            return new StringSerializer();
        }
        return createSerializer(annotation.startInclude(), annotation.endExclude(), annotation.ignoreDeserialize(), sensitiveContext);
    }


    /**
     *
     * @param startInclude
     * @param endExclude
     * @param value
     * @return
     */
    public int maskEndIndex(int startInclude, int endExclude, String value) {
        int valueLength = value.length();
        int maskEndIndex = valueLength - endExclude;

        if (maskEndIndex <= startInclude) {
            return valueLength;
        }

        return maskEndIndex;
    }

    /**
     * 脱敏
     * @param value
     * @param endIndex
     * @param jsonGenerator
     * @param sensitiveContext
     */
    protected abstract void doMask(String value, int endIndex, JsonGenerator jsonGenerator, @Nullable SensitiveContext sensitiveContext) throws IOException;

    /**
     * 创建Serializer
     * @param startInclude
     * @param endExclude
     * @param ignoreDeserialize
     * @param sensitiveContext
     * @return
     */
    protected abstract JsonSerializer<?> createSerializer(int startInclude, int endExclude, boolean ignoreDeserialize,
                                                          @Nullable SensitiveContext sensitiveContext);

}
