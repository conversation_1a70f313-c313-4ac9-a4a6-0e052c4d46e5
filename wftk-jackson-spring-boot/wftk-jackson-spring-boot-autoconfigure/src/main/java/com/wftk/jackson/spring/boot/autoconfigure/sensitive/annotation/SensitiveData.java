package com.wftk.jackson.spring.boot.autoconfigure.sensitive.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @create 2023/9/21 16:47
 */
@Target({ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SensitiveData {

    /**
     * 星号前保留明文位数
     * @return
     */
    int startInclude() default 0;


    /**
     * 星号后保留明文位数
     * @return
     */
    int endExclude() default 0;


    /**
     * 在反序列化时，如果已经是带星号脱敏的，则置为null，避免写入到数据库
     * @return
     */
    boolean ignoreDeserialize() default true;
}
