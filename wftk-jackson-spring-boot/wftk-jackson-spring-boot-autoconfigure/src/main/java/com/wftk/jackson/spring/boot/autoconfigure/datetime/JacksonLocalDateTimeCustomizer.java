package com.wftk.jackson.spring.boot.autoconfigure.datetime;

import com.fasterxml.jackson.databind.SerializationFeature;
import com.wftk.jackson.deserializer.datetime.LocalDateTimeDeserializer;
import com.wftk.jackson.serializer.datetime.LocalDateTimeSerializer;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;

/**
 * <AUTHOR>
 * @create 2022/6/14 14:58
 */
public class JacksonLocalDateTimeCustomizer implements Jackson2ObjectMapperBuilderCustomizer {

    @Override
    public void customize(org.springframework.http.converter.json.Jackson2ObjectMapperBuilder jacksonObjectMapperBuilder) {
        jacksonObjectMapperBuilder.featuresToEnable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        jacksonObjectMapperBuilder.serializers(new LocalDateTimeSerializer());
        jacksonObjectMapperBuilder.deserializers(new LocalDateTimeDeserializer());
    }
}
