package com.wftk.jackson.spring.boot.autoconfigure.sensitive.annotation;

import cn.hutool.core.annotation.AliasFor;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wftk.jackson.spring.boot.autoconfigure.sensitive.serializer.IdCardSerializer;

import java.lang.annotation.*;

/**
 * 身份证号脱敏
 * <AUTHOR>
 * @create 2023/9/21 15:40
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@JsonSerialize(using = IdCardSerializer.class)
@SensitiveData
public @interface IdCard {

    /**
     * 星号前保留明文位数
     * @return
     */
    @AliasFor(annotation = SensitiveData.class)
    int startInclude() default 1;


    /**
     * 星号后保留明文位数
     * @return
     */
    @AliasFor(annotation = SensitiveData.class)
    int endExclude() default 2;


    /**
     * 在反序列化时，如果已经是带星号脱敏的，则置为null，避免写入到数据库
     * @return
     */
    @AliasFor(annotation = SensitiveData.class)
    boolean ignoreDeserialize() default true;
}
