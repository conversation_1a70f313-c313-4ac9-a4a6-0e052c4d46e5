package com.wftk.jackson.spring.boot.autoconfigure;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wftk.jackson.core.JSONObject;
import com.wftk.jackson.core.ObjectMapperBuilder;
import com.wftk.jackson.properties.JsonProperties;
import com.wftk.jackson.spring.boot.autoconfigure.properties.JacksonProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * supported for java8
 * @Author: ying.dong
 * @Date: 2020/4/22 9:58
 */
@Configuration
@ConditionalOnClass(ObjectMapper.class)
@EnableConfigurationProperties(JacksonProperties.class)
public class JacksonAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    ObjectMapperBuilder objectMapperBuilder(JacksonProperties jacksonProperties) {
        JsonProperties jsonProperties = new JsonProperties();
        jsonProperties.setMapper(jacksonProperties.getMapper());
        jsonProperties.setSerialization(jacksonProperties.getSerialization());
        jsonProperties.setDeserialization(jacksonProperties.getDeserialization());
        return new ObjectMapperBuilder(jsonProperties);
    }

    @Bean
    @ConditionalOnMissingBean
    JSONObject jsonObject(ObjectMapperBuilder objectMapperBuilder) {
        return new JSONObject(objectMapperBuilder);
    }

}
