package com.wftk.jackson.properties;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.SerializationFeature;

import java.util.EnumMap;
import java.util.Map;

public class JsonProperties {

    /**
     * 是否启用针对localDateTime的定制化处理
     */
    private boolean localDateTimeCustomized = true;

    /**
     * 序列化feature
     */
    private Map<SerializationFeature, Boolean> serialization = new EnumMap<>(SerializationFeature.class);

    /**
     * 反序列化feature
     */
    private Map<DeserializationFeature, Boolean> deserialization = new EnumMap<>(DeserializationFeature.class);

    /**
     * mapper配置
     */
    private Map<MapperFeature, Boolean> mapper = new EnumMap<>(MapperFeature.class);

    public boolean isLocalDateTimeCustomized() {
        return localDateTimeCustomized;
    }

    public void setLocalDateTimeCustomized(boolean localDateTimeCustomized) {
        this.localDateTimeCustomized = localDateTimeCustomized;
    }

    public Map<SerializationFeature, Boolean> getSerialization() {
        return serialization;
    }

    public void setSerialization(Map<SerializationFeature, Boolean> serialization) {
        this.serialization = serialization;
    }

    public Map<DeserializationFeature, Boolean> getDeserialization() {
        return deserialization;
    }

    public void setDeserialization(Map<DeserializationFeature, Boolean> deserialization) {
        this.deserialization = deserialization;
    }

    public Map<MapperFeature, Boolean> getMapper() {
        return mapper;
    }

    public void setMapper(Map<MapperFeature, Boolean> mapper) {
        this.mapper = mapper;
    }
}
