package com.wftk.jackson.serializer.datetime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;
import java.io.Serial;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

/**
 * LocalDateTime序列化
 * <AUTHOR>
 * @create 2022/6/14 14:04
 */
public class LocalDateTimeSerializer extends StdSerializer<LocalDateTime> implements ContextualSerializer {

    private final DateTimeFormatter formatter;
    private final Boolean parseToTimestamp;

    private static final DateTimeFormatter DEFAULT_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

    @Serial
    private static final long serialVersionUID = 6783413112068645331L;

    public LocalDateTimeSerializer() {
        this(null, null);
    }

    public LocalDateTimeSerializer(DateTimeFormatter formatter) {
        this(formatter, null);
    }

    private LocalDateTimeSerializer(DateTimeFormatter formatter, Boolean parseToTimestamp) {
        super(LocalDateTime.class, false);
        this.formatter = formatter;
        this.parseToTimestamp = parseToTimestamp;
    }

    @Override
    public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        if ((parseToTimestamp == null || parseToTimestamp) && provider.isEnabled(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)) {
            ZoneOffset offset = ZoneId.systemDefault().getRules().getOffset(Instant.now());
            long timeMills = value.toInstant(offset).toEpochMilli();
            gen.writeNumber(timeMills);
        } else  {
            DateTimeFormatter dtf = formatter;
            if (dtf == null) {
                dtf = DEFAULT_FORMATTER;
            }
            gen.writeString(value.format(dtf));
        }
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property) throws JsonMappingException {
        JsonFormat.Value format = findFormatOverrides(prov, property, handledType());
        Boolean parseToTimestamp = null;
        if (format == null) {
            return this;
        }
        if (format.getShape() != null && format.getShape() == JsonFormat.Shape.NUMBER) {
            parseToTimestamp = true;
        }
        DateTimeFormatter dtf = null;
        if (format.hasPattern()) {
            parseToTimestamp = false;
            final String pattern = format.getPattern();
            final Locale locale = format.hasLocale() ? format.getLocale() : prov.getLocale();
            if (locale == null) {
                dtf = DateTimeFormatter.ofPattern(pattern);
            } else {
                dtf = DateTimeFormatter.ofPattern(pattern, locale);
            }
            if (format.hasTimeZone()) {
                dtf = dtf.withZone(format.getTimeZone().toZoneId());
            }
        }
        if (this.parseToTimestamp != parseToTimestamp || this.formatter != dtf) {
            return new LocalDateTimeSerializer(dtf, parseToTimestamp);
        }
        return this;
    }
}
