package com.wftk.jackson.serializer.rmb;


import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2022/7/15 上午11:17
 */
public class RMBFenToYuanSerializer extends JsonSerializer<Integer> {
    @Override
    public void serialize(Integer value, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (value == null) {
            jsonGenerator.writeNull();
            return;
        }
        BigDecimal result = BigDecimal.valueOf(value).multiply(BigDecimal.valueOf(0.01));
        jsonGenerator.writeObject(result);
    }
}
