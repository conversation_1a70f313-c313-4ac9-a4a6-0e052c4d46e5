package com.wftk.jackson.deserializer.rmb;


import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @Date 2022/7/15 上午11:11
 */
public class RMBYuanToFenDeserializer extends JsonDeserializer<Integer> {

    @Override
    public Integer deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        BigDecimal multiply = BigDecimal.valueOf(jsonParser.getDoubleValue()).multiply(BigDecimal.valueOf(100));
        return multiply.intValue();
    }
}
