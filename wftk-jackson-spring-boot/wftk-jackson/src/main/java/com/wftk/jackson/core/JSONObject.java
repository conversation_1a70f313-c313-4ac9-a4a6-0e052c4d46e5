package com.wftk.jackson.core;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.databind.type.MapType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.wftk.jackson.exception.JSONException;

import java.lang.reflect.Type;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Supplier;

/**
 * @Author: ying.dong
 * @Date: 2021/4/22 9:50
 */
public class JSONObject {

    private static JSONObject jsonObject;

    private static JSONObject unConfiguredJsonObject;

    private final ObjectMapper objectMapper;
    private final ObjectMapperBuilder objectMapperBuilder;

    public JSONObject(ObjectMapperBuilder objectMapperBuilder) {
        this.objectMapper = objectMapperBuilder.build();
        this.objectMapperBuilder = objectMapperBuilder;
        jsonObject = this;
    }

    /**
     * 获取单实例
     * @return
     */
    public static JSONObject getInstance() {
        if (jsonObject != null) {
            return jsonObject;
        }
        return newInstance();
    }

    /**
     *
     * @return
     */
    private synchronized static JSONObject newInstance() {
        if (unConfiguredJsonObject != null) {
            return unConfiguredJsonObject;
        }
        unConfiguredJsonObject = new JSONObject(new ObjectMapperBuilder(null));
        return unConfiguredJsonObject;
    }


    public ObjectMapper getObjectMapper() {
        return objectMapper;
    }

    public ObjectMapperBuilder getObjectMapperBuilder() {
        return objectMapperBuilder;
    }

    /**
     * json字符串转对象
     * @param json
     * @param clazz
     * @param <T>
     * @return
     */
    public <T> T parseObject(String json, Class<T> clazz) {
        if (StrUtil.isBlank(json)) {
            return null;
        }
        if (clazz.getCanonicalName().equals(String.class.getCanonicalName())) {
            return (T) json;
        }
        try {
            return objectMapper.readValue(json, clazz);
        } catch (Exception e) {
            throw new JSONException(e);
        }
    }

    /**
     * json字符串转对象(支持泛型)
     * @param json
     * @param targetType
     * @param <T>
     * @return
     */
    public <T> T parseObject(String json, TargetType<T> targetType) {
        if (StrUtil.isBlank(json)) {
            return null;
        }
        if (targetType.getType().getTypeName().equals(String.class.getCanonicalName())) {
            return (T) json;
        }
        try {
            return objectMapper.readValue(json, targetType);
        } catch (Exception e) {
            throw new JSONException(e);
        }
    }

    /**
     * json字符串转对象
     * @param json
     * @param type
     * @param <T>
     * @return
     */
    public <T> T parseObject(String json, Type type) {
        if (StrUtil.isBlank(json)) {
            return null;
        }
        if (type.getTypeName().equals(String.class.getCanonicalName())) {
            return (T) json;
        }
        JavaType javaType = TypeFactory.defaultInstance().constructType(type);
        try {
            return objectMapper.readValue(json, javaType);
        } catch (Exception e) {
            throw new JSONException(e);
        }
    }

    /**
     * json字符串转集合
     * @param json
     * @param collectionClazz
     * @param elementClazz
     * @param <E>
     * @return
     */
    public <E> Collection<E> parseCollection(String json, Class<? extends Collection<E>> collectionClazz, Class<E> elementClazz) {
        if (StrUtil.isBlank(json)) {
            return null;
        }
        CollectionType collectionType = objectMapper.getTypeFactory().constructCollectionType(collectionClazz, elementClazz);
        try {
            return objectMapper.readValue(json, collectionType);
        } catch (Exception e) {
            throw new JSONException(e);
        }
    }

    /**
     * json转Map对象
     * @param json
     * @param keyClazz
     * @param valueClazz
     * @param <K>
     * @param <V>
     * @return
     */
    public <K, V> Map<K, V> parseMap(String json, Class<K> keyClazz, Class<V> valueClazz) {
        if (StrUtil.isBlank(json)) {
            return null;
        }
        MapType mapType = objectMapper.getTypeFactory().constructMapType(Map.class, keyClazz, valueClazz);
        try {
            return objectMapper.readValue(json, mapType);
        } catch (Exception e) {
            throw new JSONException(e);
        }
    }

    /**
     * 对象转json字符串
     * @param bean
     * @return
     */
    public String toJSONString(Object bean) {
        if (bean == null) {
            return null;
        }
        if (bean instanceof String) {
            return (String) bean;
        }
        try {
            return objectMapper.writeValueAsString(bean);
        } catch (Exception e) {
            throw new JSONException(e);
        }
    }

    /**
     * 对象转换
     * @param fromValue
     * @param toValueType
     * @param <T>
     * @return
     */
    public <T> T convertValue(Object fromValue, Class<T> toValueType) {
        try {
            return objectMapper.convertValue(fromValue, toValueType);
        } catch (Exception e) {
            throw new JSONException(e);
        }
    }

    /**
     * 对象转换
     * @param fromValue
     * @param targetType
     * @param <T>
     * @return
     */
    public <T> T convertValue(Object fromValue, TargetType<T> targetType) {
        try {
            return objectMapper.convertValue(fromValue, targetType);
        } catch (Exception e) {
            throw new JSONException(e);
        }
    }



    /**
     * 利用Map构建json字符串
     * @param mapSupplier
     * @param <K>
     * @param <V>
     * @return
     */
    public <K, V> String buildJSON(Supplier<Map<K, V>> mapSupplier) {
        return toJSONString(mapSupplier.get());
    }

    /**
     * 查询节点值
     * @param jsonStr
     * @param fieldName
     * @return
     */
    public String findValueAsText(String jsonStr, String fieldName) {
        JsonNode jsonNode;
        try {
            jsonNode = objectMapper.readTree(jsonStr);
        } catch (Exception e) {
            throw new JSONException(e);
        }
        return Optional.ofNullable(jsonNode.findValue(fieldName))
                .map(it -> {
                    if (it instanceof ObjectNode) {
                        return it.toString();
                    }
                    return it.asText();
                })
                .orElse(null);
    }

    /**
     * 查询节点值
     * @param jsonStr
     * @param fieldName
     * @return
     */
    public List<String> findValuesAsText(String jsonStr, String fieldName) {
        JsonNode jsonNode;
        try {
            jsonNode = objectMapper.readTree(jsonStr);
        } catch (Exception e) {
            throw new JSONException(e);
        }
        return jsonNode.findValuesAsText(fieldName);
    }

}
