package com.wftk.jackson.core;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.Module;
import com.fasterxml.jackson.databind.cfg.ConfigFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import com.wftk.jackson.deserializer.datetime.LocalDateTimeDeserializer;
import com.wftk.jackson.properties.JsonProperties;
import com.wftk.jackson.serializer.datetime.LocalDateTimeSerializer;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * ObjectMapper 构造器
 */
public class ObjectMapperBuilder {

    private final JsonProperties jsonProperties;
    private final List<Module> modules = new ArrayList<>();

    public ObjectMapperBuilder(JsonProperties jsonProperties) {
        this.jsonProperties = jsonProperties;
    }

    public ObjectMapperBuilder(JsonProperties jsonProperties, List<Module> modules) {
        this.jsonProperties = jsonProperties;
        this.modules.addAll(modules);
    }

    /**
     * 配置module
     * @param objectMapper
     * @param modules
     */
    protected void configureModules(ObjectMapper objectMapper, List<Module> modules) {
        if (CollectionUtil.isEmpty(modules)) {
            return;
        }
        modules.forEach(objectMapper::registerModule);
    }

    /**
     * 设置序列化feature
     * @param objectMapper
     * @param featuresMap
     */
    protected void configureSerializationFeature(ObjectMapper objectMapper, Map<SerializationFeature, Boolean> featuresMap) {
        if (CollectionUtil.isEmpty(featuresMap)) {
            return;
        }
        featuresMap.forEach((k, v) -> configureFeature(objectMapper, k, v != null && v));
    }

    /**
     * 设置反序列化feature
     * @param objectMapper
     * @param featuresMap
     */
    protected void configureDeserializationFeature(ObjectMapper objectMapper, Map<DeserializationFeature, Boolean> featuresMap) {
        if (CollectionUtil.isEmpty(featuresMap)) {
            return;
        }
        featuresMap.forEach((k, v) -> configureFeature(objectMapper, k, v != null && v));
    }

    /**
     * 设置mapper的feature
     * @param objectMapper
     * @param featuresMap
     */
    protected void configureMapperFeature(ObjectMapper objectMapper, Map<MapperFeature, Boolean> featuresMap) {
        if (CollectionUtil.isEmpty(featuresMap)) {
            return;
        }
        featuresMap.forEach((k, v) -> configureFeature(objectMapper, k, v != null && v));
    }

    /**
     * 默认定制化
     * @param jsonProperties
     * @param objectMapper
     */
    protected void defaultCustomize(JsonProperties jsonProperties, ObjectMapper objectMapper) {
        //默认 module
        List<Module> defaultModules = new ArrayList<>();
        defaultModules.add(new ParameterNamesModule());
        defaultModules.add(new Jdk8Module());
        defaultModules.add(new JavaTimeModule());

        //LocalDateTime转时间戳处理
        if (jsonProperties == null || jsonProperties.isLocalDateTimeCustomized()) {
            SimpleModule localDateTimeModule = new SimpleModule();
            localDateTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer());
            localDateTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer());
            defaultModules.add(localDateTimeModule);
        }

        configureModules(objectMapper, defaultModules);

        //默认feature
        objectMapper.configure(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature(), true);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, true);
    }

    /**
     * 创建objectMapper
     * @return
     */
    protected ObjectMapper createObjectMapper() {
        return new ObjectMapper();
    }

    /**
     * 设置feature
     * @param objectMapper
     * @param feature
     * @param enabled
     */
    private void configureFeature(ObjectMapper objectMapper, ConfigFeature feature, boolean enabled) {
        if (feature instanceof DeserializationFeature) {
            objectMapper.configure((DeserializationFeature)feature, enabled);
        } else if (feature instanceof SerializationFeature) {
            objectMapper.configure((SerializationFeature) feature, enabled);
        } else if (feature instanceof MapperFeature) {
            objectMapper.configure((MapperFeature) feature, enabled);
        }
    }

    /**
     * 构造objectMapper
     * @return
     */
    public ObjectMapper build() {
        ObjectMapper objectMapper = createObjectMapper();
        //定制化
        defaultCustomize(jsonProperties, objectMapper);

        //配置module及feature
        configureModules(objectMapper, modules);
        if (jsonProperties != null) {
            configureSerializationFeature(objectMapper, jsonProperties.getSerialization());
            configureDeserializationFeature(objectMapper, jsonProperties.getDeserialization());
            configureMapperFeature(objectMapper, jsonProperties.getMapper());
        }
        return objectMapper;
    }
}
