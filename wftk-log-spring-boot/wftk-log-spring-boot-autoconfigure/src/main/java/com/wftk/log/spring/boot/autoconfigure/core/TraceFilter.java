package com.wftk.log.spring.boot.autoconfigure.core;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;


/**
 * <AUTHOR>
 * @create 2023/5/31 13:52
 */
public class TraceFilter extends OncePerRequestFilter implements MDCTraceIdSupport {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        try {
            String traceId = createTraceId();
            request.setAttribute(getTraceName(), traceId);
            filterChain.doFilter(request, response);
        } finally {
            clearTraceId();
        }
    }
}
