package com.wftk.log.spring.boot.autoconfigure.core;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.core.task.TaskDecorator;
import org.springframework.util.CollectionUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/5/31 15:08
 */
public class MDCTaskDecorator implements TaskDecorator {

    private final Logger logger = LoggerFactory.getLogger(MDCTaskDecorator.class);

    @Override
    public Runnable decorate(Runnable runnable) {
        Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
        return () -> {
            try {
                if (!CollectionUtils.isEmpty(copyOfContextMap)) {
                    MDC.setContextMap(copyOfContextMap);
                }
                runnable.run();
            } catch (Exception e) {
                logger.error("error.", e);
                throw e;
            } finally {
                MDC.clear();
            }
        };
    }
}
