package com.wftk.log.spring.boot.autoconfigure.core;

import org.slf4j.MDC;

import java.util.UUID;

/**
 * <AUTHOR>
 * @create 2024/9/30 13:43
 */
public interface MDCTraceIdSupport {

    default String getTraceName() {
        return "traceId";
    }

    /**
     * 获取traceId
     * @return
     */
    default String getTraceId() {
        return MDC.get(getTraceName());
    }

    /**
     * 设置traceId
     * @param traceId
     */
    default void setTraceId(String traceId) {
        MDC.put(getTraceName(), traceId);
    }


    /**
     * 生成traceId
     * @return
     */
    default String createTraceId() {
        String traceId = UUID.randomUUID().toString().replace("-", "");
        MDC.put(getTraceName(), traceId);
        return traceId;
    }

    /**
     * 清除MDC中的traceId
     */
    default void clearTraceId() {
        MDC.remove(getTraceName());
    }
}
