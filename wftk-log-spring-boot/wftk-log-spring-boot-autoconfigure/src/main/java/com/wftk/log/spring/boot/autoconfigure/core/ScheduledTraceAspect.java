package com.wftk.log.spring.boot.autoconfigure.core;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Aspect
public class ScheduledTraceAspect implements MDCTraceIdSupport {

    private static final Logger logger = LoggerFactory.getLogger(ScheduledTraceAspect.class);

    @Pointcut("execution(@org.springframework.scheduling.annotation.Scheduled * *(..))")
    public void scheduledMethods() {}

    @Around("scheduledMethods()")
    public Object aroundScheduledMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        String traceId = createTraceId();
        try {
            return joinPoint.proceed(args);
        } catch (Throwable e) {
            // 记录异常信息
            String methodName = joinPoint.getSignature().getName();
            logger.error("Error executing scheduled method: {}. args: {}. traceId: {}.", methodName, args, traceId, e);
            throw e;
        } finally {
            clearTraceId();
        }
    }

}
