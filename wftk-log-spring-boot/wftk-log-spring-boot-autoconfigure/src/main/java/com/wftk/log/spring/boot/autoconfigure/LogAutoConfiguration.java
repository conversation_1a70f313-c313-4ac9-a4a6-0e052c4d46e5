package com.wftk.log.spring.boot.autoconfigure;

import com.wftk.log.spring.boot.autoconfigure.core.MDCTaskDecorator;
import com.wftk.log.spring.boot.autoconfigure.core.ScheduledTraceAspect;
import com.wftk.log.spring.boot.autoconfigure.core.TraceFilter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2023/5/31 14:41
 */
@Configuration
public class LogAutoConfiguration {

    @ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
    @Bean
    FilterRegistrationBean<TraceFilter> traceFilterFilterRegistrationBean() {
        FilterRegistrationBean<TraceFilter> traceFilterFilterRegistrationBean = new FilterRegistrationBean<>();
        traceFilterFilterRegistrationBean.setFilter(new TraceFilter());
        traceFilterFilterRegistrationBean.setOrder(Integer.MIN_VALUE);
        traceFilterFilterRegistrationBean.addUrlPatterns("/*");
        return traceFilterFilterRegistrationBean;
    }

    @Bean
    MDCTaskDecorator mdcTaskDecorator() {
        return new MDCTaskDecorator();
    }

    @Bean
    ScheduledTraceAspect scheduledTraceAspect() {
        return new ScheduledTraceAspect();
    }

}
