<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.wftk</groupId>
    <artifactId>wftk-starters</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>wftk-starters</name>
    <description>wftk-starters</description>
    <modules>
        <module>wftk-http-client-spring-boot</module>
        <module>wftk-jackson-spring-boot</module>
        <module>wftk-namespace-spring-boot</module>
        <module>wftk-signature-spring-boot</module>
        <module>wftk-exception-spring-boot</module>
        <module>wftk-pageable-spring-boot</module>
        <module>common-core</module>
        <module>wftk-rabbitmq-spring-boot</module>
        <module>wftk-stateless-fsm</module>
        <module>wftk-opt-log-spring-boot</module>
        <module>wftk-sms-client-spring-boot</module>
        <module>wftk-log-spring-boot</module>
        <module>wftk-file-manager-spring-boot</module>
        <module>wftk-file-biz-spring-boot</module>
        <module>wftk-mybatis-spring-boot</module>
        <module>wftk-cache-spring-boot</module>
        <module>wftk-lock-spring-boot</module>
        <module>wftk-oauth-spring-boot</module>
        <module>wftk-message-spring-boot</module>
        <module>wftk-auth-spring-boot</module>
    </modules>
    <properties>
        <revision>3.2.10.5</revision>

        <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
        <maven-source-plugin.version>3.3.1</maven-source-plugin.version>
        <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>

        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <spring-boot.version>3.2.10</spring-boot.version>
        <lombok.version>1.18.30</lombok.version>
        <hutool.version>5.8.27</hutool.version>
        <redisson-spring-boot.starter.version>3.37.0</redisson-spring-boot.starter.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- hutool -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-extra</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.redisson/redisson-spring-boot-starter -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson-spring-boot.starter.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>


    <distributionManagement>
        <repository>
            <id>rdc-releases</id>
            <url>https://packages.aliyun.com/maven/repository/2261348-release-GKzXRW/</url>
        </repository>
        <snapshotRepository>
            <id>rdc-snapshots</id>
            <url>https://packages.aliyun.com/maven/repository/2261348-snapshot-rIoa5V/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <!-- 源码插件 -->
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven-source-plugin.version}</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- 多模块版本管理 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <encoding>UTF-8</encoding>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.springframework.boot</groupId>
                                <artifactId>spring-boot-configuration-processor</artifactId>
                                <version>${spring-boot.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <configuration>
                        <excludes>
                            <exclude>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                            </exclude>
                        </excludes>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

</project>
