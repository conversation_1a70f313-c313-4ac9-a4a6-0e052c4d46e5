package com.wftk.signature.spring.boot.autoconfigure.exception;

import com.wftk.exception.common.HttpStatusCode;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.translator.base.AbstractExceptionTranslator;

public class IllegalRequestExceptionTranslator extends AbstractExceptionTranslator {

    @Override
    public boolean support(Throwable t) {
        return t instanceof IllegalRequestException;
    }

    @Override
    protected ResolvedResult doTranslate(Throwable throwable) {
        return ResolvedResultBuilder.build(403, throwable.getMessage(), HttpStatusCode.FORBIDDEN);
    }

}
