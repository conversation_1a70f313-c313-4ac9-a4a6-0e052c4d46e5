package com.wftk.signature.spring.boot.autoconfigure.filter;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.util.StreamUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingResponseWrapper;

import com.wftk.common.core.request.matcher.AntIgnoredRequestMatcher;
import com.wftk.common.core.request.matcher.IgnoredRequestMatcher;
import com.wftk.common.core.request.wrapper.ContentCachingRequestWrapper;
import com.wftk.jackson.core.JSONObject;
import com.wftk.signature.encryption.EncryptPayloadHandler;
import com.wftk.signature.encryption.EncryptedPayload;
import com.wftk.signature.result.SignValidateResult;
import com.wftk.signature.spring.boot.autoconfigure.context.WebSignatureContext;
import com.wftk.signature.spring.boot.autoconfigure.exception.InvalidSignatureException;
import com.wftk.signature.spring.boot.autoconfigure.properties.SignProperties;
import com.wftk.signature.spring.boot.autoconfigure.validator.SignValidator;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class EncryptPayloadFilter extends OncePerRequestFilter {

    private final EncryptPayloadHandler encryptPayloadHandler;
    private final SignProperties signProperties;
    private final SignValidator signValidator;
    private final IgnoredRequestMatcher ignoredRequestMatcher;


    public EncryptPayloadFilter(EncryptPayloadHandler encryptPayloadHandler, SignProperties signProperties, SignValidator signValidator) {
        this.encryptPayloadHandler = encryptPayloadHandler;
        this.signProperties = signProperties;
        this.signValidator = signValidator;
        this.ignoredRequestMatcher = new AntIgnoredRequestMatcher(signProperties.getEncryptFilter().getIgnoredPatterns());
    }


    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        return ignoredRequestMatcher.ignore(request);
    }


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        String clientId = request.getParameter(signProperties.getClientIdNameParam());
        //包装response
        ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);
        try {
            String body = StreamUtils.copyToString(request.getInputStream(), StandardCharsets.UTF_8);
            //签名校验及解密
            String decryptedBody = signVerifyAndDecrypt(request, clientId, body);
            if (!(request instanceof ContentCachingRequestWrapper)) {
                request = new ContentCachingRequestWrapper(request, decryptedBody.getBytes(StandardCharsets.UTF_8));
            }
            // 继续处理请求
            filterChain.doFilter(request, responseWrapper);
        } finally {
            // 响应时加密 (handlerExceptionResolver.resolveException可能被全局异常处理，所以选择在finally中加密)
            byte[] responseBytes = responseWrapper.getContentAsByteArray();
            encryptAndSignResponse(responseWrapper, clientId, responseBytes);
            WebSignatureContext.reset();
        }
    }


    /**
     * 校验签名并解密请求体
     * @param request
     * @param clientId
     * @param body
     * @return
     */
    private String signVerifyAndDecrypt(HttpServletRequest request, String clientId, String body) {
        //校验签名
        SignValidateResult signValidateResult = signValidate(request, body);

        //解密请求体
        EncryptedPayload encryptedRequestBody = JSONObject.getInstance().parseObject(body, EncryptedPayload.class);
        String decryptedBody = encryptPayloadHandler.decrypt(clientId, encryptedRequestBody);
        //设置客户端信息
        WebSignatureContext.set(signValidateResult.getClientInfo());
        return decryptedBody;
    }


    /**
     * 响应时加密
     * @param responseWrapper
     * @param clientId
     * @param responseBytes
     * @throws IOException
     */
    private void encryptAndSignResponse(ContentCachingResponseWrapper responseWrapper, String clientId, byte[] responseBytes) throws IOException {
        String content = new String(responseBytes, StandardCharsets.UTF_8);
        EncryptedPayload encryptedBody = encryptPayloadHandler.encryptAndSignResponse(clientId, content);
        byte[] resultBytes = JSONObject.getInstance().toJSONString(encryptedBody).getBytes(StandardCharsets.UTF_8);

        // 清空 responseWrapper 中之前的内容，避免内容重复
        responseWrapper.resetBuffer();
        // 设置响应头：Content-Type
        responseWrapper.setContentType("application/json;charset=UTF-8");
        // 将加密后的JSON数据写入 responseWrapper 的缓冲区
        responseWrapper.getOutputStream().write(resultBytes);
        responseWrapper.copyBodyToResponse();
    }


    /**
     * 校验签名
     * @param request
     * @param body
     */
    private SignValidateResult signValidate(HttpServletRequest request, String body) {
        //校验签名
        Map<String, String> urlParamsMap = getParamsMap(request);
        //添加body参数
        urlParamsMap.put("body", body);
        SignValidateResult validateResult = signValidator.validate(urlParamsMap);
        if (!validateResult.isValid()) {
            throw new InvalidSignatureException(validateResult);
        }
        return validateResult;
    }


    /**
     * 获取url参数
     * @param request
     * @return
     */
    private Map<String, String> getParamsMap(HttpServletRequest request) {
        return request.getParameterMap().entrySet()
        .stream()
        .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue()[0]));
    }

}
