package com.wftk.signature.spring.boot.autoconfigure;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerExceptionResolver;

import com.wftk.signature.loader.CompositeClientCredentialLoader;
import com.wftk.signature.spring.boot.autoconfigure.filter.IPFilter;
import com.wftk.signature.spring.boot.autoconfigure.properties.SignProperties;

@Configuration
public class IpFilterConfiguration {


    @ConditionalOnProperty(prefix = "wftk.signature.ip-filter", name = "enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
    @Bean
    FilterRegistrationBean<IPFilter> ipFilterRegistrationBean(SignProperties signProperties,
                                            CompositeClientCredentialLoader compositeClientCredentialLoader,
                                            @Qualifier("handlerExceptionResolver") HandlerExceptionResolver handlerExceptionResolver) {
        FilterRegistrationBean<IPFilter> ipFilterRegistrationBean = new FilterRegistrationBean<>();
        SignProperties.FilterConfig filterConfig = signProperties.getIpFilter();
        IPFilter ipFilter = new IPFilter(signProperties, compositeClientCredentialLoader, handlerExceptionResolver);
        ipFilterRegistrationBean.setFilter(ipFilter);
        ipFilterRegistrationBean.setName(filterConfig.getName());
        ipFilterRegistrationBean.setOrder(filterConfig.getOrder());
        ipFilterRegistrationBean.setUrlPatterns(filterConfig.getUrlPatterns());
        return ipFilterRegistrationBean;
    }

}
