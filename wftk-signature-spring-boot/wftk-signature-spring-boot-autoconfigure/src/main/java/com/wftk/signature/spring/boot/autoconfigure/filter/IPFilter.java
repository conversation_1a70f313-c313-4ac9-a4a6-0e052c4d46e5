/*
 * @Author: dy
 * @Date: 2025-06-06 17:08:50
 * @LastEditors: dy
 * @LastEditTime: 2025-06-12 17:18:59
 * @Description: 
 */
package com.wftk.signature.spring.boot.autoconfigure.filter;

import java.io.IOException;
import java.util.Set;

import org.springframework.lang.NonNull;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.HandlerExceptionResolver;

import com.wftk.common.core.request.matcher.AntIgnoredRequestMatcher;
import com.wftk.common.core.request.matcher.IgnoredRequestMatcher;
import com.wftk.common.core.util.IPUtil;
import com.wftk.signature.builder.ClientInfo;
import com.wftk.signature.loader.ClientCredentialLoader;
import com.wftk.signature.spring.boot.autoconfigure.exception.IllegalRequestException;
import com.wftk.signature.spring.boot.autoconfigure.properties.SignProperties;

import cn.hutool.core.collection.CollUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class IPFilter extends OncePerRequestFilter {

    private final SignProperties signProperties;
    private final ClientCredentialLoader clientCredentialLoader;
    private final IgnoredRequestMatcher ignoredRequestMatcher;
    private final HandlerExceptionResolver handlerExceptionResolver;


    public IPFilter(SignProperties signProperties, ClientCredentialLoader clientCredentialLoader,
                    HandlerExceptionResolver handlerExceptionResolver) {
        this.signProperties = signProperties;
        this.clientCredentialLoader = clientCredentialLoader;
        this.ignoredRequestMatcher = new AntIgnoredRequestMatcher(signProperties.getIpFilter().getIgnoredPatterns());
        this.handlerExceptionResolver = handlerExceptionResolver;
    }

    @Override
    protected boolean shouldNotFilter(@NonNull HttpServletRequest request) {
        return ignoredRequestMatcher.ignore(request);
    }

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull FilterChain filterChain) throws ServletException {
        String clientId = request.getParameter(signProperties.getClientIdNameParam());
        ClientInfo clientInfo = clientCredentialLoader.get(clientId);
        if (clientInfo == null) {
            log.warn("client info not found, clientId: {}", clientId);
            handlerExceptionResolver.resolveException(request, response, null, new IllegalRequestException("client info not found"));
            return;
        }
        String requestIp = IPUtil.getRequestIp(request);
        Set<String> clientIps = clientInfo.getIps();
        if (IPUtil.isInternalIp(requestIp)) {
            log.info("request ip is internal ip, requestIp: {}, ignore...", requestIp);
        } else {
            if (CollUtil.isNotEmpty(clientIps) && !IPUtil.isIpInCidr(requestIp, clientIps)) {
                log.warn("ip not allowed, clientId: {}, requestIp: {}", clientId, requestIp);
                handlerExceptionResolver.resolveException(request, response, null, new IllegalRequestException("ip not allowed"));
                return;
            }
        }
        try {
            filterChain.doFilter(request, response);
        } catch (IOException | ServletException e) {
            log.error("do filter internal error.", e);
            handlerExceptionResolver.resolveException(request, response, null, new ServletException(e));
            return;
        }
    }

}
