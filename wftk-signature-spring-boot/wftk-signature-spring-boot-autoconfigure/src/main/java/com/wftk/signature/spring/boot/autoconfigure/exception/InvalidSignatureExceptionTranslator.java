package com.wftk.signature.spring.boot.autoconfigure.exception;

import com.wftk.exception.common.HttpStatusCode;
import com.wftk.exception.common.result.ResolvedResult;
import com.wftk.exception.common.result.ResolvedResultBuilder;
import com.wftk.exception.core.translator.base.AbstractExceptionTranslator;
import com.wftk.signature.result.SignValidateResult;

/**
 * <AUTHOR>
 * @create 2023/4/4 11:48
 */

public class InvalidSignatureExceptionTranslator extends AbstractExceptionTranslator {
    @Override
    protected ResolvedResult doTranslate(Throwable throwable) {
        InvalidSignatureException invalidSignatureException = (InvalidSignatureException) throwable;
        SignValidateResult signValidateResult = invalidSignatureException.getSignValidateResult();
        return ResolvedResultBuilder.build(signValidateResult.getCode(), signValidateResult.getMessage(), HttpStatusCode.FORBIDDEN);
    }

    @Override
    public boolean support(Throwable throwable) {
        return throwable instanceof InvalidSignatureException;
    }
}
