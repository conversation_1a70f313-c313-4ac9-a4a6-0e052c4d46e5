package com.wftk.signature.spring.boot.autoconfigure;


import com.wftk.exception.core.translator.base.ExceptionTranslator;
import com.wftk.signature.spring.boot.autoconfigure.properties.SignProperties;
import com.wftk.signature.spring.boot.autoconfigure.exception.InvalidSignatureExceptionTranslator;
import com.wftk.signature.spring.boot.autoconfigure.filter.SignatureFilter;
import com.wftk.signature.spring.boot.autoconfigure.validator.SignValidator;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerExceptionResolver;

/**
 * <AUTHOR>
 * @create 2023/4/4 10:43
 */
@Configuration
public class SignatureFilterConfiguration {

    @ConditionalOnProperty(prefix = "wftk.signature.filter", name = "enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
    @Bean
    FilterRegistrationBean<SignatureFilter> signatureFilterFilterRegistrationBean(SignValidator signValidator, SignProperties signProperties,
                                                                                  @Qualifier("handlerExceptionResolver") HandlerExceptionResolver handlerExceptionResolver) {
        FilterRegistrationBean<SignatureFilter> signatureFilterFilterRegistrationBean = new FilterRegistrationBean<>();
        SignProperties.FilterConfig filterConfig = signProperties.getFilter();
        SignatureFilter signatureFilter = new SignatureFilter(signValidator, filterConfig.getIgnoredPatterns(), handlerExceptionResolver);
        signatureFilterFilterRegistrationBean.setFilter(signatureFilter);
        signatureFilterFilterRegistrationBean.setName(filterConfig.getName());
        signatureFilterFilterRegistrationBean.setOrder(filterConfig.getOrder());
        signatureFilterFilterRegistrationBean.setUrlPatterns(filterConfig.getUrlPatterns());
        return signatureFilterFilterRegistrationBean;
    }


    @ConditionalOnClass(ExceptionTranslator.class)
    @Bean
    InvalidSignatureExceptionTranslator invalidSignatureExceptionTranslator() {
        return new InvalidSignatureExceptionTranslator();
    }

}
