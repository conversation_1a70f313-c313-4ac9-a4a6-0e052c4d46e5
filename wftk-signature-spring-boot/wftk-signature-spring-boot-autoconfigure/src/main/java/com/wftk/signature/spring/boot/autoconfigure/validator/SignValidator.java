package com.wftk.signature.spring.boot.autoconfigure.validator;

import java.util.Map;

import com.wftk.signature.result.SignValidateResult;
import jakarta.servlet.http.HttpServletRequest;


/**
 * API签名校验
 * @Author: ying.dong
 * @Date: 2021/7/6 15:43
 */
public interface SignValidator {

    /**
     * 校验参数
     * @param request
     * @return
     */
    SignValidateResult validate(HttpServletRequest request);


    /**
     * 校验参数
     * @param paramsMap
     * @return
     */
    SignValidateResult validate(Map<String, String> paramsMap);
}
