package com.wftk.signature.spring.boot.autoconfigure.exception;


import com.wftk.signature.result.SignValidateResult;

import java.io.Serial;

/**
 * <AUTHOR>
 * @create 2023/4/4 11:01
 */
public class InvalidSignatureException extends RuntimeException {
    @Serial
    private static final long serialVersionUID = 5751580187417849828L;

    private final SignValidateResult signValidateResult;

    public InvalidSignatureException(SignValidateResult signValidateResult) {
        super(signValidateResult.getMessage());
        this.signValidateResult = signValidateResult;
    }

    public SignValidateResult getSignValidateResult() {
        return signValidateResult;
    }
}
