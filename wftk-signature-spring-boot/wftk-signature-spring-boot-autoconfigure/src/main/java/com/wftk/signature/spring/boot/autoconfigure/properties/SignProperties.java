package com.wftk.signature.spring.boot.autoconfigure.properties;

import com.wftk.signature.builder.ClientInfo;
import com.wftk.signature.config.NonceConfig;

import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Author: ying.dong
 * @Date: 2021/7/6 16:10
 */
@Data
@ConfigurationProperties(prefix = SignProperties.CONFIG_PREFIX)
public class SignProperties {

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    public static final String CONFIG_PREFIX = "config.signature";

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    public static final Long DEFAULT_EXPIRE_IN_MILLS = 8000L;

    /**
     * 签名有效期(毫秒)
     */
    private Long expireInMills = DEFAULT_EXPIRE_IN_MILLS;

    /**
     *
     */
    private String clientIdNameParam = "client_id";

    /**
     * filter配置
     */
    @NestedConfigurationProperty
    private FilterConfig filter = new FilterConfig();

    @NestedConfigurationProperty
    private FilterConfig encryptFilter = new FilterConfig("encryptPayloadFilter", Integer.MAX_VALUE - 20020, 
                                            new HashSet<>(){{add("/*");}}, Collections.emptySet());

    @NestedConfigurationProperty
    private FilterConfig ipFilter = new FilterConfig("ipFilter", Integer.MAX_VALUE - 20010, 
                                            new HashSet<>(){{add("/*");}}, Collections.emptySet());

    @NestedConfigurationProperty
    private NonceConfig nonce = new NonceConfig();

    @NestedConfigurationProperty
    private List<ClientInfo> clients;



    /**
     * filter配置
     */
    @Data
    public static class FilterConfig {

        @Getter(AccessLevel.NONE)
        @Setter(AccessLevel.NONE)
        private static final String DEFAULT_FILTER_NAME = "signatureFilter";

        @Getter(AccessLevel.NONE)
        @Setter(AccessLevel.NONE)
        private static final Integer DEFAULT_ORDER = Integer.MAX_VALUE - 20000;

        @Getter(AccessLevel.NONE)
        @Setter(AccessLevel.NONE)
        private static final Set<String> DEFAULT_URL_PATTERNS = new HashSet<>() {{
            add("/*");
        }};


        public FilterConfig() {
            this.name = DEFAULT_FILTER_NAME;
            this.order = DEFAULT_ORDER;
            this.urlPatterns = DEFAULT_URL_PATTERNS;
            this.ignoredPatterns = Collections.emptySet();
        }

        public FilterConfig(String name, Integer order, Set<String> urlPatterns, Set<String> ignoredPatterns) {
            this.name = name;
            this.order = order;
            this.urlPatterns = urlPatterns;
            this.ignoredPatterns = ignoredPatterns;
        }

        /**
         * 是否启用
         */
        private boolean enabled = true;

        /**
         * filter实例名
         */
        private String name;

        /**
         * filter顺序
         */
        private Integer order;

        /**
         * 拦截路径
         */
        private Set<String> urlPatterns;

        /**
         * 忽略路径
         */
        private Set<String> ignoredPatterns;

    }

}
