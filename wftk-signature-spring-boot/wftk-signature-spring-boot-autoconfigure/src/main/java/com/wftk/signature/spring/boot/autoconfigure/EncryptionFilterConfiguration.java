package com.wftk.signature.spring.boot.autoconfigure;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.wftk.signature.builder.factory.SignBuilderFactory;
import com.wftk.signature.encryption.DefaultEncryptPayloadHandler;
import com.wftk.signature.encryption.EncryptPayloadHandler;
import com.wftk.signature.loader.CompositeClientCredentialLoader;
import com.wftk.signature.spring.boot.autoconfigure.filter.EncryptPayloadFilter;
import com.wftk.signature.spring.boot.autoconfigure.properties.SignProperties;
import com.wftk.signature.spring.boot.autoconfigure.validator.SignValidator;

@Configuration
public class EncryptionFilterConfiguration {

    @ConditionalOnProperty(prefix = "wftk.signature.encrypt-filter", name = "enabled", havingValue = "true", matchIfMissing = true)
    @ConditionalOnMissingBean
    @Bean
    EncryptPayloadHandler encryptPayloadHandler(CompositeClientCredentialLoader compositeClientCredentialLoader, SignBuilderFactory<?> signBuilderFactory) {
        return new DefaultEncryptPayloadHandler(compositeClientCredentialLoader, signBuilderFactory);
    }

    @ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
    @Bean
    FilterRegistrationBean<EncryptPayloadFilter> encryptionFilterRegistrationBean(SignValidator signValidator, 
                                            SignProperties signProperties,
                                            EncryptPayloadHandler encryptPayloadHandler) {
        FilterRegistrationBean<EncryptPayloadFilter> encryptionFilterRegistrationBean = new FilterRegistrationBean<>();
        SignProperties.FilterConfig filterConfig = signProperties.getEncryptFilter();
        EncryptPayloadFilter encryptionFilter = new EncryptPayloadFilter(encryptPayloadHandler, signProperties, signValidator);
        encryptionFilterRegistrationBean.setFilter(encryptionFilter);
        encryptionFilterRegistrationBean.setName(filterConfig.getName());
        encryptionFilterRegistrationBean.setOrder(filterConfig.getOrder());
        encryptionFilterRegistrationBean.setUrlPatterns(filterConfig.getUrlPatterns());
        return encryptionFilterRegistrationBean;
    }

}
