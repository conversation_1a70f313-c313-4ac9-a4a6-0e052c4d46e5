package com.wftk.signature.spring.boot.autoconfigure;

import com.wftk.signature.builder.SignBuilder;
import com.wftk.signature.builder.factory.DefaultSignBuilderFactory;
import com.wftk.signature.builder.factory.SignBuilderFactory;
import com.wftk.signature.repository.SignHistoryRepository;
import com.wftk.signature.loader.ClientCredentialLoader;
import com.wftk.signature.loader.CompositeClientCredentialLoader;
import com.wftk.signature.loader.PropertiesClientCredentialLoader;
import com.wftk.signature.spring.boot.autoconfigure.exception.IllegalRequestExceptionTranslator;
import com.wftk.signature.spring.boot.autoconfigure.properties.SignProperties;
import com.wftk.signature.spring.boot.autoconfigure.validator.DefaultSignValidator;
import com.wftk.signature.spring.boot.autoconfigure.validator.SignValidator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * @Author: ying.dong
 * @Date: 2021/11/3 16:58
 */
@Configuration
@EnableConfigurationProperties(SignProperties.class)
public class SignatureAutoConfiguration {

    @Bean
    PropertiesClientCredentialLoader propertiesClientCredentialLoader(SignProperties signProperties) {
        return new PropertiesClientCredentialLoader(signProperties.getClients());
    }

    @Bean
    @ConditionalOnMissingBean
    CompositeClientCredentialLoader compositeClientCredentialLoader(List<ClientCredentialLoader> clientCredentialLoaders) {
        return new CompositeClientCredentialLoader(clientCredentialLoaders);
    }


    @Bean
    @ConditionalOnBean(SignHistoryRepository.class)
    @ConditionalOnMissingBean
    SignValidator signValidator(SignProperties signProperties, CompositeClientCredentialLoader compositeClientCredentialLoader,
                                SignHistoryRepository signHistoryRepository) {
        return new DefaultSignValidator(signProperties.getNonce(), signProperties.getExpireInMills(), compositeClientCredentialLoader,
                signHistoryRepository, signProperties.getClientIdNameParam());
    }

    @Bean
    @ConditionalOnMissingBean
    SignValidator defaultSignValidator(SignProperties signProperties, CompositeClientCredentialLoader compositeClientCredentialLoader) {
        return new DefaultSignValidator(signProperties.getNonce(), signProperties.getExpireInMills(), compositeClientCredentialLoader,
                null, signProperties.getClientIdNameParam());
    }

    @Bean
    @ConditionalOnMissingBean
    SignBuilderFactory<? extends SignBuilder> signBuilderFactory(SignProperties signProperties) {
        return new DefaultSignBuilderFactory(signProperties.getClients());
    }


    @Bean
    IllegalRequestExceptionTranslator illegalRequestExceptionTranslator() {
        return new IllegalRequestExceptionTranslator();
    }
}
