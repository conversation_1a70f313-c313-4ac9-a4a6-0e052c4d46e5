package com.wftk.signature.spring.boot.autoconfigure.validator;


import cn.hutool.core.util.StrUtil;

import java.util.Map;

import com.wftk.signature.builder.ClientInfo;
import com.wftk.signature.builder.DefaultSignBuilder;
import com.wftk.signature.builder.SignBuilder;
import com.wftk.signature.config.NonceConfig;
import com.wftk.signature.loader.ClientCredentialLoader;
import com.wftk.signature.repository.SignHistoryRepository;
import com.wftk.signature.result.DefaultSignValidateResult;
import com.wftk.signature.result.SignValidateResult;
import com.wftk.signature.spring.boot.autoconfigure.builder.HttpRequestSignBuilder;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;


/**
 * @Author: ying.dong
 * @Date: 2021/7/6 15:54
 */
@Slf4j
public class DefaultSignValidator implements SignValidator {

    private final NonceConfig nonceConfig;
    private final Long expireInMills;
    private final ClientCredentialLoader clientCredentialLoader;
    private final SignHistoryRepository signHistoryRepository;
    private final String clientIdParamName;


    private static final String CLIENT_ID = "client_id";
    private static final String TIMESTAMP = "timestamp";

    public DefaultSignValidator(NonceConfig nonceConfig, Long expireInMills, ClientCredentialLoader clientCredentialLoader) {
        this(nonceConfig, expireInMills, clientCredentialLoader, null, CLIENT_ID);
    }

    public DefaultSignValidator(NonceConfig nonceConfig, Long expireInMills, ClientCredentialLoader clientCredentialLoader, SignHistoryRepository repository) {
        this(nonceConfig, expireInMills, clientCredentialLoader, repository, CLIENT_ID);
    }

    public DefaultSignValidator(NonceConfig nonceConfig, Long expireInMills, ClientCredentialLoader clientCredentialLoader,
                                SignHistoryRepository repository, String clientIdParamName) {
        this.nonceConfig = nonceConfig;
        this.expireInMills = expireInMills;
        this.clientCredentialLoader = clientCredentialLoader;
        this.signHistoryRepository = repository;
        this.clientIdParamName = clientIdParamName;
    }

    @Override
    public SignValidateResult validate(HttpServletRequest request) {
        try {
            String sign = request.getParameter("sign");
            String clientId = request.getParameter(clientIdParamName);
            String nonce = request.getParameter(nonceConfig.getName());
            String timestamp = request.getParameter(TIMESTAMP);
            SignValidateResult signValidateResult = doValidate(clientId, nonce, timestamp, sign);
            if (!signValidateResult.isValid()) {
                return signValidateResult;
            }
            return signValidate(request, clientCredentialLoader.get(clientId), sign);
        } catch (Exception e) {
            log.warn("do validate signature error.", e);
            return DefaultSignValidateResult.fail();
        }
    }

    @Override
    public SignValidateResult validate(Map<String, String> paramsMap) {
        try {
            String sign = paramsMap.get("sign");
            String clientId = paramsMap.get(clientIdParamName);
            String nonce = paramsMap.get(nonceConfig.getName());
            String timestamp = paramsMap.get(TIMESTAMP);
            SignValidateResult signValidateResult = doValidate(clientId, nonce, timestamp, sign);
            if (!signValidateResult.isValid()) {
                return signValidateResult;
            }
            return signValidate(paramsMap, clientCredentialLoader.get(clientId), sign);
        } catch (Exception e) {
            log.warn("do validate signature error.", e);
            return DefaultSignValidateResult.fail();
        }
    }


    /**
     * 校验参数
     * @param clientId
     * @param nonce
     * @param timestamp
     * @param sign
     * @return
     */
    private SignValidateResult doValidate(String clientId, String nonce, String timestamp, String sign) {
        
        //1. 未携带sign参数的直接拒绝
        if (StrUtil.isBlank(sign)) {
            return DefaultSignValidateResult.fail("sign must not be null");
        }

        //2. client_id校验
        if (StrUtil.isBlank(clientId)) {
            return DefaultSignValidateResult.fail(clientIdParamName + " must not be null");
        }
        ClientInfo clientInfo = clientCredentialLoader.get(clientId);
        if (clientInfo == null) {
            return DefaultSignValidateResult.fail("illegal client_id: " + clientId);
        }

        //3. 重放校验
        SignValidateResult signValidateResult = repeatableValidate(nonce, timestamp);
        if (!signValidateResult.isValid()) {
            return signValidateResult;
        }
        return DefaultSignValidateResult.SUCCESS;
    }


    /**
     * 重放校验
     * @param request
     * @return
     */
    private SignValidateResult repeatableValidate(String nonce, String timestamp) {
        if (nonceConfig.isEnable()) {
            if (StrUtil.isBlank(nonce)) {
                return DefaultSignValidateResult.fail("nonce must not be null.");
            }
            if (signHistoryRepository.isExists(nonce)) {
                return DefaultSignValidateResult.fail("repeatable validate failed with nonce [" + nonce + "]");
            }
            signHistoryRepository.save(nonce, nonceConfig.getExpireInMills());
        }
        if (StrUtil.isBlank(timestamp)) {
            return DefaultSignValidateResult.fail("timestamp must not be null");
        }
        if (System.currentTimeMillis() - Long.parseLong(timestamp) > expireInMills) {
            return DefaultSignValidateResult.fail("timestamp timeout.");
        }
        return DefaultSignValidateResult.SUCCESS;
    }

    /**
     * 签名校验
     * @param request
     * @param clientInfo
     * @param sign
     * @return
     */
    private SignValidateResult signValidate(HttpServletRequest request, ClientInfo clientInfo, String sign) {
        String serverSign = getSignBuilder(clientInfo, request).build();
        if (!serverSign.equalsIgnoreCase(sign)) {
            return DefaultSignValidateResult.fail("invalid sign");
        }
        return DefaultSignValidateResult.success().clientInfo(clientInfo);
    }


    /**
     * 签名校验
     * @param paramsMap
     * @param clientInfo
     * @param sign
     * @return
     */
    private SignValidateResult signValidate(Map<String, String> paramsMap, ClientInfo clientInfo, String sign) {
        SignBuilder signBuilder = new DefaultSignBuilder(clientInfo.getClientSecret(), clientInfo.getDefaultAlgorithm());
        paramsMap.forEach(signBuilder::addParam);
        String serverSign = signBuilder.build();
        if (!serverSign.equalsIgnoreCase(sign)) {
            return DefaultSignValidateResult.fail("invalid sign");
        }
        return DefaultSignValidateResult.success().clientInfo(clientInfo);
    }


    /**
     * @param clientInfo
     * @return
     */
    private SignBuilder getSignBuilder(ClientInfo clientInfo, HttpServletRequest request) {
        String algorithm = request.getParameter("algorithm");
        if (StrUtil.isBlank(algorithm)) {
            algorithm = clientInfo.getDefaultAlgorithm();
        }
        return new HttpRequestSignBuilder(clientInfo.getClientSecret(), request, algorithm);
    }

}
