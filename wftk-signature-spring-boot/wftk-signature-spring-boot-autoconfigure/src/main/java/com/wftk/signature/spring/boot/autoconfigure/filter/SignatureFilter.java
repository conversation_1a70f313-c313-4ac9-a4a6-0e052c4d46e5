package com.wftk.signature.spring.boot.autoconfigure.filter;

import com.wftk.common.core.request.matcher.AntIgnoredRequestMatcher;
import com.wftk.common.core.request.matcher.IgnoredRequestMatcher;
import com.wftk.common.core.request.wrapper.ContentCachingRequestWrapper;
import com.wftk.signature.result.SignValidateResult;
import com.wftk.signature.spring.boot.autoconfigure.context.WebSignatureContext;
import com.wftk.signature.spring.boot.autoconfigure.exception.InvalidSignatureException;
import com.wftk.signature.spring.boot.autoconfigure.validator.SignValidator;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.HandlerExceptionResolver;

import java.io.IOException;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2023/2/3 14:00
 */
@Slf4j
public class SignatureFilter extends OncePerRequestFilter {

    private final SignValidator signValidator;
    private final IgnoredRequestMatcher ignoredRequestMatcher;
    private final HandlerExceptionResolver handlerExceptionResolver;

    public SignatureFilter(SignValidator signValidator, Set<String> ignorePatterns, HandlerExceptionResolver handlerExceptionResolver) {
        this(signValidator, new AntIgnoredRequestMatcher(ignorePatterns), handlerExceptionResolver);
    }

    public SignatureFilter(SignValidator signValidator, IgnoredRequestMatcher ignoredRequestMatcher, HandlerExceptionResolver handlerExceptionResolver) {
        this.signValidator = signValidator;
        this.ignoredRequestMatcher = ignoredRequestMatcher;
        this.handlerExceptionResolver = handlerExceptionResolver;
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        return ignoredRequestMatcher.ignore(request);
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        if (!(request instanceof ContentCachingRequestWrapper)) {
            request = new ContentCachingRequestWrapper(request);
        }
        SignValidateResult validateResult = signValidator.validate(request);
        if (!validateResult.isValid()) {
            log.warn("invalid signature, uri: {}", request.getRequestURI());
            //异常传递到MVC
            handlerExceptionResolver.resolveException(request, response, null, new InvalidSignatureException(validateResult));
            return;
        }
        try {
            WebSignatureContext.set(validateResult.getClientInfo());
            filterChain.doFilter(request, response);
        } finally {
            WebSignatureContext.reset();
        }
    }

}
