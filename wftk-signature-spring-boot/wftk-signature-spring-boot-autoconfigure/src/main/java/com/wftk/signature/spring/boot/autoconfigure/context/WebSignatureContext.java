package com.wftk.signature.spring.boot.autoconfigure.context;

import com.wftk.signature.builder.ClientInfo;

/**
 * <AUTHOR>
 * @create 2023/10/18 09:56
 */
public class WebSignatureContext {

    private static final ThreadLocal<ClientInfo> context = new ThreadLocal<>();

    public static void set(ClientInfo clientInfo) {
        context.set(clientInfo);
    }

    public static ClientInfo get() {
        return context.get();
    }


    public static void reset() {
        context.remove();
    }
}
