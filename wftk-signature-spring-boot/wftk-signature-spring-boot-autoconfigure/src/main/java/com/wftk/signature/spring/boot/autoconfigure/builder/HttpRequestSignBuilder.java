package com.wftk.signature.spring.boot.autoconfigure.builder;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.signature.builder.BaseSignBuilder;
import com.wftk.signature.enums.SignAlgorithmEnum;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.Serial;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: ying.dong
 * @Date: 2021/11/3 19:18
 */
@Slf4j
public class HttpRequestSignBuilder extends BaseSignBuilder {

    @Serial
    private static final long serialVersionUID = 661116651853927766L;
    private final HttpServletRequest request;

    public HttpRequestSignBuilder(String secret, HttpServletRequest request) {
        this(secret, request, 
        StrUtil.isBlank(request.getParameter("algorithm")) ? SignAlgorithmEnum.MD5.getValue() : request.getParameter("algorithm"));
    }

    public HttpRequestSignBuilder(String secret, HttpServletRequest request, String algorithm) {
        super(secret, algorithm);
        this.request = request;
    }

    @Override
    public String build() {
        signBuilderParams();
        signBuilderBody();
        return super.build();
    }

    /**
     * 参数
     */
    private void signBuilderParams() {
        Map<String, String[]> parameterMap = request.getParameterMap();
        if (!CollectionUtil.isEmpty(parameterMap)) {
            Map<String, Object> urlParams = parameterMap.entrySet().stream()
                    .filter(it -> {
                        if (ArrayUtil.isEmpty(it.getValue())) {
                            return false;
                        }
                        return StrUtil.isNotBlank(it.getValue()[0]);
                    })
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> StrUtil.join(",", (Object[]) entry.getValue())));
            addParams(urlParams);
        }
    }

    /**
     * body
     */
    private void signBuilderBody() {
        String contentType = request.getContentType();
        if (!request.getMethod().equalsIgnoreCase("GET") && StrUtil.isNotBlank(contentType)
                && contentType.toLowerCase().contains("application/json")) {
            //json
            try(InputStream in = request.getInputStream()) {
                String jsonBody = IoUtil.read(in, StandardCharsets.UTF_8);
                if (StrUtil.isNotBlank(jsonBody)) {
                    addParam("body", jsonBody);
                }
            } catch (IOException e) {
                log.error("failed to get json body.", e);
            }
        }
    }


}
