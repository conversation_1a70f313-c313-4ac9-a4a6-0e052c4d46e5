<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wftk</groupId>
        <artifactId>wftk-starters</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>wftk-signature-spring-boot</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>wftk-signature-spring-boot-autoconfigure</module>
        <module>wftk-signature</module>
        <module>wftk-signature-spring-boot-starter</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <jakarta.servlet.version>5.0.0</jakarta.servlet.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-crypto</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.servlet</groupId>
                <artifactId>jakarta.servlet-api</artifactId>
                <version>${jakarta.servlet.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>