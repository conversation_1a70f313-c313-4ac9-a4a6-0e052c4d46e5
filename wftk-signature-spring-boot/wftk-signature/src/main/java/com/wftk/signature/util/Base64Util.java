package com.wftk.signature.util;

import java.util.Base64;

public class Base64Util {

    /**
     * Base64编码
     * 
     * @param data 待编码的数据
     * @return Base64编码后的字符串
     */
    public static String encodeBase64(byte[] data) {
        return Base64.getEncoder().encodeToString(data);
    }

    /**
     * Base64解码
     * 
     * @param data Base64编码的字符串
     * @return 解码后的字节数组
     */
    public static byte[] decodeBase64(String data) {
        return Base64.getDecoder().decode(data);
    }
}
