package com.wftk.signature.builder;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.Serial;
import java.util.Map;
import java.util.TreeMap;

import com.wftk.signature.enums.SignAlgorithmEnum;

/**
 * 签名构造器
 * @Author: ying.dong
 * @Date: 2021/7/7 17:01
 */
@Slf4j
public class BaseSignBuilder extends TreeMap<String, Object> implements SignBuilder {

    @Serial
    private static final long serialVersionUID = 2157764791862820404L;
    protected final String secret;
    protected final SignAlgorithmEnum algorithm;

    public BaseSignBuilder(String secret) {
        this(secret, SignAlgorithmEnum.MD5.getValue());
    }

    public BaseSignBuilder(String secret, String algorithm) {
        this.secret = secret;
        this.algorithm = SignAlgorithmEnum.fromValue(algorithm);
    }

    @Override
    public SignBuilder addParam(String name, Object param) {
        put(name, param);
        return this;
    }

    @Override
    public SignBuilder addParams(Map<String, Object> params) {
        putAll(params);
        return this;
    }

    @Override
    public SignBuilder addObject(Object object) {
        Map<String, Object> propertiesMap = BeanUtil.beanToMap(object, false, true);
        putAll(propertiesMap);
        return this;
    }

    @Override
    public SignBuilder removeParam(String name) {
        remove(name);
        return this;
    }

    @Override
    public String build() {
        //移除sign
        remove("sign");
        String params = StrUtil.join("&", entrySet()) + secret;
        log.info("sign before: [{}]", params);
        String sign = getSign(params);
        log.info("after sign: [{}], algorithm: [{}]", sign, algorithm.getValue());
        return sign;
    }


    /**
     * 获取签名
     * @param params
     * @return
     */
    protected String getSign(String params) {
        switch (algorithm) {
            case MD5:
                return SecureUtil.md5(params);
            case SHA1:
                return SecureUtil.sha1(params);
            case SHA256:
                return SecureUtil.sha256(params);
            default:
                throw new RuntimeException("invalid algorithm: " + algorithm);
        }
    }
}
