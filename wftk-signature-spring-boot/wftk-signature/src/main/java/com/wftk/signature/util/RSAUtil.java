package com.wftk.signature.util;

import javax.crypto.Cipher;

import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

import com.wftk.signature.exception.EncryptionException;

/**
 * RSA加密工具类
 * 使用RSA/ECB/OAEPWithSHA-256AndMGF1Padding模式
 * 
 */
public class RSAUtil {

    private static final String ALGORITHM = "RSA";
    private static final String TRANSFORMATION = "RSA/ECB/OAEPWithSHA-256AndMGF1Padding";
    private static final int KEY_SIZE = 2048;

    /**
     * 生成RSA密钥对
     * 
     * @return RSA密钥对
     */
    public static KeyPair generateKeyPair() {
        try {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(ALGORITHM);
            keyPairGenerator.initialize(KEY_SIZE);
            return keyPairGenerator.generateKeyPair();
        } catch (Exception e) {
            throw new EncryptionException("生成RSA密钥对失败", e);
        }
    }

    /**
     * 公钥加密
     * 
     * @param data 待加密的数据
     * @param publicKey 公钥
     * @return 加密后的字节数组
     */
    public static byte[] encrypt(byte[] data, PublicKey publicKey) {
        try {
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            return cipher.doFinal(data);
        } catch (Exception e) {
            throw new EncryptionException("RSA公钥加密失败", e);
        }
    }

    /**
     * 私钥解密
     * 
     * @param encryptedData 加密的数据
     * @param privateKey 私钥
     * @return 解密后的字节数组
     */
    public static byte[] decrypt(byte[] encryptedData, PrivateKey privateKey) {
        try {
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            return cipher.doFinal(encryptedData);
        } catch (Exception e) {
            throw new EncryptionException("RSA私钥解密失败", e);
        }
    }

    /**
     * 公钥加密并返回Base64编码的字符串
     * 
     * @param data 待加密的数据
     * @param publicKey 公钥
     * @return Base64编码的加密字符串
     */
    public static String encryptToBase64(byte[] data, PublicKey publicKey) {
        byte[] encryptedData = encrypt(data, publicKey);
        return Base64Util.encodeBase64(encryptedData);
    }

    /**
     * 解密Base64编码的RSA加密数据
     * 
     * @param base64Data Base64编码的加密数据
     * @param privateKey 私钥
     * @return 解密后的字节数组
     */
    public static byte[] decryptFromBase64(String base64Data, PrivateKey privateKey) {
        byte[] encryptedData = Base64Util.decodeBase64(base64Data);
        return decrypt(encryptedData, privateKey);
    }

    /**
     * 将公钥转换为Base64字符串
     * 
     * @param publicKey 公钥
     * @return Base64编码的公钥字符串
     */
    public static String publicKeyToBase64(PublicKey publicKey) {
        return Base64Util.encodeBase64(publicKey.getEncoded());
    }

    /**
     * 将私钥转换为Base64字符串
     * 
     * @param privateKey 私钥
     * @return Base64编码的私钥字符串
     */
    public static String privateKeyToBase64(PrivateKey privateKey) {
        return Base64Util.encodeBase64(privateKey.getEncoded());
    }

    /**
     * 从Base64字符串恢复公钥
     * 
     * @param base64PublicKey Base64编码的公钥字符串
     * @return 公钥对象
     */
    public static PublicKey base64ToPublicKey(String base64PublicKey) {
        try {
            byte[] keyBytes = Base64Util.decodeBase64(base64PublicKey);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
            return keyFactory.generatePublic(keySpec);
        } catch (Exception e) {
            throw new EncryptionException("从Base64字符串恢复公钥失败", e);
        }
    }

    /**
     * 从Base64字符串恢复私钥
     * 
     * @param base64PrivateKey Base64编码的私钥字符串
     * @return 私钥对象
     */
    public static PrivateKey base64ToPrivateKey(String base64PrivateKey) {
        try {
            byte[] keyBytes = Base64Util.decodeBase64(base64PrivateKey);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
            return keyFactory.generatePrivate(keySpec);
        } catch (Exception e) {
            throw new EncryptionException("从Base64字符串恢复私钥失败", e);
        }
    }


}
