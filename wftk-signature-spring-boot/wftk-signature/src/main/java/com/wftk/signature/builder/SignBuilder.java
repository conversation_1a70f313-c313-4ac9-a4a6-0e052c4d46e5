package com.wftk.signature.builder;

import java.util.Map;

/**
 * @Author: ying.dong
 * @Date: 2021/7/7 16:58
 */
public interface SignBuilder {

    /**
     * 添加参数
     * @param name
     * @param param
     * @return
     */
    SignBuilder addParam(String name, Object param);

    /**
     * 添加参数
     * @param params
     * @return
     */
    SignBuilder addParams(Map<String, Object> params);

    /**
     * 添加对象
     * @param object
     * @return
     */
    SignBuilder addObject(Object object);

    /**
     * 移除参数
     * @param name
     * @return
     */
    SignBuilder removeParam(String name);

    /**
     * 构造sign
     * @return
     */
    String build();
}
