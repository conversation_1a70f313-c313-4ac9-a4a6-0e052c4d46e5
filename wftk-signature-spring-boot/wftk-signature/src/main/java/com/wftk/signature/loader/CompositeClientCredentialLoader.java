package com.wftk.signature.loader;

import cn.hutool.core.collection.CollectionUtil;
import com.wftk.signature.builder.ClientInfo;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/10/17 18:47
 */
public class CompositeClientCredentialLoader implements ClientCredentialLoader {

    private final List<ClientCredentialLoader> clientCredentialLoaders;

    public CompositeClientCredentialLoader(List<ClientCredentialLoader> clientCredentialLoaders) {
        this.clientCredentialLoaders = new ArrayList<>(clientCredentialLoaders);
        this.clientCredentialLoaders.sort(Comparator.comparingInt(ClientCredentialLoader::getOrder));
    }

    @Override
    public ClientInfo get(String clientId) {
        if (CollectionUtil.isEmpty(clientCredentialLoaders)) {
            return null;
        }
        for (ClientCredentialLoader clientCredentialLoader : clientCredentialLoaders) {
            ClientInfo clientInfo = clientCredentialLoader.get(clientId);
            if (clientInfo == null) {
                continue;
            }
            return clientInfo;
        }
        return null;
    }
}
