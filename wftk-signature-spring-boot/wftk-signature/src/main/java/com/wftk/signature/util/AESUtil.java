package com.wftk.signature.util;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import com.wftk.signature.exception.EncryptionException;

import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;

/**
 * AES加密工具类
 * 使用AES-256-CBC模式进行加密
 * 
 */
public class AESUtil {

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";
    private static final int KEY_LENGTH = 256;
    private static final int IV_LENGTH = 16;

    /**
     * 生成随机AES密钥
     * 
     * @return AES密钥字节数组
     */
    public static byte[] generateKey() {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
            keyGenerator.init(KEY_LENGTH);
            SecretKey secretKey = keyGenerator.generateKey();
            return secretKey.getEncoded();
        } catch (Exception e) {
            throw new EncryptionException("生成AES密钥失败", e);
        }
    }

    /**
     * 生成随机IV
     * 
     * @return IV字节数组
     */
    public static byte[] generateIV() {
        byte[] iv = new byte[IV_LENGTH];
        new SecureRandom().nextBytes(iv);
        return iv;
    }

    /**
     * AES加密
     * 
     * @param data 待加密的数据
     * @param key AES密钥
     * @param iv 初始向量
     * @return 加密后的字节数组
     */
    public static byte[] encrypt(String data, byte[] key, byte[] iv) {
        try {
            SecretKeySpec secretKeySpec = new SecretKeySpec(key, ALGORITHM);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
            
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);
            
            return cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            throw new EncryptionException("AES加密失败", e);
        }
    }

    /**
     * AES解密
     * 
     * @param encryptedData 加密的数据
     * @param key AES密钥
     * @param iv 初始向量
     * @return 解密后的字符串
     */
    public static String decrypt(byte[] encryptedData, byte[] key, byte[] iv) {
        try {
            SecretKeySpec secretKeySpec = new SecretKeySpec(key, ALGORITHM);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
            
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);
            
            byte[] decryptedData = cipher.doFinal(encryptedData);
            return new String(decryptedData, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new EncryptionException("AES解密失败", e);
        }
    }

    /**
     * AES加密并返回Base64编码的字符串
     * 
     * @param data 待加密的数据
     * @param key AES密钥
     * @param iv 初始向量
     * @return Base64编码的加密字符串
     */
    public static String encryptToBase64(String data, byte[] key, byte[] iv) {
        byte[] encryptedData = encrypt(data, key, iv);
        return Base64Util.encodeBase64(encryptedData);
    }

    /**
     * 解密Base64编码的AES加密数据
     * 
     * @param base64Data Base64编码的加密数据
     * @param key AES密钥
     * @param iv 初始向量
     * @return 解密后的字符串
     */
    public static String decryptFromBase64(String base64Data, byte[] key, byte[] iv) {
        byte[] encryptedData = Base64Util.decodeBase64(base64Data);
        return decrypt(encryptedData, key, iv);
    }
}
