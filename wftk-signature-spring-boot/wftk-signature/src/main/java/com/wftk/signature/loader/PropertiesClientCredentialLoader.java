package com.wftk.signature.loader;

import cn.hutool.core.collection.CollUtil;
import com.wftk.signature.builder.ClientInfo;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @create 2023/10/17 18:29
 */
public class PropertiesClientCredentialLoader implements ClientCredentialLoader {

    private final Map<String, ClientInfo> signClientMap = new ConcurrentHashMap<>();

    public PropertiesClientCredentialLoader(List<ClientInfo> clients) {
        if (CollUtil.isNotEmpty(clients)) {
            clients.forEach(c -> signClientMap.put(c.getClientId(), c));
        }
    }

    @Override
    public ClientInfo get(String clientId) {
        return signClientMap.get(clientId);
    }

}
