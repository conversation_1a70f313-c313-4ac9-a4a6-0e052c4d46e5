package com.wftk.signature.builder;

import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Set;

import com.wftk.signature.enums.SignAlgorithmEnum;
import com.wftk.signature.util.RSAUtil;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/4/13 17:28
 */
@Data
public class ClientInfo {

    private String clientId;
    private String clientSecret;

    /**
     * 默认签名算法
     */
    private String defaultAlgorithm = SignAlgorithmEnum.MD5.getValue();

    /**
     * 支持多租户
     */
    private String tenantId;

    /**
     * ip白名单
     */
    private Set<String> ips;

    /**
     * 加密信息
     */
    private EncryptionInfo encryptionInfo;



    /**
     * 加密信息
     */
    @Data
    public static class EncryptionInfo {
        /**
         * 自身的公钥和私钥
         */
        private PublicKey publicKey;
        private PrivateKey privateKey;

        /**
         * 目标对象的公钥
         */
        private PublicKey targetPublicKey;


        public void setPublicKey(String publicKey) {
            this.publicKey = RSAUtil.base64ToPublicKey(publicKey);
        }

        public void setPrivateKey(String privateKey) {
            this.privateKey = RSAUtil.base64ToPrivateKey(privateKey);
        }

        public void setTargetPublicKey(String targetPublicKey) {
            this.targetPublicKey = RSAUtil.base64ToPublicKey(targetPublicKey);
        }

        public PublicKey getPublicKey() {
            return publicKey;
        }

        public PrivateKey getPrivateKey() {
            return privateKey;
        }

        public PublicKey getTargetPublicKey() {
            return targetPublicKey;
        }

    }
}
