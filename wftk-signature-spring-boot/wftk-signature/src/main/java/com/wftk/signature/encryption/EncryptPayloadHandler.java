package com.wftk.signature.encryption;

import java.security.PublicKey;
import java.util.Map;

public interface EncryptPayloadHandler {

    /**
     * 加密请求体
     * @param body
     * @param publicKey
     * @return
     */
    EncryptedPayload encrypt(String body, PublicKey publicKey);

    /**
     * 加密请求体
     * @param clientId
     * @param body
     * @return
     */
    EncryptedPayload encryptRequest(String clientId, String body);


    /**
     * 加密请求体并签名
     * @param clientId
     * @param body
     * @param urlParamsMap
     * @return
     */
    EncryptedPayload encryptAndSignRequest(String clientId, String body, Map<String, String> urlParamsMap);


    /**
     * 加密响应体
     * @param clientId
     * @param body
     * @return
     */
    EncryptedPayload encryptResponse(String clientId, String body);


    /**
     * 加密响应体并签名
     * @param clientId
     * @param body
     * @return
     */
    EncryptedPayload encryptAndSignResponse(String clientId, String body);


    /**
     * 解密请求体
     * @param clientId
     * @param encryptedRequestBody
     * @return
     */
    String decrypt(String clientId, EncryptedPayload encryptedRequestBody);

}
