package com.wftk.signature.encryption;

/**
 * 加密请求数据传输对象
 * 
 */
public class EncryptedPayload {

    /**
     * 使用RSA公钥加密后的AES密钥（Base64编码）
     */
    private String encryptedKey;

    /**
     * 使用AES密钥加密后的业务数据（Base64编码）
     */
    private String payload;

    /**
     * AES加密使用的初始向量（Base64编码）
     */
    private String iv;

    public EncryptedPayload() {
    }

    public EncryptedPayload(String encryptedKey, String payload, String iv) {
        this.encryptedKey = encryptedKey;
        this.payload = payload;
        this.iv = iv;
    }

    public String getEncryptedKey() {
        return encryptedKey;
    }

    public void setEncryptedKey(String encryptedKey) {
        this.encryptedKey = encryptedKey;
    }

    public String getPayload() {
        return payload;
    }

    public void setPayload(String payload) {
        this.payload = payload;
    }

    public String getIv() {
        return iv;
    }

    public void setIv(String iv) {
        this.iv = iv;
    }

    @Override
    public String toString() {
        return "EncryptedRequestBody{" +
                "encryptedKey='" + encryptedKey + '\'' +
                ", payload='" + payload + '\'' +
                ", iv='" + iv + '\'' +
                '}';
    }
}
