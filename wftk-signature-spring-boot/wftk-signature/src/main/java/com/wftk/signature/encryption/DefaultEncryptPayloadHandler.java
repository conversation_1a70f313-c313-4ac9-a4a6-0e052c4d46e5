package com.wftk.signature.encryption;

import java.security.PublicKey;
import java.util.Map;

import com.wftk.jackson.core.JSONObject;
import com.wftk.signature.builder.ClientInfo;
import com.wftk.signature.builder.ClientInfo.EncryptionInfo;
import com.wftk.signature.builder.SignBuilder;
import com.wftk.signature.builder.factory.SignBuilderFactory;
import com.wftk.signature.exception.EncryptionException;
import com.wftk.signature.loader.ClientCredentialLoader;
import com.wftk.signature.util.AESUtil;
import com.wftk.signature.util.Base64Util;
import com.wftk.signature.util.RSAUtil;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 加密请求体处理器
 */
@Slf4j
public class DefaultEncryptPayloadHandler implements EncryptPayloadHandler {

    private final ClientCredentialLoader clientCredentialLoader;
    private final SignBuilderFactory<?> signBuilderFactory;

    public DefaultEncryptPayloadHandler(ClientCredentialLoader clientCredentialLoader, SignBuilderFactory<?> signBuilderFactory) {
        this.clientCredentialLoader = clientCredentialLoader;
        this.signBuilderFactory = signBuilderFactory;
    }

    /**
     * 解密请求体
     * @param clientId
     * @param encryptedRequestBody
     * @return
     */
    @Override
    public String decrypt(String clientId, EncryptedPayload encryptedRequestBody) {
        try {
            log.info("before decrypt: {}", encryptedRequestBody);
            // 1. 解密AES密钥
            ClientInfo clientInfo = clientCredentialLoader.get(clientId);
            if (clientInfo == null) {
                throw new IllegalArgumentException("clientInfo must not be null");
            }
            EncryptionInfo encryptionInfo = clientInfo.getEncryptionInfo();
            if (encryptionInfo == null) {
                throw new IllegalArgumentException("encryptionInfo must not be null");
            }
            byte[] aesKey = RSAUtil.decryptFromBase64(encryptedRequestBody.getEncryptedKey(), encryptionInfo.getPrivateKey());

            // 2. Base64解码IV
            byte[] iv = Base64Util.decodeBase64(encryptedRequestBody.getIv());

            // 3. AES解密业务数据
            String decryptedData = AESUtil.decryptFromBase64(encryptedRequestBody.getPayload(), aesKey, iv);

            log.info("after decrypt: {}", decryptedData);

            return decryptedData;
        } catch (Exception e) {
            throw new EncryptionException("解密请求失败", e);
        }
    }

    @Override
    public EncryptedPayload encrypt(String body, PublicKey publicKey) {
        try {
            log.info("before encrypt: {}", body);
            // 1. 生成AES密钥和IV
            byte[] aesKey = AESUtil.generateKey();
            byte[] iv = AESUtil.generateIV();

            // 2. AES加密业务数据
            String encryptedData = AESUtil.encryptToBase64(body, aesKey, iv);

            // 3. RSA加密AES密钥
            String encryptedKey = RSAUtil.encryptToBase64(aesKey, publicKey);

            // 4. Base64编码IV
            String ivBase64 = Base64Util.encodeBase64(iv);

            log.info("after encrypt: [{}], encryptedKey: [{}], ivBase64: [{}]", encryptedData, encryptedKey, ivBase64);

            // 5. 构建加密请求对象
            return new EncryptedPayload(encryptedKey, encryptedData, ivBase64);
        } catch (Exception e) {
            throw new EncryptionException("加密请求失败", e);
        }
    }

    @Override
    public EncryptedPayload encryptRequest(String clientId, String body) {
        return encrypt(clientId, body, true);
    }

    @Override
    public EncryptedPayload encryptResponse(String clientId, String body) {
        return encrypt(clientId, body, false);
    }


    /**
     * 加密请求体
     * @param clientId
     * @param body
     * @param isRequest
     * @return
     */
    private EncryptedPayload encrypt(String clientId, String body, boolean isRequest) {
        if (StrUtil.isBlank(clientId)) {
            throw new IllegalArgumentException("clientId must not be blank");
        }
        ClientInfo clientInfo = clientCredentialLoader.get(clientId);
        if (clientInfo == null) {
            throw new IllegalArgumentException("clientInfo must not be null");
        }
        EncryptionInfo encryptionInfo = clientInfo.getEncryptionInfo();
        if (encryptionInfo == null) {
            throw new IllegalArgumentException("encryptionInfo must not be null");
        }
        PublicKey publicKey = isRequest ? encryptionInfo.getPublicKey() : encryptionInfo.getTargetPublicKey();
        return encrypt(body, publicKey);
    }

    @Override
    public EncryptedPayload encryptAndSignResponse(String clientId, String body) {
        EncryptedPayload encryptedPayload = encryptResponse(clientId, body);
        SignBuilder signBuilder = signBuilderFactory.get(clientId);
        signBuilder.addParam("body", JSONObject.getInstance().toJSONString(encryptedPayload));
        String signature = signBuilder.build();
        return new SignedEncryptedPayload(encryptedPayload, signature);
    }


    @Override
    public EncryptedPayload encryptAndSignRequest(String clientId, String body, Map<String, String> urlParamsMap) {
        EncryptedPayload encryptRequest = encryptRequest(clientId, body);
        SignBuilder signBuilder = signBuilderFactory.get(clientId);
        if (MapUtil.isNotEmpty(urlParamsMap)) {
            urlParamsMap.forEach(signBuilder::addParam);
        }
        signBuilder.addParam("body", JSONObject.getInstance().toJSONString(encryptRequest));
        String signature = signBuilder.build();
        return new SignedEncryptedPayload(encryptRequest, signature);
    }

}
