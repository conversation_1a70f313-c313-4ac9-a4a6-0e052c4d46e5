package com.wftk.signature.config;

/**
 * <AUTHOR>
 * @create 2023/10/30 14:24
 */
public class NonceConfig {


    /**
     * 是否启用nonce
     */
    private boolean enable = false;

    /**
     * nonce参数名
     */
    private String name = "nonce";

    /**
     * 默认过期时间
     */
    private Long expireInMills = 8000L;

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getExpireInMills() {
        return expireInMills;
    }

    public void setExpireInMills(Long expireInMills) {
        this.expireInMills = expireInMills;
    }
}
