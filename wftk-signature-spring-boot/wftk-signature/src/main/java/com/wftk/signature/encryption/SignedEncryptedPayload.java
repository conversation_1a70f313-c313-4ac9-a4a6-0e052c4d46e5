package com.wftk.signature.encryption;

/**
 * 加密请求体
 */
public class SignedEncryptedPayload extends EncryptedPayload {

    private String sign;

    public SignedEncryptedPayload() {
    }

    public SignedEncryptedPayload(EncryptedPayload encryptedPayload, String sign) {
        super(encryptedPayload.getEncryptedKey(), encryptedPayload.getPayload(), encryptedPayload.getIv());
        this.sign = sign;
    }

    public SignedEncryptedPayload(String encryptedKey, String payload, String iv, String sign) {
        super(encryptedKey, payload, iv);
        this.sign = sign;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

}
