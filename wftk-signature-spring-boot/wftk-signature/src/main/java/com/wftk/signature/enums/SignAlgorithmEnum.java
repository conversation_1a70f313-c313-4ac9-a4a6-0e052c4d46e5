package com.wftk.signature.enums;

/**
 * 签名算法
 */
public enum SignAlgorithmEnum {

    MD5("MD5"), 
	SHA1("SHA-1"), 
	SHA256("SHA-256");

    private final String value;

    SignAlgorithmEnum(String value) {
        this.value = value;
    }

    public static SignAlgorithmEnum fromValue(String value) {
        for (SignAlgorithmEnum algorithm : SignAlgorithmEnum.values()) {
            if (algorithm.getValue().equalsIgnoreCase(value)) {
                return algorithm;
            }
        }
        throw new IllegalArgumentException("invalid algorithm: " + value);
    }

    public String getValue() {
        return value;
    }
}
