package com.wftk.signature.result;


/**
 * @Author: ying.dong
 * @Date: 2021/7/6 15:52
 */
public class DefaultSignValidateResult extends SignValidateResult {

    public static SignValidateResult SUCCESS = new DefaultSignValidateResult(SignStatus.PASS);

    public DefaultSignValidateResult(SignStatus status) {
        super(status);
    }

    public DefaultSignValidateResult(SignStatus status, String result) {
        super(status, result);
    }

    public static SignValidateResult success() {
        return new DefaultSignValidateResult(SignStatus.PASS);
    }

    public static SignValidateResult fail(String message) {
        return new DefaultSignValidateResult(SignStatus.FAIL, message);
    }

    public static SignValidateResult fail() {
        return new DefaultSignValidateResult(SignStatus.FAIL);
    }

}
