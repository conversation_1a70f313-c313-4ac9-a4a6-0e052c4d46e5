package com.wftk.signature.result;


import com.wftk.signature.builder.ClientInfo;

/**
 * @Author: ying.dong
 * @Date: 2021/7/6 15:46
 */
public abstract class SignValidateResult {

    private final Integer code;
    private String message;

    private ClientInfo clientInfo;

    public SignValidateResult(SignStatus status) {
        this.code = status.getStatus();
    }

    public SignValidateResult(SignStatus status, String message) {
        this.code = status.getStatus();
        this.message = message;
    }

    public boolean isValid() {
        return code.equals(SignStatus.PASS.getStatus());
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public SignValidateResult clientInfo(ClientInfo clientInfo) {
        this.clientInfo = clientInfo;
        return this;
    }

    public ClientInfo getClientInfo() {
        return clientInfo;
    }


    public void setMessage(String message) {
        this.message = message;
    }

    public enum SignStatus {
        PASS(200), FAIL(403);

        private final Integer status;

        SignStatus(Integer status) {
            this.status = status;
        }

        public Integer getStatus() {
            return status;
        }
    }
}
