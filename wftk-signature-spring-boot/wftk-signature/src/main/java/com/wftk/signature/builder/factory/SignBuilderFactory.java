package com.wftk.signature.builder.factory;


import com.wftk.signature.builder.SignBuilder;

/**
 * <AUTHOR>
 * @create 2023/4/4 14:21
 */
public interface SignBuilderFactory<T extends SignBuilder> {

    /**
     *
     * @param clientId
     * @return
     */
    default T get(String clientId) {
        return get(clientId, null);
    }

    /**
     * 
     * @param clientId
     * @param algorithm
     * @return
     */ 
    T get(String clientId, String algorithm);
}
