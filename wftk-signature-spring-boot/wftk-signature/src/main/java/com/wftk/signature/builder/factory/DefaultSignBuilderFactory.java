package com.wftk.signature.builder.factory;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.signature.builder.ClientInfo;
import com.wftk.signature.builder.DefaultSignBuilder;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/4/4 14:22
 */
public class DefaultSignBuilderFactory implements SignBuilderFactory<DefaultSignBuilder> {

    private final Map<String, ClientInfo> clientInfoMap;

    public DefaultSignBuilderFactory(List<ClientInfo> clients) {
        clientInfoMap = initialClientInfo(clients);
    }


    /**
     *
     * @param clients
     * @return
     */
    private Map<String, ClientInfo> initialClientInfo(List<ClientInfo> clients) {
        if (CollUtil.isEmpty(clients)) {
            return Collections.emptyMap();
        }
        Map<String, ClientInfo> clientInfo = new HashMap<>();
        for (ClientInfo client : clients) {
            clientInfo.put(client.getClientId(), client);
        }
        return clientInfo;
    }

    @Override
    public DefaultSignBuilder get(String clientId, String algorithm) {
        if (StrUtil.isBlank(clientId) || !clientInfoMap.containsKey(clientId)) {
            throw new RuntimeException("invalid clientId: " + clientId);
        }
        ClientInfo clientInfo = clientInfoMap.get(clientId);
        if (StrUtil.isBlank(algorithm)) {
            algorithm = clientInfo.getDefaultAlgorithm();
        }
        return new DefaultSignBuilder(clientInfo.getClientSecret(), algorithm);
    }
}
