package com.wftk.pageable.spring.boot.autoconfigure.core;

import java.util.Optional;

/**
 * @Author: ying.dong
 * @Date: 2021/5/6 14:52
 */
public class PageContext {

    private static final ThreadLocal<PageInfo> context = new ThreadLocal<>();

    public static boolean setPageInfo(Integer pageNum, Integer pageSize) {
        PageInfo pageInfo = new PageInfo(pageNum, pageSize);
        context.set(pageInfo);
        return true;
    }

    public static Optional<Integer> getPageNum() {
        return Optional.ofNullable(context.get())
                .map(PageInfo::getPageNum);
    }

    public static Optional<Integer> getPageSize() {
        return Optional.ofNullable(context.get())
                .map(PageInfo::getPageSize);
    }

    public static boolean clear() {
        context.remove();
        return true;
    }

    private static class PageInfo {
        private Integer pageNum;
        private Integer pageSize;

        PageInfo(Integer pageNum, Integer pageSize) {
            this.pageNum = pageNum;
            this.pageSize = pageSize;
        }

        public Integer getPageNum() {
            return pageNum;
        }

        public void setPageNum(Integer pageNum) {
            this.pageNum = pageNum;
        }

        public Integer getPageSize() {
            return pageSize;
        }

        public void setPageSize(Integer pageSize) {
            this.pageSize = pageSize;
        }
    }

}
