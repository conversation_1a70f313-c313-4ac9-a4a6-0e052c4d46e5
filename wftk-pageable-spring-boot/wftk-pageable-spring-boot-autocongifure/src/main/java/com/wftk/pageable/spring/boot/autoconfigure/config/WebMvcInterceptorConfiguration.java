package com.wftk.pageable.spring.boot.autoconfigure.config;

import com.wftk.pageable.spring.boot.autoconfigure.properties.PageProperties;
import com.wftk.pageable.spring.boot.autoconfigure.core.PageableInterceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.DispatcherServlet;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * @Author: ying.dong
 * @Date: 2021/5/6 15:43
 */
@Configuration
@ConditionalOnClass(value = {WebMvcConfigurer.class, DispatcherServlet.class, SqlSessionFactory.class})
public class WebMvcInterceptorConfiguration {

    @Configuration
    @AutoConfigureAfter(DispatcherServletAutoConfiguration.class)
    @ConditionalOnBean(SqlSessionFactory.class)
    @EnableConfigurationProperties(PageProperties.class)
    static class PageableInterceptorConfiguration implements WebMvcConfigurer {

        @Autowired
        private PageProperties pageProperties;

        @ConditionalOnMissingBean
        @Bean
        PageableInterceptor pageableInterceptor() {
            return new PageableInterceptor(pageProperties);
        }

        @Override
        public void addInterceptors(InterceptorRegistry registry) {
            registry.addInterceptor(pageableInterceptor());
        }
    }
}
