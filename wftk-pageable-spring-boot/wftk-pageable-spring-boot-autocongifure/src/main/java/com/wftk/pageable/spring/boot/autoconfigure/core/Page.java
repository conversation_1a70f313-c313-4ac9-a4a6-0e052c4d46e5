package com.wftk.pageable.spring.boot.autoconfigure.core;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: ying.dong
 * @Date: 2021/5/6 14:50
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Slf4j
public class Page<E> extends PageInfo<E> {

    @Serial
    private static final long serialVersionUID = -66639682854755053L;

    public Page(@NonNull List<E> list) {
        super(list);
    }


    public Page() {
        super(new ArrayList<>());
    }

    /**
     * 分页
     * @param iSelect
     * @return
     * @param <E>
     */
    public static<E> Page<E> doSelectPage(ISelect<E> iSelect) {
        return doSelectPage(null, null, iSelect);
    }

    /**
     * 指定分页参数
     * @param pageNum
     * @param pageSize
     * @param iSelect
     * @return
     * @param <E>
     */
    public static <E> Page<E> doSelectPage(Integer pageNum, Integer pageSize, ISelect<E> iSelect) {
        pageNum = pageNum != null ? pageNum : PageContext.getPageNum().orElse(null);
        pageSize = pageSize != null ? pageSize : PageContext.getPageSize().orElse(null);
        if (pageNum != null && pageSize != null) {
            log.debug("do select page, pageNum:{}, pageSize:{}", pageNum, pageSize);
            PageHelper.startPage(pageNum, pageSize);
        }
        return new Page<>(iSelect.doSelect());
    }

    /**
     *
     * @param pageNum
     * @param pageSize
     * @param autoResetPageInfo
     * @param iSelect
     * @return
     * @param <E>
     */
    public static<E> Page<E> doSelectPage(Integer pageNum, Integer pageSize, boolean autoResetPageInfo, ISelect<E> iSelect) {
        try {
            return doSelectPage(pageNum, pageSize, iSelect);
        } finally {
            if (autoResetPageInfo) {
                PageContext.clear();
            }
        }
    }

    /**
     *
     * @param iSelect
     * @param autoResetPageInfo
     * @param <E>
     * @return
     */
    public static<E> Page<E> doSelectPage(ISelect<E> iSelect, boolean autoResetPageInfo) {
        return doSelectPage(null , null, autoResetPageInfo, iSelect);
    }

    /**
     * 类型转换
     * @param function
     * @param <R>
     * @return
     */
    public <R> Page<R> toPage(ListFunction<E, R> function) {
        com.github.pagehelper.Page<R> page = new com.github.pagehelper.Page<>(getPageNum(), getPageSize());
        page.setTotal(getTotal());
        page.addAll(function.apply(getList()));
        return new Page<>(page);
    }

    /**
     * 是否为空
     * @return
     */
    public boolean isEmpty() {
        return !(getList() != null && !getList().isEmpty());
    }



    @FunctionalInterface
    public interface ListFunction<E, R> {

        List<R> apply(List<E> list);
    }
}
