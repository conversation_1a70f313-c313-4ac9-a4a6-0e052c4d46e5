package com.wftk.pageable.spring.boot.autoconfigure.core;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.pageable.spring.boot.autoconfigure.properties.PageProperties;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;


/**
 * @Author: ying.dong
 * @Date: 2021/5/6 14:58
 */
@Slf4j
public class PageableInterceptor implements HandlerInterceptor {

    private final PageProperties properties;

    public PageableInterceptor(PageProperties properties) {
        this.properties = properties;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String pageNum = request.getParameter(properties.getPageNumName());
        String pageSize = request.getParameter(properties.getPageSizeName());
        if (checkPageParam(pageNum) && checkPageParam(pageSize)) {
            int size = Integer.parseInt(pageSize);
            if (!properties.isAllowPageSizeOverFlow() && properties.getMaxPageSize() < size) {
                throw new IllegalArgumentException("page size exceeded. expected max [" + properties.getMaxPageSize() + "], but [" + size + "].");
            }
            log.debug("find page params in request parameters, pageNum: {}, pageSize: {}", pageNum, pageSize);
            PageContext.setPageInfo(Integer.parseInt(pageNum), Integer.parseInt(pageSize));
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        PageContext.clear();
    }

    /**
     *
     * @param param
     * @return
     */
    private boolean checkPageParam(String param) {
        if (StrUtil.isBlank(param)) {
            return false;
        }
        return NumberUtil.isNumber(param);
    }
}
