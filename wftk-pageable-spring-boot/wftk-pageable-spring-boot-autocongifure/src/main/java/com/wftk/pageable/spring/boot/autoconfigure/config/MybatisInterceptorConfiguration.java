package com.wftk.pageable.spring.boot.autoconfigure.config;

import com.github.pagehelper.PageInterceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: ying.dong
 * @Date: 2021/5/7 13:46
 */
@Configuration
@ConditionalOnClass({SqlSessionFactory.class})
public class MybatisInterceptorConfiguration {

    @Bean
    @ConditionalOnMissingBean
    PageInterceptor pageInterceptor() {
        return new PageInterceptor();
    }

}
