package com.wftk.pageable.spring.boot.autoconfigure.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @Author: ying.dong
 * @Date: 2021/5/6 15:28
 */
@Data
@ConfigurationProperties(prefix = PageProperties.PREFIX)
public class PageProperties {

    public static final String PREFIX = "config.pageable";

    /**
     * 分页名称（外部可覆盖）
     */
    private String pageNumName = "pageNum";

    /**
     * 每页条目（外部可覆盖）
     */
    private String pageSizeName = "pageSize";

    /**
     * 每页最大允许条目
     */
    private Integer maxPageSize = 20;

    /**
     * 是否允许超过最大条目
     * true: 允许
     * false: 不允许
     */
    private boolean allowPageSizeOverFlow;
}
